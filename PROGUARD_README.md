# ProGuard Code Obfuscation for Windchill Complaints Microservice

This document explains how to use ProGuard code obfuscation in the Windchill Complaints microservice project.

## Overview

ProGuard is a code obfuscator that:
- **Obfuscates** class names, method names, and field names to make reverse engineering difficult
- **Optimizes** bytecode by removing unused code and inlining methods
- **Shrinks** the final JAR size by removing unused classes and methods
- **Protects** intellectual property and sensitive business logic

## Configuration Files

### 1. `proguard.conf`
The main ProGuard configuration file containing:
- Rules to preserve Spring Boot functionality
- Keep rules for annotations, REST endpoints, and configuration classes
- Obfuscation settings for business logic
- Optimization parameters

### 2. `pom.xml` Updates
- Added ProGuard Maven plugin in a separate profile
- Configured to run only when explicitly requested
- Set up with proper Java module dependencies

### 3. `application.properties` Updates
- Disabled Spring Boot DevTools (incompatible with obfuscation)
- Disabled unnecessary features for production builds

## How to Build

### Regular Build (No Obfuscation)
```bash
mvn clean package
```
This creates: `target/windchill.complaints-0.0.1-SNAPSHOT.jar`

### Obfuscated Build
```bash
mvn clean package -Pobfuscate
```
This creates both:
- `target/windchill.complaints-0.0.1-SNAPSHOT.jar` (original)
- `target/windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar` (obfuscated)

### Alternative Obfuscated Build
```bash
mvn clean package -Dobfuscate=true
```

## What Gets Obfuscated

### ✅ **Preserved (Not Obfuscated)**
- Main application class
- Spring Boot annotations (@SpringBootApplication, @Configuration, etc.)
- REST API endpoints and their methods
- Spring components (@Service, @Controller, @Repository)
- Jackson serialization annotations
- Lombok annotations
- Request/Response DTOs
- Exception classes
- Configuration properties
- Swagger/OpenAPI annotations

### 🔒 **Obfuscated**
- Private methods and fields
- Internal business logic
- Local variables
- Non-public utility methods
- Internal class names (moved to 'obfuscated' package)

## Output Files

After running obfuscated build, you'll find these files in the `target/` directory:

1. **`windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar`** - The obfuscated JAR file
2. **`mapping.txt`** - Maps original names to obfuscated names (keep this secure!)
3. **`seeds.txt`** - Lists all classes/methods that were kept
4. **`usage.txt`** - Shows what was removed during optimization

## Running the Obfuscated Application

The obfuscated JAR runs exactly like the original:

```bash
java -jar target/windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar
```

All functionality remains intact:
- REST API endpoints work normally
- Spring Boot auto-configuration works
- Database connections and external services work
- Swagger UI remains accessible

## Security Considerations

### 🔐 **Keep Secure**
- **`mapping.txt`** - Contains the mapping between original and obfuscated names
- **`proguard.conf`** - Reveals what was preserved vs obfuscated
- Original source code and non-obfuscated JARs

### 📋 **Best Practices**
1. **Use obfuscated builds only for production deployment**
2. **Keep mapping files secure** - needed for debugging production issues
3. **Test thoroughly** - ensure all functionality works after obfuscation
4. **Version control** - keep mapping files with corresponding releases
5. **CI/CD Integration** - automate obfuscated builds in deployment pipeline

## Troubleshooting

### Common Issues

1. **ClassNotFoundException at runtime**
   - Add keep rules in `proguard.conf` for the missing class
   - Check if the class uses reflection

2. **Spring Boot auto-configuration fails**
   - Ensure all Spring annotations are preserved
   - Check if custom configuration classes need keep rules

3. **JSON serialization/deserialization errors**
   - Verify Jackson annotations are preserved
   - Add keep rules for DTO classes if needed

4. **Method not found errors**
   - Add keep rules for methods accessed via reflection
   - Check if interfaces need to be preserved

### Debugging

1. **Check `seeds.txt`** to see what was preserved
2. **Review `usage.txt`** to see what was removed
3. **Use `mapping.txt`** to map obfuscated names back to originals
4. **Add `-verbose` option** to ProGuard for detailed logging

## Customization

### Adding Keep Rules

To preserve additional classes or methods, add rules to `proguard.conf`:

```proguard
# Keep specific class
-keep class com.example.MyClass {
    *;
}

# Keep specific method
-keepclassmembers class * {
    public void myMethod();
}

# Keep classes with specific annotation
-keep @com.example.MyAnnotation class * {
    *;
}
```

### Adjusting Obfuscation Level

Modify these settings in `proguard.conf`:

```proguard
# More aggressive obfuscation
-overloadaggressively
-allowaccessmodification

# Less aggressive (preserve more names)
-keeppackagenames
-keepattributes SourceFile,LineNumberTable
```

## Performance Impact

- **Build time**: Increases by 30-60 seconds
- **Runtime performance**: Slightly improved due to optimization
- **JAR size**: Typically 10-20% smaller due to unused code removal
- **Memory usage**: Slightly reduced due to optimizations

## Integration with CI/CD

Add to your deployment pipeline:

```yaml
# Example GitHub Actions
- name: Build Obfuscated JAR
  run: mvn clean package -Pobfuscate

- name: Deploy Obfuscated Application
  run: |
    cp target/*-obfuscated.jar deployment/
    # Deploy obfuscated JAR
```

## Support

For issues with ProGuard obfuscation:
1. Check the troubleshooting section above
2. Review ProGuard documentation: https://www.guardsquare.com/proguard
3. Examine the generated log files in the `target/` directory
