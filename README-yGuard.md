# yGuard Obfuscation for Windchill Complaints Microservice

This document explains how to use yGuard for code obfuscation in the Windchill Complaints Spring Boot microservice.

## Overview

yGuard is an open-source Java obfuscation tool that helps protect your code by renaming classes, methods, and fields while preserving functionality. This configuration is specifically tailored for Spring Boot applications.

## Prerequisites

- Java 17+
- Maven 3.6+
- yGuard 4.1.1 (automatically downloaded via Maven)

## Quick Start

### 1. Build with Obfuscation (Maven)

```bash
# Build the project and create obfuscated JAR
mvn clean package

# The obfuscation runs automatically during the package phase
```

This creates two JAR files:
- `target/windchill.complaints-0.0.1-SNAPSHOT.jar` (original)
- `target/windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar` (obfuscated)

### 2. Build with Standalone yGuard Configuration

```bash
# Using the standalone yGuard configuration file
ant -f yguard-config.xml obfuscate
```

## Configuration Files

### 1. Maven Integration (pom.xml)
The Maven AntRun plugin is configured to run yGuard during the package phase. The configuration includes:
- yGuard dependency
- AntRun plugin with yGuard task definition
- Comprehensive obfuscation rules for Spring Boot

### 2. Standalone Configuration (yguard-config.xml)
A standalone Ant build file that can be used independently of Maven for more control over the obfuscation process.

## What Gets Obfuscated

### ✅ **Preserved (Not Obfuscated)**
- Main application class (`WindchillComplaintsApplication`)
- Spring Boot annotations (`@SpringBootApplication`, `@Configuration`, etc.)
- REST API endpoints and their public methods
- Spring components (`@Service`, `@Controller`, `@Repository`)
- Jackson serialization annotations
- Lombok annotations and generated methods
- Request/Response DTOs
- Exception classes
- Configuration properties classes
- Swagger/OpenAPI annotations
- Camel route classes
- Public interfaces and their methods

### 🔒 **Obfuscated**
- Private methods and fields in utility classes (`utils` package)
- Private implementation details in service implementation classes
- Internal helper methods
- Private constants and variables
- Non-public inner classes

## Running the Obfuscated Application

```bash
# Run the obfuscated JAR
java -jar target/windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar

# Or with Spring profiles
java -jar target/windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar --spring.profiles.active=prod
```

## Verification

### 1. Check Obfuscation Log
After obfuscation, check the log file:
```bash
# Maven build log
cat target/yguard-log.xml

# Standalone build log
cat target/yguard-obfuscation.xml
```

### 2. Compare JAR Contents
```bash
# List classes in original JAR
jar -tf target/windchill.complaints-0.0.1-SNAPSHOT.jar | grep "com/itcinfotech"

# List classes in obfuscated JAR
jar -tf target/windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar | grep "com/itcinfotech"
```

### 3. Test Application Functionality
```bash
# Start the obfuscated application
java -jar target/windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar

# Test API endpoints
curl -X GET http://localhost:8080/complaints/csrf-token
curl -X POST http://localhost:8080/complaints -H "Content-Type: application/json" -d '{...}'
```

## Troubleshooting

### Common Issues

1. **ClassNotFoundException after obfuscation**
   - Check if the class is properly preserved in the `<keep>` rules
   - Verify Spring annotations are correctly handled

2. **JSON Serialization Issues**
   - Ensure DTOs are in the preserved packages (`dto`, `request`, `response`)
   - Check Jackson annotations are preserved

3. **Spring Boot Auto-configuration Problems**
   - Verify `@Configuration` classes are preserved
   - Check `@Bean` methods are not obfuscated

### Debug Mode

Enable verbose logging by adding to the yGuard configuration:
```xml
<rename logfile="target/yguard-debug.xml" 
        replaceClassNameStrings="true" 
        conserveManifest="true"
        verbose="true">
```

## Advanced Configuration

### Custom Obfuscation Rules

To add custom obfuscation rules, modify the `yguard-config.xml` file:

```xml
<!-- Example: Keep specific utility class -->
<keep>
    <class classes="public" methods="public" fields="public">
        <patternset>
            <include name="com.itcinfotech.windchill.complaints.utils.SpecificUtil"/>
        </patternset>
    </class>
</keep>

<!-- Example: Obfuscate specific package -->
<adjust replaceContent="true">
    <class classes="private,protected" methods="private,protected" fields="private,protected">
        <patternset>
            <include name="com.itcinfotech.windchill.complaints.internal.**"/>
        </patternset>
    </class>
</adjust>
```

### Integration with CI/CD

Add to your CI/CD pipeline:
```yaml
# Example GitHub Actions
- name: Build and Obfuscate
  run: |
    mvn clean package
    # Upload obfuscated JAR
    mv target/windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar ./app-obfuscated.jar
```

## Security Considerations

1. **Keep obfuscation logs secure** - They contain the mapping between original and obfuscated names
2. **Test thoroughly** - Ensure all functionality works after obfuscation
3. **Version control** - Don't commit obfuscated JARs to version control
4. **Backup original JARs** - Keep original JARs for debugging purposes

## Performance Impact

- **Build time**: Adds ~30-60 seconds to build process
- **Runtime performance**: Minimal impact (< 1% overhead)
- **JAR size**: Slightly smaller due to shorter class/method names

## Support

For issues with yGuard configuration:
1. Check the [yGuard documentation](https://yworks.github.io/yGuard/)
2. Review the [yGuard examples](https://github.com/yWorks/yGuard/tree/master/examples)
3. Check the obfuscation log files for errors
