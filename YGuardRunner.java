import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

/**
 * Simple Java program to run yGuard obfuscation
 * This creates an Ant build file and executes it
 */
public class YGuardRunner {
    
    public static void main(String[] args) {
        try {
            // Check if input JAR exists
            File inputJar = new File("target/windchill.complaints-0.0.1-SNAPSHOT.jar");
            if (!inputJar.exists()) {
                System.err.println("ERROR: Input JAR not found: " + inputJar.getAbsolutePath());
                System.err.println("Please run 'mvn clean package -DskipTests' first");
                System.exit(1);
            }
            
            // Create Ant build file
            String buildXml = createAntBuildFile();
            File buildFile = new File("yguard-build.xml");
            
            try (FileWriter writer = new FileWriter(buildFile)) {
                writer.write(buildXml);
            }
            
            System.out.println("========================================");
            System.out.println("Running yGuard Obfuscation");
            System.out.println("========================================");
            System.out.println("Input JAR: " + inputJar.getAbsolutePath());
            System.out.println("Build file: " + buildFile.getAbsolutePath());
            System.out.println();
            
            // Run Ant
            ProcessBuilder pb = new ProcessBuilder("java", "-cp", 
                System.getProperty("user.home") + "/.m2/repository/com/yworks/yguard/4.1.1/yguard-4.1.1.jar",
                "org.apache.tools.ant.Main", "-f", "yguard-build.xml", "obfuscate");
            pb.inheritIO();
            Process process = pb.start();
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                System.out.println();
                System.out.println("========================================");
                System.out.println("yGuard Obfuscation Completed Successfully!");
                System.out.println("========================================");
                
                File outputJar = new File("target/windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar");
                if (outputJar.exists()) {
                    System.out.println("✅ Obfuscated JAR created: " + outputJar.getAbsolutePath());
                    System.out.println("Original size:    " + inputJar.length() + " bytes");
                    System.out.println("Obfuscated size:  " + outputJar.length() + " bytes");
                } else {
                    System.out.println("⚠️  Obfuscated JAR not found");
                }
                
                File logFile = new File("target/yguard-runner.xml");
                if (logFile.exists()) {
                    System.out.println("📄 Log file: " + logFile.getAbsolutePath());
                }
            } else {
                System.err.println("ERROR: yGuard obfuscation failed with exit code: " + exitCode);
                System.exit(1);
            }
            
        } catch (Exception e) {
            System.err.println("ERROR: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
    
    private static String createAntBuildFile() {
        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
               "<project name=\"yguard-obfuscation\" default=\"obfuscate\" basedir=\".\">\n" +
               "    \n" +
               "    <property name=\"yguard.jar\" value=\"${user.home}/.m2/repository/com/yworks/yguard/4.1.1/yguard-4.1.1.jar\"/>\n" +
               "    <property name=\"input.jar\" value=\"target/windchill.complaints-0.0.1-SNAPSHOT.jar\"/>\n" +
               "    <property name=\"output.jar\" value=\"target/windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar\"/>\n" +
               "    <property name=\"log.file\" value=\"target/yguard-runner.xml\"/>\n" +
               "    \n" +
               "    <target name=\"obfuscate\">\n" +
               "        <taskdef name=\"yguard\" classname=\"com.yworks.yguard.YGuardTask\" classpath=\"${yguard.jar}\"/>\n" +
               "        \n" +
               "        <echo message=\"Starting yGuard obfuscation...\"/>\n" +
               "        <echo message=\"Input JAR: ${input.jar}\"/>\n" +
               "        <echo message=\"Output JAR: ${output.jar}\"/>\n" +
               "        \n" +
               "        <yguard>\n" +
               "            <inoutpair in=\"${input.jar}\" out=\"${output.jar}\"/>\n" +
               "            \n" +
               "            <rename logfile=\"${log.file}\" \n" +
               "                    replaceClassNameStrings=\"true\" \n" +
               "                    conserveManifest=\"true\">\n" +
               "                \n" +
               "                <!-- Keep main application class -->\n" +
               "                <keep>\n" +
               "                    <class classes=\"public\" methods=\"public\" fields=\"public\">\n" +
               "                        <patternset>\n" +
               "                            <include name=\"com.itcinfotech.windchill.complaints.WindchillComplaintsApplication\"/>\n" +
               "                        </patternset>\n" +
               "                    </class>\n" +
               "                </keep>\n" +
               "                \n" +
               "                <!-- Keep Spring Boot components -->\n" +
               "                <keep>\n" +
               "                    <class classes=\"public\" methods=\"public\" fields=\"public\">\n" +
               "                        <patternset>\n" +
               "                            <include name=\"com.itcinfotech.windchill.complaints.controller.**\"/>\n" +
               "                            <include name=\"com.itcinfotech.windchill.complaints.service.**\"/>\n" +
               "                            <include name=\"com.itcinfotech.windchill.complaints.config.**\"/>\n" +
               "                            <include name=\"com.itcinfotech.windchill.complaints.dto.**\"/>\n" +
               "                            <include name=\"com.itcinfotech.windchill.complaints.request.**\"/>\n" +
               "                            <include name=\"com.itcinfotech.windchill.complaints.response.**\"/>\n" +
               "                            <include name=\"com.itcinfotech.windchill.complaints.exception.**\"/>\n" +
               "                            <include name=\"com.itcinfotech.windchill.complaints.camel.**\"/>\n" +
               "                        </patternset>\n" +
               "                    </class>\n" +
               "                </keep>\n" +
               "                \n" +
               "                <!-- Keep serialization methods -->\n" +
               "                <keep>\n" +
               "                    <method class=\"**\" name=\"writeObject\"/>\n" +
               "                    <method class=\"**\" name=\"readObject\"/>\n" +
               "                    <method class=\"**\" name=\"writeReplace\"/>\n" +
               "                    <method class=\"**\" name=\"readResolve\"/>\n" +
               "                </keep>\n" +
               "                \n" +
               "                <!-- Keep enum methods -->\n" +
               "                <keep>\n" +
               "                    <method class=\"**\" name=\"values\"/>\n" +
               "                    <method class=\"**\" name=\"valueOf\"/>\n" +
               "                </keep>\n" +
               "                \n" +
               "            </rename>\n" +
               "        </yguard>\n" +
               "        \n" +
               "        <echo message=\"yGuard obfuscation completed successfully!\"/>\n" +
               "    </target>\n" +
               "    \n" +
               "</project>";
    }
}
""";
    }
}
