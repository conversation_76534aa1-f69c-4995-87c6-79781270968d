@echo off
REM Build script for creating obfuscated JAR of Windchill Complaints Microservice
REM Usage: build-obfuscated.bat

echo ========================================
echo Building Obfuscated Windchill Complaints Microservice
echo ========================================

echo.
echo Cleaning previous builds...
call mvn clean

if %ERRORLEVEL% neq 0 (
    echo ERROR: Maven clean failed
    pause
    exit /b 1
)

echo.
echo Building obfuscated JAR...
call mvn package -Pobfuscate

if %ERRORLEVEL% neq 0 (
    echo ERROR: Obfuscated build failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================

echo.
echo Generated files in target/ directory:
dir target\*.jar /b

echo.
echo ProGuard output files:
if exist target\mapping.txt echo - mapping.txt (name mappings)
if exist target\seeds.txt echo - seeds.txt (preserved classes)
if exist target\usage.txt echo - usage.txt (removed code)

echo.
echo To run the obfuscated application:
echo java -jar target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar

echo.
pause
