@echo off
REM Build script for yGuard obfuscation
REM This script builds the project and creates an obfuscated JAR using yGuard

echo ========================================
echo yGuard Obfuscation Build Script
echo ========================================
echo.

REM Check if <PERSON>ven is available
where mvn >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ERROR: <PERSON><PERSON> is not installed or not in PATH
    echo Please install Maven and try again
    pause
    exit /b 1
)

REM Check if Java is available
where java >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java and try again
    pause
    exit /b 1
)

echo Step 1: Cleaning previous builds...
call mvn clean
if %ERRORLEVEL% neq 0 (
    echo ERROR: Maven clean failed
    pause
    exit /b 1
)

echo.
echo Step 2: Building project and running yGuard obfuscation...
call mvn package
if %ERRORLEVEL% neq 0 (
    echo ERROR: <PERSON><PERSON> package failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================

REM Check if obfuscated JAR was created
if exist "target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar" (
    echo ✅ Obfuscated JAR created successfully
    echo    Location: target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar
) else (
    echo ⚠️  Obfuscated JAR not found - check build logs
)

REM Check if original JAR exists
if exist "target\windchill.complaints-0.0.1-SNAPSHOT.jar" (
    echo ✅ Original JAR: target\windchill.complaints-0.0.1-SNAPSHOT.jar
) else (
    echo ❌ Original JAR not found
)

REM Check if log file exists
if exist "target\yguard-log.xml" (
    echo ✅ yGuard log file: target\yguard-log.xml
) else (
    echo ⚠️  yGuard log file not found
)

echo.
echo ========================================
echo File sizes:
echo ========================================
if exist "target\windchill.complaints-0.0.1-SNAPSHOT.jar" (
    for %%I in ("target\windchill.complaints-0.0.1-SNAPSHOT.jar") do echo Original JAR:    %%~zI bytes
)
if exist "target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar" (
    for %%I in ("target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar") do echo Obfuscated JAR:  %%~zI bytes
)

echo.
echo ========================================
echo Next steps:
echo ========================================
echo 1. Test the obfuscated JAR:
echo    java -jar target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar
echo.
echo 2. Compare JAR contents:
echo    jar -tf target\windchill.complaints-0.0.1-SNAPSHOT.jar ^| findstr "com/itcinfotech"
echo    jar -tf target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar ^| findstr "com/itcinfotech"
echo.
echo 3. Check obfuscation log:
echo    type target\yguard-log.xml
echo.

pause
