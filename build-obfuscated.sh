#!/bin/bash
# Build script for yGuard obfuscation
# This script builds the project and creates an obfuscated JAR using yGuard

echo "========================================"
echo "yGuard Obfuscation Build Script"
echo "========================================"
echo

# Check if <PERSON><PERSON> is available
if ! command -v mvn &> /dev/null; then
    echo "ERROR: Maven is not installed or not in PATH"
    echo "Please install <PERSON><PERSON> and try again"
    exit 1
fi

# Check if Java is available
if ! command -v java &> /dev/null; then
    echo "ERROR: Java is not installed or not in PATH"
    echo "Please install Java and try again"
    exit 1
fi

echo "Step 1: Cleaning previous builds..."
mvn clean
if [ $? -ne 0 ]; then
    echo "ERROR: Maven clean failed"
    exit 1
fi

echo
echo "Step 2: Building project and running yGuard obfuscation..."
mvn package
if [ $? -ne 0 ]; then
    echo "ERROR: Maven package failed"
    exit 1
fi

echo
echo "========================================"
echo "Build completed successfully!"
echo "========================================"

# Check if obfuscated JAR was created
if [ -f "target/windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar" ]; then
    echo "✅ Obfuscated JAR created successfully"
    echo "   Location: target/windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar"
else
    echo "⚠️  Obfuscated JAR not found - check build logs"
fi

# Check if original JAR exists
if [ -f "target/windchill.complaints-0.0.1-SNAPSHOT.jar" ]; then
    echo "✅ Original JAR: target/windchill.complaints-0.0.1-SNAPSHOT.jar"
else
    echo "❌ Original JAR not found"
fi

# Check if log file exists
if [ -f "target/yguard-log.xml" ]; then
    echo "✅ yGuard log file: target/yguard-log.xml"
else
    echo "⚠️  yGuard log file not found"
fi

echo
echo "========================================"
echo "File sizes:"
echo "========================================"
if [ -f "target/windchill.complaints-0.0.1-SNAPSHOT.jar" ]; then
    original_size=$(stat -f%z "target/windchill.complaints-0.0.1-SNAPSHOT.jar" 2>/dev/null || stat -c%s "target/windchill.complaints-0.0.1-SNAPSHOT.jar" 2>/dev/null)
    echo "Original JAR:    $original_size bytes"
fi
if [ -f "target/windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar" ]; then
    obfuscated_size=$(stat -f%z "target/windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar" 2>/dev/null || stat -c%s "target/windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar" 2>/dev/null)
    echo "Obfuscated JAR:  $obfuscated_size bytes"
fi

echo
echo "========================================"
echo "Next steps:"
echo "========================================"
echo "1. Test the obfuscated JAR:"
echo "   java -jar target/windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar"
echo
echo "2. Compare JAR contents:"
echo "   jar -tf target/windchill.complaints-0.0.1-SNAPSHOT.jar | grep 'com/itcinfotech'"
echo "   jar -tf target/windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar | grep 'com/itcinfotech'"
echo
echo "3. Check obfuscation log:"
echo "   cat target/yguard-log.xml"
echo

# Make the script pause on macOS/Linux (optional)
read -p "Press Enter to continue..."
