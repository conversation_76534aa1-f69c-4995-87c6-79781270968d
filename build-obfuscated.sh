#!/bin/bash
# Build script for creating obfuscated JAR of Windchill Complaints Microservice
# Usage: ./build-obfuscated.sh

echo "========================================"
echo "Building Obfuscated Windchill Complaints Microservice"
echo "========================================"

echo
echo "Cleaning previous builds..."
mvn clean

if [ $? -ne 0 ]; then
    echo "ERROR: Maven clean failed"
    exit 1
fi

echo
echo "Building obfuscated JAR..."
mvn package -Pobfuscate

if [ $? -ne 0 ]; then
    echo "ERROR: Obfuscated build failed"
    exit 1
fi

echo
echo "========================================"
echo "Build completed successfully!"
echo "========================================"

echo
echo "Generated files in target/ directory:"
ls -la target/*.jar

echo
echo "ProGuard output files:"
[ -f target/mapping.txt ] && echo "- mapping.txt (name mappings)"
[ -f target/seeds.txt ] && echo "- seeds.txt (preserved classes)"
[ -f target/usage.txt ] && echo "- usage.txt (removed code)"

echo
echo "To run the obfuscated application:"
echo "java -jar target/windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar"

echo
