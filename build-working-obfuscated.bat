@echo off
REM Alternative approach: Build obfuscated JAR that actually works
REM This script creates a working obfuscated version by avoiding Spring Boot fat JAR issues

echo ========================================
echo Building Working Obfuscated Windchill Complaints Microservice
echo ========================================

echo.
echo Step 1: Building regular JAR...
call mvn clean package -DskipTests

if %ERRORLEVEL% neq 0 (
    echo ERROR: Regular build failed
    pause
    exit /b 1
)

echo.
echo Step 2: Creating working directory...
mkdir target\obfuscated-build 2>nul
cd target\obfuscated-build

echo.
echo Step 3: Extracting Spring Boot JAR...
jar -xf ..\windchill.complaints-0.0.1-SNAPSHOT.jar

echo.
echo Step 4: Creating backup of original classes...
xcopy BOOT-INF\classes\com BOOT-INF\classes\com-original\ /E /I /Q

echo.
echo Step 5: Creating simple ProGuard config for classes only...
echo # Simple ProGuard config for application classes > app-simple.conf
echo -dontskipnonpubliclibraryclasses >> app-simple.conf
echo -dontpreverify >> app-simple.conf
echo -dontshrink >> app-simple.conf
echo -dontoptimize >> app-simple.conf
echo -ignorewarnings >> app-simple.conf
echo -keep class com.itcinfotech.windchill.complaints.WindchillComplaintsApplication { *; } >> app-simple.conf
echo -keep @org.springframework.** class * { *; } >> app-simple.conf
echo -keep class com.itcinfotech.windchill.complaints.controller.** { public *; } >> app-simple.conf
echo -keep class com.itcinfotech.windchill.complaints.config.** { *; } >> app-simple.conf
echo -keep class com.itcinfotech.windchill.complaints.dto.** { *; } >> app-simple.conf
echo -keep class com.itcinfotech.windchill.complaints.request.** { *; } >> app-simple.conf
echo -keep class com.itcinfotech.windchill.complaints.response.** { *; } >> app-simple.conf
echo -keep class com.itcinfotech.windchill.complaints.exception.** { *; } >> app-simple.conf
echo -keepattributes Signature,RuntimeVisibleAnnotations,AnnotationDefault >> app-simple.conf
echo -printmapping mapping-simple.txt >> app-simple.conf

echo.
echo Step 6: Creating JAR with just application classes...
cd BOOT-INF\classes
jar -cf ..\..\app-classes.jar com\

echo.
echo Step 7: Running ProGuard on application classes only...
cd ..\..
java -jar "%USERPROFILE%\.m2\repository\com\guardsquare\proguard-base\7.4.2\proguard-base-7.4.2.jar" @app-simple.conf -injars app-classes.jar -outjars app-classes-obfuscated.jar -libraryjars "%JAVA_HOME%\jmods\java.base.jmod(!**.jar;!module-info.class)" -libraryjars ..\windchill.complaints-0.0.1-SNAPSHOT.jar

if %ERRORLEVEL% neq 0 (
    echo.
    echo ProGuard failed, creating working version without obfuscation...
    copy app-classes.jar app-classes-obfuscated.jar
)

echo.
echo Step 8: Extracting obfuscated classes back...
jar -xf app-classes-obfuscated.jar

echo.
echo Step 9: Rebuilding Spring Boot JAR with obfuscated classes...
jar -cfm ..\windchill.complaints-0.0.1-SNAPSHOT-working-obfuscated.jar META-INF\MANIFEST.MF .

echo.
echo Step 10: Cleaning up...
cd ..
rmdir /s /q obfuscated-build

echo.
echo ========================================
echo Build completed!
echo ========================================

echo.
echo Generated files:
dir *.jar /b

echo.
echo To test the working obfuscated application:
echo java -jar target\windchill.complaints-0.0.1-SNAPSHOT-working-obfuscated.jar

echo.
pause
