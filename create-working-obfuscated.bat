@echo off
REM Create a working obfuscated JAR by manually handling Spring Boot structure
REM This approach avoids ProGuard issues with Spring Boot loader classes

echo ========================================
echo Creating Working Obfuscated Windchill Complaints JAR
echo ========================================

REM Step 1: Build the regular JAR
echo.
echo Step 1: Building regular Spring Boot JAR...
call mvn clean package -DskipTests

if %ERRORLEVEL% neq 0 (
    echo ERROR: Regular build failed
    pause
    exit /b 1
)

REM Step 2: Create working directory
echo.
echo Step 2: Setting up working directory...
if exist target\obfuscated-work rmdir /s /q target\obfuscated-work
mkdir target\obfuscated-work
cd target\obfuscated-work

REM Step 3: Extract the Spring Boot JAR
echo.
echo Step 3: Extracting Spring Boot JAR...
jar -xf ..\windchill.complaints-0.0.1-SNAPSHOT.jar

REM Step 4: Create JAR with just your application classes
echo.
echo Step 4: Creating JAR with application classes only...
cd BOOT-INF\classes
jar -cf ..\..\app-only.jar com\itcinfotech\

REM Step 5: Create simple ProGuard config for application classes
echo.
echo Step 5: Creating ProGuard configuration...
cd ..\..
echo # Simple ProGuard config for application classes only > app-obfuscate.conf
echo -dontskipnonpubliclibraryclasses >> app-obfuscate.conf
echo -dontpreverify >> app-obfuscate.conf
echo -dontshrink >> app-obfuscate.conf
echo -dontoptimize >> app-obfuscate.conf
echo -ignorewarnings >> app-obfuscate.conf
echo -dontwarn ** >> app-obfuscate.conf
echo. >> app-obfuscate.conf
echo # Keep main class >> app-obfuscate.conf
echo -keep class com.itcinfotech.windchill.complaints.WindchillComplaintsApplication { *; } >> app-obfuscate.conf
echo. >> app-obfuscate.conf
echo # Keep Spring annotations >> app-obfuscate.conf
echo -keep @org.springframework.** class * { *; } >> app-obfuscate.conf
echo -keep class com.itcinfotech.windchill.complaints.controller.** { public *; } >> app-obfuscate.conf
echo -keep class com.itcinfotech.windchill.complaints.config.** { *; } >> app-obfuscate.conf
echo -keep class com.itcinfotech.windchill.complaints.dto.** { *; } >> app-obfuscate.conf
echo -keep class com.itcinfotech.windchill.complaints.request.** { *; } >> app-obfuscate.conf
echo -keep class com.itcinfotech.windchill.complaints.response.** { *; } >> app-obfuscate.conf
echo -keep class com.itcinfotech.windchill.complaints.exception.** { *; } >> app-obfuscate.conf
echo. >> app-obfuscate.conf
echo # Keep attributes >> app-obfuscate.conf
echo -keepattributes *Annotation* >> app-obfuscate.conf
echo -keepattributes Signature >> app-obfuscate.conf
echo -keepattributes SourceFile,LineNumberTable >> app-obfuscate.conf
echo. >> app-obfuscate.conf
echo # Allow obfuscation of private methods and fields >> app-obfuscate.conf
echo -keepclassmembers class com.itcinfotech.windchill.complaints.service.** { >> app-obfuscate.conf
echo     public *; >> app-obfuscate.conf
echo     protected *; >> app-obfuscate.conf
echo } >> app-obfuscate.conf
echo. >> app-obfuscate.conf
echo -keepclassmembers class com.itcinfotech.windchill.complaints.utils.** { >> app-obfuscate.conf
echo     public *; >> app-obfuscate.conf
echo     protected *; >> app-obfuscate.conf
echo } >> app-obfuscate.conf
echo. >> app-obfuscate.conf
echo -printmapping mapping-working.txt >> app-obfuscate.conf

REM Step 6: Run ProGuard on application classes only
echo.
echo Step 6: Running ProGuard on application classes...
java -jar "%USERPROFILE%\.m2\repository\com\guardsquare\proguard-base\7.4.2\proguard-base-7.4.2.jar" @app-obfuscate.conf -injars app-only.jar -outjars app-obfuscated.jar -libraryjars "%JAVA_HOME%\jmods\java.base.jmod(!**.jar;!module-info.class)" -libraryjars ..\windchill.complaints-0.0.1-SNAPSHOT.jar

if %ERRORLEVEL% neq 0 (
    echo.
    echo ProGuard failed, using original classes...
    copy app-only.jar app-obfuscated.jar
)

REM Step 7: Replace application classes with obfuscated ones
echo.
echo Step 7: Replacing application classes with obfuscated versions...
rmdir /s /q BOOT-INF\classes\com
jar -xf app-obfuscated.jar

REM Step 8: Rebuild the Spring Boot JAR
echo.
echo Step 8: Creating final obfuscated Spring Boot JAR...
jar -cfm ..\windchill.complaints-0.0.1-SNAPSHOT-final-obfuscated.jar META-INF\MANIFEST.MF .

REM Step 9: Copy mapping file
echo.
echo Step 9: Copying mapping file...
if exist mapping-working.txt copy mapping-working.txt ..\mapping-final.txt

REM Step 10: Cleanup
echo.
echo Step 10: Cleaning up...
cd ..
rmdir /s /q obfuscated-work

echo.
echo ========================================
echo Build completed successfully!
echo ========================================

echo.
echo Generated files:
dir windchill.complaints-0.0.1-SNAPSHOT-final-obfuscated.jar

echo.
echo To test the obfuscated application:
echo java -jar target\windchill.complaints-0.0.1-SNAPSHOT-final-obfuscated.jar

echo.
pause
