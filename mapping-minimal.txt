com.itcinfotech.windchill.complaints.WindchillComplaintsApplication -> com.itcinfotech.windchill.complaints.WindchillComplaintsApplication:
# {"fileName":"WindchillComplaintsApplication.java","id":"sourceFile"}
    7:7:void <init>() -> <init>
    10:11:void main(java.lang.String[]) -> main
com.itcinfotech.windchill.complaints.camel.FileWatcherRoute -> com.itcinfotech.windchill.complaints.camel.FileWatcherRoute:
# {"fileName":"FileWatcherRoute.java","id":"sourceFile"}
    java.lang.String rootDir -> a
    com.itcinfotech.windchill.complaints.utils.ZipUtil zipUtil -> b
    19:19:void <init>() -> <init>
    31:74:void configure() -> configure
    57:73:void lambda$configure$0(org.apache.camel.Exchange) -> a
com.itcinfotech.windchill.complaints.config.AppConfig -> com.itcinfotech.windchill.complaints.config.AppConfig:
# {"fileName":"AppConfig.java","id":"sourceFile"}
    9:9:void <init>() -> <init>
    13:31:org.springframework.web.client.RestTemplate restTemplate(org.springframework.boot.web.client.RestTemplateBuilder) -> restTemplate
    28:28:org.springframework.http.client.ClientHttpResponse lambda$restTemplate$0(org.springframework.http.HttpRequest,byte[],org.springframework.http.client.ClientHttpRequestExecution) -> a
com.itcinfotech.windchill.complaints.config.CamelConfig -> com.itcinfotech.windchill.complaints.config.CamelConfig:
# {"fileName":"CamelConfig.java","id":"sourceFile"}
    9:9:void <init>() -> <init>
com.itcinfotech.windchill.complaints.config.FreshserviceConfig -> com.itcinfotech.windchill.complaints.config.FreshserviceConfig:
# {"fileName":"FreshserviceConfig.java","id":"sourceFile"}
    java.lang.String apiUrl -> a
    java.lang.String apiKey -> b
    long maxAttachmentSize -> c
    java.lang.String allowedExtensions -> d
    17:17:void <init>() -> <init>
    39:43:java.nio.file.Path tempAttachmentDirectory() -> tempAttachmentDirectory
    53:68:void validateFreshserviceConfig() -> validateFreshserviceConfig
com.itcinfotech.windchill.complaints.config.IdentityNamingStrategy -> com.itcinfotech.windchill.complaints.config.IdentityNamingStrategy:
# {"fileName":"IdentityNamingStrategy.java","id":"sourceFile"}
    5:5:void <init>() -> <init>
    8:8:java.lang.String translate(java.lang.String) -> translate
com.itcinfotech.windchill.complaints.config.JacksonConfig -> com.itcinfotech.windchill.complaints.config.JacksonConfig:
# {"fileName":"JacksonConfig.java","id":"sourceFile"}
    12:12:void <init>() -> <init>
    16:25:com.fasterxml.jackson.databind.ObjectMapper objectMapper() -> objectMapper
com.itcinfotech.windchill.complaints.config.MultipartConfig -> com.itcinfotech.windchill.complaints.config.MultipartConfig:
# {"fileName":"MultipartConfig.java","id":"sourceFile"}
    12:12:void <init>() -> <init>
    16:16:org.springframework.web.multipart.MultipartResolver multipartResolver() -> multipartResolver
    21:24:jakarta.servlet.MultipartConfigElement multipartConfigElement() -> multipartConfigElement
com.itcinfotech.windchill.complaints.config.SecurityConfig -> com.itcinfotech.windchill.complaints.config.SecurityConfig:
# {"fileName":"SecurityConfig.java","id":"sourceFile"}
    java.lang.String ENCODED_PASSWORD -> a
    19:19:void <init>() -> <init>
    23:27:org.springframework.security.web.SecurityFilterChain filterChain(org.springframework.security.config.annotation.web.builders.HttpSecurity) -> filterChain
    33:38:org.springframework.security.core.userdetails.UserDetailsService userDetailsService() -> userDetailsService
    43:43:org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder passwordEncoder() -> passwordEncoder
    24:25:void lambda$filterChain$0(org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer$AuthorizationManagerRequestMatcherRegistry) -> a
com.itcinfotech.windchill.complaints.config.SwaggerConfig -> com.itcinfotech.windchill.complaints.config.SwaggerConfig:
# {"fileName":"SwaggerConfig.java","id":"sourceFile"}
    24:24:void <init>() -> <init>
    28:35:io.swagger.v3.oas.models.OpenAPI customOpenAPI() -> customOpenAPI
com.itcinfotech.windchill.complaints.controller.ComplaintController -> com.itcinfotech.windchill.complaints.controller.ComplaintController:
# {"fileName":"ComplaintController.java","id":"sourceFile"}
    com.itcinfotech.windchill.complaints.service.ComplaintService complaintService -> a
    com.itcinfotech.windchill.complaints.service.CsrfTokenService csrfTokenService -> b
    24:27:void <init>(com.itcinfotech.windchill.complaints.service.ComplaintService,com.itcinfotech.windchill.complaints.service.CsrfTokenService) -> <init>
    37:41:org.springframework.http.ResponseEntity createComplaint(com.itcinfotech.windchill.complaints.request.MozarcComplaintRequest,java.lang.Integer) -> createComplaint
    54:59:org.springframework.http.ResponseEntity getCsrfToken() -> getCsrfToken
com.itcinfotech.windchill.complaints.controller.ComplaintController$CsrfTokenResponse -> com.itcinfotech.windchill.complaints.controller.ComplaintController$CsrfTokenResponse:
# {"fileName":"ComplaintController.java","id":"sourceFile"}
    java.lang.String csrfToken -> a
    long timestamp -> b
    70:73:void <init>(java.lang.String) -> <init>
    76:76:java.lang.String getCsrfToken() -> getCsrfToken
    80:80:long getTimestamp() -> getTimestamp
com.itcinfotech.windchill.complaints.controller.FreshserviceTicketController -> com.itcinfotech.windchill.complaints.controller.FreshserviceTicketController:
# {"fileName":"FreshserviceTicketController.java","id":"sourceFile"}
    org.slf4j.Logger logger -> a
    com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketService freshserviceTicketService -> b
    29:31:void <init>(com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketService) -> <init>
    42:42:org.springframework.http.ResponseEntity createTicket(com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest) -> createTicket
    56:59:org.springframework.http.ResponseEntity createTicketWithAttachments(com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest,java.util.List) -> createTicketWithAttachments
    73:75:org.springframework.http.ResponseEntity addAttachmentsToTicket(java.lang.Long,java.util.List) -> addAttachmentsToTicket
    87:89:org.springframework.http.ResponseEntity getTicketById(java.lang.Long) -> getTicketById
    101:103:org.springframework.http.ResponseEntity getTicketsByIds(java.util.List) -> getTicketsByIds
    115:117:org.springframework.http.ResponseEntity getTicketAttachments(java.lang.Long) -> getTicketAttachments
    129:131:org.springframework.http.ResponseEntity downloadAttachment(java.lang.Long) -> downloadAttachment
    25:25:void <clinit>() -> <clinit>
com.itcinfotech.windchill.complaints.controller.FreshserviceTicketExampleController -> com.itcinfotech.windchill.complaints.controller.FreshserviceTicketExampleController:
# {"fileName":"FreshserviceTicketExampleController.java","id":"sourceFile"}
    org.slf4j.Logger logger -> a
    com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketExample freshserviceTicketExample -> b
    com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketService freshserviceTicketService -> c
    org.springframework.web.client.RestTemplate restTemplate -> d
    java.lang.String freshserviceBaseUrl -> e
    java.lang.String freshserviceApiKey -> f
    54:58:void <init>(com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketExample,com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketService,org.springframework.web.client.RestTemplate) -> <init>
    78:142:org.springframework.http.ResponseEntity testCreateTicket() -> testCreateTicket
    181:409:org.springframework.http.ResponseEntity createTicketWithAttachments(java.util.List,java.lang.String) -> createTicketWithAttachments
    442:498:org.springframework.http.ResponseEntity testAddAttachmentsToTicket(java.lang.Long,java.util.List) -> testAddAttachmentsToTicket
    556:570:org.springframework.http.ResponseEntity createTicketWithCustomRequest(com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest) -> createTicketWithCustomRequest
    582:612:org.springframework.http.ResponseEntity getTicketById(java.lang.Long) -> getTicketById
    635:726:org.springframework.http.ResponseEntity getTicketRaw(java.lang.Long) -> getTicketRaw
    749:764:org.springframework.http.ResponseEntity getTicketAttachments(java.lang.Long) -> getTicketAttachments
    786:801:org.springframework.http.ResponseEntity downloadAttachment(java.lang.Long) -> downloadAttachment
    824:831:org.springframework.http.ResponseEntity getTicketConversations(java.lang.Long) -> getTicketConversations
    853:900:org.springframework.http.ResponseEntity createTicketDirectJson() -> createTicketDirectJson
    39:39:void <clinit>() -> <clinit>
com.itcinfotech.windchill.complaints.controller.UploadController -> com.itcinfotech.windchill.complaints.controller.UploadController:
# {"fileName":"UploadController.java","id":"sourceFile"}
    8:8:void <init>() -> <init>
com.itcinfotech.windchill.complaints.dto.AdditionalRelatedPersonnelOrLocations -> com.itcinfotech.windchill.complaints.dto.AdditionalRelatedPersonnelOrLocations:
# {"fileName":"AdditionalRelatedPersonnelOrLocations.java","id":"sourceFile"}
    java.lang.String oDataType -> a
    java.lang.String oDataContact -> b
    6:6:void <init>(java.lang.String,java.lang.String) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.String oDataType() -> oDataType
    6:6:java.lang.String oDataContact() -> oDataContact
com.itcinfotech.windchill.complaints.dto.AgeUnits -> com.itcinfotech.windchill.complaints.dto.AgeUnits:
# {"fileName":"AgeUnits.java","id":"sourceFile"}
    java.lang.String value -> a
    6:6:void <init>(java.lang.String) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.String value() -> value
com.itcinfotech.windchill.complaints.dto.Circumstance -> com.itcinfotech.windchill.complaints.dto.Circumstance:
# {"fileName":"Circumstance.java","id":"sourceFile"}
    java.lang.String value -> a
    java.lang.String display -> b
    6:6:void <init>(java.lang.String,java.lang.String) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.String value() -> value
    6:6:java.lang.String display() -> display
com.itcinfotech.windchill.complaints.dto.ContainerValue -> com.itcinfotech.windchill.complaints.dto.ContainerValue:
# {"fileName":"ContainerValue.java","id":"sourceFile"}
    java.lang.String oDataType -> a
    java.lang.String id -> b
    java.lang.String name -> c
    9:9:void <init>(java.lang.String,java.lang.String,java.lang.String) -> <init>
    8:8:java.lang.String toString() -> toString
    8:8:int hashCode() -> hashCode
    8:8:boolean equals(java.lang.Object) -> equals
    8:8:java.lang.String oDataType() -> oDataType
    8:8:java.lang.String id() -> id
    8:8:java.lang.String name() -> name
com.itcinfotech.windchill.complaints.dto.Content -> com.itcinfotech.windchill.complaints.dto.Content:
# {"fileName":"Content.java","id":"sourceFile"}
    java.lang.String url -> a
    java.lang.String label -> b
    6:6:void <init>(java.lang.String,java.lang.String) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.String url() -> url
    6:6:java.lang.String label() -> label
com.itcinfotech.windchill.complaints.dto.ContentInfoStage1 -> com.itcinfotech.windchill.complaints.dto.ContentInfoStage1:
# {"fileName":"ContentInfoStage1.java","id":"sourceFile"}
    java.lang.String streamId -> a
    java.lang.String fileSize -> b
    java.lang.String encodedInfo -> c
    java.lang.String fileName -> d
    java.lang.String mimeType -> e
    java.lang.Boolean primaryContent -> f
    6:6:void <init>(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Boolean) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.String streamId() -> streamId
    6:6:java.lang.String fileSize() -> fileSize
    6:6:java.lang.String encodedInfo() -> encodedInfo
    6:6:java.lang.String fileName() -> fileName
    6:6:java.lang.String mimeType() -> mimeType
    6:6:java.lang.Boolean primaryContent() -> primaryContent
com.itcinfotech.windchill.complaints.dto.ContentInfoStage2 -> com.itcinfotech.windchill.complaints.dto.ContentInfoStage2:
# {"fileName":"ContentInfoStage2.java","id":"sourceFile"}
    java.lang.Integer streamId -> a
    java.lang.Integer fileSize -> b
    java.lang.String encodedInfo -> c
    6:6:void <init>(java.lang.Integer,java.lang.Integer,java.lang.String) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.Integer streamId() -> streamId
    6:6:java.lang.Integer fileSize() -> fileSize
    6:6:java.lang.String encodedInfo() -> encodedInfo
com.itcinfotech.windchill.complaints.dto.ContentInfoStage3 -> com.itcinfotech.windchill.complaints.dto.ContentInfoStage3:
# {"fileName":"ContentInfoStage3.java","id":"sourceFile"}
    java.lang.Integer streamId -> a
    java.lang.Integer fileSize -> b
    java.lang.String encodedInfo -> c
    java.lang.String fileName -> d
    java.lang.String mimeType -> e
    java.lang.Boolean primaryContent -> f
    6:6:void <init>(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.String,java.lang.Boolean) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.Integer streamId() -> streamId
    6:6:java.lang.Integer fileSize() -> fileSize
    6:6:java.lang.String encodedInfo() -> encodedInfo
    6:6:java.lang.String fileName() -> fileName
    6:6:java.lang.String mimeType() -> mimeType
    6:6:java.lang.Boolean primaryContent() -> primaryContent
com.itcinfotech.windchill.complaints.dto.ContentValue -> com.itcinfotech.windchill.complaints.dto.ContentValue:
# {"fileName":"ContentValue.java","id":"sourceFile"}
    java.lang.String id -> a
    int vaultId -> b
    java.lang.String masterUrl -> c
    java.lang.String replicaUrl -> d
    java.util.List fileNames -> e
    int folderId -> f
    java.util.List streamIds -> g
    9:9:void <init>(java.lang.String,int,java.lang.String,java.lang.String,java.util.List,int,java.util.List) -> <init>
    8:8:java.lang.String toString() -> toString
    8:8:int hashCode() -> hashCode
    8:8:boolean equals(java.lang.Object) -> equals
    8:8:java.lang.String id() -> id
    8:8:int vaultId() -> vaultId
    8:8:java.lang.String masterUrl() -> masterUrl
    8:8:java.lang.String replicaUrl() -> replicaUrl
    8:8:java.util.List fileNames() -> fileNames
    8:8:int folderId() -> folderId
    8:8:java.util.List streamIds() -> streamIds
com.itcinfotech.windchill.complaints.dto.ContentValueStage3 -> com.itcinfotech.windchill.complaints.dto.ContentValueStage3:
# {"fileName":"ContentValueStage3.java","id":"sourceFile"}
    java.lang.String contentType -> a
    java.lang.String category -> b
    java.lang.String comments -> c
    java.lang.String createdBy -> d
    java.lang.String createdOn -> e
    java.lang.String description -> f
    com.itcinfotech.windchill.complaints.dto.FormatIcon formatIcon -> g
    java.lang.String id -> h
    java.lang.String lastModified -> i
    java.lang.String modifiedBy -> j
    com.itcinfotech.windchill.complaints.dto.Content content -> k
    java.lang.String fileName -> l
    java.lang.Integer fileSize -> m
    java.lang.String format -> n
    java.lang.String mimeType -> o
    9:9:void <init>(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.itcinfotech.windchill.complaints.dto.FormatIcon,java.lang.String,java.lang.String,java.lang.String,com.itcinfotech.windchill.complaints.dto.Content,java.lang.String,java.lang.Integer,java.lang.String,java.lang.String) -> <init>
    8:8:java.lang.String toString() -> toString
    8:8:int hashCode() -> hashCode
    8:8:boolean equals(java.lang.Object) -> equals
    8:8:java.lang.String contentType() -> contentType
    8:8:java.lang.String category() -> category
    8:8:java.lang.String comments() -> comments
    8:8:java.lang.String createdBy() -> createdBy
    8:8:java.lang.String createdOn() -> createdOn
    8:8:java.lang.String description() -> description
    8:8:com.itcinfotech.windchill.complaints.dto.FormatIcon formatIcon() -> formatIcon
    8:8:java.lang.String id() -> id
    8:8:java.lang.String lastModified() -> lastModified
    8:8:java.lang.String modifiedBy() -> modifiedBy
    8:8:com.itcinfotech.windchill.complaints.dto.Content content() -> content
    8:8:java.lang.String fileName() -> fileName
    8:8:java.lang.Integer fileSize() -> fileSize
    8:8:java.lang.String format() -> format
    8:8:java.lang.String mimeType() -> mimeType
com.itcinfotech.windchill.complaints.dto.CountryOfEvent -> com.itcinfotech.windchill.complaints.dto.CountryOfEvent:
# {"fileName":"CountryOfEvent.java","id":"sourceFile"}
    java.lang.String value -> a
    java.lang.String display -> b
    6:6:void <init>(java.lang.String,java.lang.String) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.String value() -> value
    6:6:java.lang.String display() -> display
com.itcinfotech.windchill.complaints.dto.CountryOfOrigin -> com.itcinfotech.windchill.complaints.dto.CountryOfOrigin:
# {"fileName":"CountryOfOrigin.java","id":"sourceFile"}
    java.lang.String value -> a
    java.lang.String display -> b
    6:6:void <init>(java.lang.String,java.lang.String) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.String value() -> value
    6:6:java.lang.String display() -> display
com.itcinfotech.windchill.complaints.dto.EventLocation -> com.itcinfotech.windchill.complaints.dto.EventLocation:
# {"fileName":"EventLocation.java","id":"sourceFile"}
    java.lang.String value -> a
    java.lang.String display -> b
    6:6:void <init>(java.lang.String,java.lang.String) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.String value() -> value
    6:6:java.lang.String display() -> display
com.itcinfotech.windchill.complaints.dto.FileInfo -> com.itcinfotech.windchill.complaints.dto.FileInfo:
# {"fileName":"FileInfo.java","id":"sourceFile"}
    java.lang.String fileName -> a
    java.lang.Integer fileSize -> b
    6:6:void <init>(java.lang.String,java.lang.Integer) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.String fileName() -> fileName
    6:6:java.lang.Integer fileSize() -> fileSize
com.itcinfotech.windchill.complaints.dto.FormatIcon -> com.itcinfotech.windchill.complaints.dto.FormatIcon:
# {"fileName":"FormatIcon.java","id":"sourceFile"}
    java.lang.String path -> a
    java.lang.String tooltip -> b
    6:6:void <init>(java.lang.String,java.lang.String) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.String path() -> path
    6:6:java.lang.String tooltip() -> tooltip
com.itcinfotech.windchill.complaints.dto.Gender -> com.itcinfotech.windchill.complaints.dto.Gender:
# {"fileName":"Gender.java","id":"sourceFile"}
    java.lang.String value -> a
    6:6:void <init>(java.lang.String) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.String value() -> value
com.itcinfotech.windchill.complaints.dto.HowReported -> com.itcinfotech.windchill.complaints.dto.HowReported:
# {"fileName":"HowReported.java","id":"sourceFile"}
    java.lang.String value -> a
    java.lang.String display -> b
    6:6:void <init>(java.lang.String,java.lang.String) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.String value() -> value
    6:6:java.lang.String display() -> display
com.itcinfotech.windchill.complaints.dto.ManufacturingValue -> com.itcinfotech.windchill.complaints.dto.ManufacturingValue:
# {"fileName":"ManufacturingValue.java","id":"sourceFile"}
    java.lang.String oDataType -> a
    java.lang.String placeId -> b
    java.lang.String name -> c
    7:7:void <init>(java.lang.String,java.lang.String,java.lang.String) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.String oDataType() -> oDataType
    6:6:java.lang.String placeId() -> placeId
    6:6:java.lang.String name() -> name
com.itcinfotech.windchill.complaints.dto.MozarcComplaintValue -> com.itcinfotech.windchill.complaints.dto.MozarcComplaintValue:
# {"fileName":"MozarcComplaintValue.java","id":"sourceFile"}
    java.lang.String oDataType -> a
    java.lang.String id -> b
    7:7:void <init>(java.lang.String,java.lang.String) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.String oDataType() -> oDataType
    6:6:java.lang.String id() -> id
com.itcinfotech.windchill.complaints.dto.OrganizationID -> com.itcinfotech.windchill.complaints.dto.OrganizationID:
# {"fileName":"OrganizationID.java","id":"sourceFile"}
    java.lang.String codingSystem -> a
    java.lang.String uniqueIdentifier -> b
    5:5:void <init>(java.lang.String,java.lang.String) -> <init>
    5:5:java.lang.String toString() -> toString
    5:5:int hashCode() -> hashCode
    5:5:boolean equals(java.lang.Object) -> equals
    5:5:java.lang.String codingSystem() -> codingSystem
    5:5:java.lang.String uniqueIdentifier() -> uniqueIdentifier
com.itcinfotech.windchill.complaints.dto.PartValue -> com.itcinfotech.windchill.complaints.dto.PartValue:
# {"fileName":"PartValue.java","id":"sourceFile"}
    java.lang.String oDataType -> a
    java.lang.String partId -> b
    java.lang.String number -> c
    7:7:void <init>(java.lang.String,java.lang.String,java.lang.String) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.String oDataType() -> oDataType
    6:6:java.lang.String partId() -> partId
    6:6:java.lang.String number() -> number
com.itcinfotech.windchill.complaints.dto.PeopleValue -> com.itcinfotech.windchill.complaints.dto.PeopleValue:
# {"fileName":"PeopleValue.java","id":"sourceFile"}
    java.lang.String oDataType -> a
    java.lang.String id -> b
    java.lang.String name -> c
    java.lang.String uniquenessEmailAddress -> d
    7:7:void <init>(java.lang.String,java.lang.String,java.lang.String,java.lang.String) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.String oDataType() -> oDataType
    6:6:java.lang.String id() -> id
    6:6:java.lang.String name() -> name
    6:6:java.lang.String uniquenessEmailAddress() -> uniquenessEmailAddress
com.itcinfotech.windchill.complaints.dto.PrimaryRelatedPersonOrLocation -> com.itcinfotech.windchill.complaints.dto.PrimaryRelatedPersonOrLocation:
# {"fileName":"PrimaryRelatedPersonOrLocation.java","id":"sourceFile"}
    java.lang.String odataType -> a
    java.lang.String contactOdataBind -> b
    java.lang.Integer age -> c
    com.itcinfotech.windchill.complaints.dto.AgeUnits ageUnits -> d
    java.lang.String dateOfBirth -> e
    java.lang.Boolean dateOfBirthApproximate -> f
    com.itcinfotech.windchill.complaints.dto.Gender gender -> g
    java.lang.Integer weight -> h
    com.itcinfotech.windchill.complaints.dto.WeightUnits weightUnits -> i
    7:7:void <init>(java.lang.String,java.lang.String,java.lang.Integer,com.itcinfotech.windchill.complaints.dto.AgeUnits,java.lang.String,java.lang.Boolean,com.itcinfotech.windchill.complaints.dto.Gender,java.lang.Integer,com.itcinfotech.windchill.complaints.dto.WeightUnits) -> <init>
    7:7:java.lang.String toString() -> toString
    7:7:int hashCode() -> hashCode
    7:7:boolean equals(java.lang.Object) -> equals
    7:7:java.lang.String odataType() -> odataType
    7:7:java.lang.String contactOdataBind() -> contactOdataBind
    7:7:java.lang.Integer age() -> age
    7:7:com.itcinfotech.windchill.complaints.dto.AgeUnits ageUnits() -> ageUnits
    7:7:java.lang.String dateOfBirth() -> dateOfBirth
    7:7:java.lang.Boolean dateOfBirthApproximate() -> dateOfBirthApproximate
    7:7:com.itcinfotech.windchill.complaints.dto.Gender gender() -> gender
    7:7:java.lang.Integer weight() -> weight
    7:7:com.itcinfotech.windchill.complaints.dto.WeightUnits weightUnits() -> weightUnits
com.itcinfotech.windchill.complaints.dto.PrimaryRelatedProduct -> com.itcinfotech.windchill.complaints.dto.PrimaryRelatedProduct:
# {"fileName":"PrimaryRelatedProduct.java","id":"sourceFile"}
    java.lang.String oDataSubject -> a
    java.lang.Boolean expectedReturn -> b
    java.lang.Boolean primary -> c
    java.lang.Integer quantity -> d
    java.lang.String serialLotNumber -> e
    com.itcinfotech.windchill.complaints.dto.UnitOfMeasure unitOfMeasure -> f
    java.lang.String oDataManufacturingLocation -> g
    9:9:void <init>(java.lang.String,java.lang.Boolean,java.lang.Boolean,java.lang.Integer,java.lang.String,com.itcinfotech.windchill.complaints.dto.UnitOfMeasure,java.lang.String) -> <init>
    8:8:java.lang.String toString() -> toString
    8:8:int hashCode() -> hashCode
    8:8:boolean equals(java.lang.Object) -> equals
    8:8:java.lang.String oDataSubject() -> oDataSubject
    8:8:java.lang.Boolean expectedReturn() -> expectedReturn
    8:8:java.lang.Boolean primary() -> primary
    8:8:java.lang.Integer quantity() -> quantity
    8:8:java.lang.String serialLotNumber() -> serialLotNumber
    8:8:com.itcinfotech.windchill.complaints.dto.UnitOfMeasure unitOfMeasure() -> unitOfMeasure
    8:8:java.lang.String oDataManufacturingLocation() -> oDataManufacturingLocation
com.itcinfotech.windchill.complaints.dto.State -> com.itcinfotech.windchill.complaints.dto.State:
# {"fileName":"State.java","id":"sourceFile"}
    java.lang.String value -> a
    java.lang.String display -> b
    6:6:void <init>(java.lang.String,java.lang.String) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.String value() -> value
    6:6:java.lang.String display() -> display
com.itcinfotech.windchill.complaints.dto.TypeIcon -> com.itcinfotech.windchill.complaints.dto.TypeIcon:
# {"fileName":"TypeIcon.java","id":"sourceFile"}
    java.lang.String path -> a
    java.lang.String tooltip -> b
    6:6:void <init>(java.lang.String,java.lang.String) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.String path() -> path
    6:6:java.lang.String tooltip() -> tooltip
com.itcinfotech.windchill.complaints.dto.UnitOfMeasure -> com.itcinfotech.windchill.complaints.dto.UnitOfMeasure:
# {"fileName":"UnitOfMeasure.java","id":"sourceFile"}
    java.lang.String value -> a
    6:6:void <init>(java.lang.String) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.String value() -> value
com.itcinfotech.windchill.complaints.dto.WeightUnits -> com.itcinfotech.windchill.complaints.dto.WeightUnits:
# {"fileName":"WeightUnits.java","id":"sourceFile"}
    java.lang.String value -> a
    6:6:void <init>(java.lang.String) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.String value() -> value
com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceAttachmentResponse -> com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceAttachmentResponse:
# {"fileName":"FreshserviceAttachmentResponse.java","id":"sourceFile"}
    java.lang.Long id -> a
    java.lang.String contentType -> b
    java.lang.Long size -> c
    java.lang.String name -> d
    java.lang.String attachmentUrl -> e
    java.lang.String createdAt -> f
    java.lang.String updatedAt -> g
    java.lang.String canonicalUrl -> h
    java.lang.Boolean hasAccess -> i
    35:36:void <init>() -> <init>
    40:40:java.lang.Long getId() -> getId
    44:45:void setId(java.lang.Long) -> setId
    48:48:java.lang.String getContentType() -> getContentType
    52:53:void setContentType(java.lang.String) -> setContentType
    56:56:java.lang.Long getSize() -> getSize
    60:61:void setSize(java.lang.Long) -> setSize
    64:64:java.lang.String getName() -> getName
    68:69:void setName(java.lang.String) -> setName
    72:72:java.lang.String getAttachmentUrl() -> getAttachmentUrl
    76:77:void setAttachmentUrl(java.lang.String) -> setAttachmentUrl
    80:80:java.lang.String getCreatedAt() -> getCreatedAt
    84:85:void setCreatedAt(java.lang.String) -> setCreatedAt
    88:88:java.lang.String getUpdatedAt() -> getUpdatedAt
    92:93:void setUpdatedAt(java.lang.String) -> setUpdatedAt
    96:96:java.lang.String getCanonicalUrl() -> getCanonicalUrl
    100:101:void setCanonicalUrl(java.lang.String) -> setCanonicalUrl
    104:104:java.lang.Boolean getHasAccess() -> getHasAccess
    108:109:void setHasAccess(java.lang.Boolean) -> setHasAccess
com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceAttachmentResponseWrapper -> com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceAttachmentResponseWrapper:
# {"fileName":"FreshserviceAttachmentResponseWrapper.java","id":"sourceFile"}
    java.util.List attachments -> a
    13:14:void <init>() -> <init>
    16:18:void <init>(java.util.List) -> <init>
    22:22:java.util.List getAttachments() -> getAttachments
    26:27:void setAttachments(java.util.List) -> setAttachments
com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceResponse -> com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceResponse:
# {"fileName":"FreshserviceResponse.java","id":"sourceFile"}
    java.lang.Object ticket -> a
    13:14:void <init>() -> <init>
    16:18:void <init>(java.lang.Object) -> <init>
    22:22:java.lang.Object getTicket() -> getTicket
    26:27:void setTicket(java.lang.Object) -> setTicket
com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest -> com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest:
# {"fileName":"FreshserviceTicketRequest.java","id":"sourceFile"}
    java.lang.String name -> a
    java.lang.String email -> b
    java.lang.String subject -> c
    java.lang.String description -> d
    java.lang.Integer status -> e
    java.lang.Integer priority -> f
    java.lang.Integer source -> g
    java.lang.Long departmentId -> h
    java.lang.String category -> i
    java.lang.String subCategory -> j
    java.lang.String type -> k
    java.lang.Integer impact -> l
    java.lang.Integer urgency -> m
    java.util.Map customFields -> n
    8:8:void <init>(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Long,java.lang.String,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer,java.util.Map) -> <init>
    8:8:java.lang.String toString() -> toString
    8:8:int hashCode() -> hashCode
    8:8:boolean equals(java.lang.Object) -> equals
    8:8:java.lang.String name() -> name
    8:8:java.lang.String email() -> email
    8:8:java.lang.String subject() -> subject
    8:8:java.lang.String description() -> description
    8:8:java.lang.Integer status() -> status
    8:8:java.lang.Integer priority() -> priority
    8:8:java.lang.Integer source() -> source
    8:8:java.lang.Long departmentId() -> departmentId
    8:8:java.lang.String category() -> category
    8:8:java.lang.String subCategory() -> subCategory
    8:8:java.lang.String type() -> type
    8:8:java.lang.Integer impact() -> impact
    8:8:java.lang.Integer urgency() -> urgency
    8:8:java.util.Map customFields() -> customFields
com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketResponse -> com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketResponse:
# {"fileName":"FreshserviceTicketResponse.java","id":"sourceFile"}
    java.lang.Long id -> a
    java.lang.String subject -> b
    java.lang.String description -> c
    java.lang.String descriptionText -> d
    java.lang.Integer status -> e
    java.lang.Integer priority -> f
    java.lang.Integer source -> g
    java.lang.Long requesterId -> h
    java.lang.Long responderId -> i
    java.lang.Long departmentId -> j
    java.lang.String category -> k
    java.lang.String subCategory -> l
    java.lang.String itemCategory -> m
    java.lang.String createdAt -> n
    java.lang.String updatedAt -> o
    java.lang.String dueBy -> p
    java.lang.String frDueBy -> q
    java.lang.Boolean isEscalated -> r
    java.lang.Boolean frEscalated -> s
    java.lang.Boolean deleted -> t
    java.lang.Boolean spam -> u
    java.lang.Long emailConfigId -> v
    java.lang.Long groupId -> w
    java.lang.Long workspaceId -> x
    java.lang.Long requestedForId -> y
    java.lang.String toEmails -> z
    java.util.List ccEmails -> A
    java.util.List bccEmails -> B
    java.util.List fwdEmails -> C
    java.util.List replyCcEmails -> D
    java.lang.String type -> E
    java.lang.Integer impact -> F
    java.lang.Integer urgency -> G
    java.lang.Integer tasksDependencyType -> H
    java.lang.Long slaPolicyId -> I
    java.lang.Long appliedBusinessHours -> J
    java.lang.Boolean createdWithinBusinessHours -> K
    java.lang.String resolutionNotes -> L
    java.lang.String resolutionNotesHtml -> M
    java.util.Map customFields -> N
    java.util.List attachments -> O
    136:137:void <init>() -> <init>
    141:141:java.lang.Long getId() -> getId
    145:146:void setId(java.lang.Long) -> setId
    149:149:java.lang.String getSubject() -> getSubject
    153:154:void setSubject(java.lang.String) -> setSubject
    157:157:java.lang.String getDescription() -> getDescription
    161:162:void setDescription(java.lang.String) -> setDescription
    165:165:java.lang.String getDescriptionText() -> getDescriptionText
    169:170:void setDescriptionText(java.lang.String) -> setDescriptionText
    173:173:java.lang.Integer getStatus() -> getStatus
    177:178:void setStatus(java.lang.Integer) -> setStatus
    181:181:java.lang.Integer getPriority() -> getPriority
    185:186:void setPriority(java.lang.Integer) -> setPriority
    189:189:java.lang.Integer getSource() -> getSource
    193:194:void setSource(java.lang.Integer) -> setSource
    197:197:java.lang.Long getRequesterId() -> getRequesterId
    201:202:void setRequesterId(java.lang.Long) -> setRequesterId
    205:205:java.lang.Long getResponderId() -> getResponderId
    209:210:void setResponderId(java.lang.Long) -> setResponderId
    213:213:java.lang.Long getDepartmentId() -> getDepartmentId
    217:218:void setDepartmentId(java.lang.Long) -> setDepartmentId
    221:221:java.lang.String getCategory() -> getCategory
    225:226:void setCategory(java.lang.String) -> setCategory
    229:229:java.lang.String getSubCategory() -> getSubCategory
    233:234:void setSubCategory(java.lang.String) -> setSubCategory
    237:237:java.lang.String getItemCategory() -> getItemCategory
    241:242:void setItemCategory(java.lang.String) -> setItemCategory
    245:245:java.lang.String getCreatedAt() -> getCreatedAt
    249:250:void setCreatedAt(java.lang.String) -> setCreatedAt
    253:253:java.lang.String getUpdatedAt() -> getUpdatedAt
    257:258:void setUpdatedAt(java.lang.String) -> setUpdatedAt
    261:261:java.lang.String getDueBy() -> getDueBy
    265:266:void setDueBy(java.lang.String) -> setDueBy
    269:269:java.lang.String getFrDueBy() -> getFrDueBy
    273:274:void setFrDueBy(java.lang.String) -> setFrDueBy
    277:277:java.lang.Boolean getIsEscalated() -> getIsEscalated
    281:282:void setIsEscalated(java.lang.Boolean) -> setIsEscalated
    285:285:java.lang.Boolean getFrEscalated() -> getFrEscalated
    289:290:void setFrEscalated(java.lang.Boolean) -> setFrEscalated
    293:293:java.lang.Boolean getDeleted() -> getDeleted
    297:298:void setDeleted(java.lang.Boolean) -> setDeleted
    301:301:java.lang.Boolean getSpam() -> getSpam
    305:306:void setSpam(java.lang.Boolean) -> setSpam
    309:309:java.lang.Long getEmailConfigId() -> getEmailConfigId
    313:314:void setEmailConfigId(java.lang.Long) -> setEmailConfigId
    317:317:java.lang.Long getGroupId() -> getGroupId
    321:322:void setGroupId(java.lang.Long) -> setGroupId
    325:325:java.lang.Long getWorkspaceId() -> getWorkspaceId
    329:330:void setWorkspaceId(java.lang.Long) -> setWorkspaceId
    333:333:java.lang.Long getRequestedForId() -> getRequestedForId
    337:338:void setRequestedForId(java.lang.Long) -> setRequestedForId
    341:341:java.lang.String getToEmails() -> getToEmails
    345:346:void setToEmails(java.lang.String) -> setToEmails
    349:349:java.util.List getCcEmails() -> getCcEmails
    353:354:void setCcEmails(java.util.List) -> setCcEmails
    357:357:java.util.List getBccEmails() -> getBccEmails
    361:362:void setBccEmails(java.util.List) -> setBccEmails
    365:365:java.util.List getFwdEmails() -> getFwdEmails
    369:370:void setFwdEmails(java.util.List) -> setFwdEmails
    373:373:java.util.List getReplyCcEmails() -> getReplyCcEmails
    377:378:void setReplyCcEmails(java.util.List) -> setReplyCcEmails
    381:381:java.lang.String getType() -> getType
    385:386:void setType(java.lang.String) -> setType
    389:389:java.lang.Integer getImpact() -> getImpact
    393:394:void setImpact(java.lang.Integer) -> setImpact
    397:397:java.lang.Integer getUrgency() -> getUrgency
    401:402:void setUrgency(java.lang.Integer) -> setUrgency
    405:405:java.lang.Integer getTasksDependencyType() -> getTasksDependencyType
    409:410:void setTasksDependencyType(java.lang.Integer) -> setTasksDependencyType
    413:413:java.lang.Long getSlaPolicyId() -> getSlaPolicyId
    417:418:void setSlaPolicyId(java.lang.Long) -> setSlaPolicyId
    421:421:java.lang.Long getAppliedBusinessHours() -> getAppliedBusinessHours
    425:426:void setAppliedBusinessHours(java.lang.Long) -> setAppliedBusinessHours
    429:429:java.lang.Boolean getCreatedWithinBusinessHours() -> getCreatedWithinBusinessHours
    433:434:void setCreatedWithinBusinessHours(java.lang.Boolean) -> setCreatedWithinBusinessHours
    437:437:java.lang.String getResolutionNotes() -> getResolutionNotes
    441:442:void setResolutionNotes(java.lang.String) -> setResolutionNotes
    445:445:java.lang.String getResolutionNotesHtml() -> getResolutionNotesHtml
    449:450:void setResolutionNotesHtml(java.lang.String) -> setResolutionNotesHtml
    453:453:java.util.Map getCustomFields() -> getCustomFields
    457:458:void setCustomFields(java.util.Map) -> setCustomFields
    461:461:java.util.List getAttachments() -> getAttachments
    465:466:void setAttachments(java.util.List) -> setAttachments
com.itcinfotech.windchill.complaints.exception.DuplicateComplaintException -> com.itcinfotech.windchill.complaints.exception.DuplicateComplaintException:
# {"fileName":"DuplicateComplaintException.java","id":"sourceFile"}
    5:6:void <init>(java.lang.String) -> <init>
com.itcinfotech.windchill.complaints.request.ComplaintDTO -> com.itcinfotech.windchill.complaints.request.ComplaintDTO:
# {"fileName":"ComplaintDTO.java","id":"sourceFile"}
    java.lang.String number -> a
    java.lang.String additionalInformation -> b
    java.lang.String name -> c
    com.itcinfotech.windchill.complaints.dto.HowReported howReported -> d
    boolean dateApproximate -> e
    com.itcinfotech.windchill.complaints.dto.EventLocation eventLocation -> f
    com.itcinfotech.windchill.complaints.dto.Circumstance circumstance -> g
    java.lang.String primaryCode -> h
    com.itcinfotech.windchill.complaints.dto.CountryOfOrigin countryOfOrigin -> i
    com.itcinfotech.windchill.complaints.dto.CountryOfEvent countryOfEvent -> j
    java.time.ZonedDateTime date -> k
    java.lang.String devicifyKey -> l
    java.lang.String summary -> m
    java.lang.String odataType -> n
    java.lang.String[] additionalRelatedProducts -> o
    java.lang.String primaryRelatedProduct -> p
    java.lang.String entryLocation -> q
    java.lang.String[] smallThumbnails -> r
    java.lang.String primaryRelatedPersonOrLocation -> s
    java.lang.String[] thumbnails -> t
    java.lang.String context -> u
    java.lang.String[] additionalRelatedPersonnelOrLocations -> v
    java.lang.String[] attachments -> w
    9:9:void <init>() -> <init>
com.itcinfotech.windchill.complaints.request.ComplaintRequest -> com.itcinfotech.windchill.complaints.request.ComplaintRequest:
# {"fileName":"ComplaintRequest.java","id":"sourceFile"}
    java.lang.String oDataType -> a
    java.lang.String additionalInformation -> b
    com.itcinfotech.windchill.complaints.dto.HowReported howReported -> c
    boolean dateApproximate -> d
    com.itcinfotech.windchill.complaints.dto.EventLocation eventLocation -> e
    com.itcinfotech.windchill.complaints.dto.CountryOfOrigin countryOfOrigin -> f
    com.itcinfotech.windchill.complaints.dto.CountryOfEvent countryOfEvent -> g
    java.lang.String summary -> h
    java.lang.String sourceIntakeSystem -> i
    java.lang.String sourceIntakeUserName -> j
    java.lang.String primaryCode -> k
    java.lang.String primaryCodePath -> l
    java.lang.String date -> m
    java.lang.String context -> n
    com.itcinfotech.windchill.complaints.dto.PrimaryRelatedPersonOrLocation primaryRelatedPersonOrLocation -> o
    java.util.List additionalRelatedPersonnelOrLocation -> p
    com.itcinfotech.windchill.complaints.dto.PrimaryRelatedProduct primaryRelatedProduct -> q
    java.lang.Integer noOfFiles -> r
    java.util.List attachments -> s
    10:10:void <init>(java.lang.String,java.lang.String,com.itcinfotech.windchill.complaints.dto.HowReported,boolean,com.itcinfotech.windchill.complaints.dto.EventLocation,com.itcinfotech.windchill.complaints.dto.CountryOfOrigin,com.itcinfotech.windchill.complaints.dto.CountryOfEvent,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.itcinfotech.windchill.complaints.dto.PrimaryRelatedPersonOrLocation,java.util.List,com.itcinfotech.windchill.complaints.dto.PrimaryRelatedProduct,java.lang.Integer,java.util.List) -> <init>
    9:9:java.lang.String toString() -> toString
    9:9:int hashCode() -> hashCode
    9:9:boolean equals(java.lang.Object) -> equals
    9:9:java.lang.String oDataType() -> oDataType
    9:9:java.lang.String additionalInformation() -> additionalInformation
    9:9:com.itcinfotech.windchill.complaints.dto.HowReported howReported() -> howReported
    9:9:boolean dateApproximate() -> dateApproximate
    9:9:com.itcinfotech.windchill.complaints.dto.EventLocation eventLocation() -> eventLocation
    9:9:com.itcinfotech.windchill.complaints.dto.CountryOfOrigin countryOfOrigin() -> countryOfOrigin
    9:9:com.itcinfotech.windchill.complaints.dto.CountryOfEvent countryOfEvent() -> countryOfEvent
    9:9:java.lang.String summary() -> summary
    9:9:java.lang.String sourceIntakeSystem() -> sourceIntakeSystem
    9:9:java.lang.String sourceIntakeUserName() -> sourceIntakeUserName
    9:9:java.lang.String primaryCode() -> primaryCode
    9:9:java.lang.String primaryCodePath() -> primaryCodePath
    9:9:java.lang.String date() -> date
    9:9:java.lang.String context() -> context
    9:9:com.itcinfotech.windchill.complaints.dto.PrimaryRelatedPersonOrLocation primaryRelatedPersonOrLocation() -> primaryRelatedPersonOrLocation
    9:9:java.util.List additionalRelatedPersonnelOrLocation() -> additionalRelatedPersonnelOrLocation
    9:9:com.itcinfotech.windchill.complaints.dto.PrimaryRelatedProduct primaryRelatedProduct() -> primaryRelatedProduct
    9:9:java.lang.Integer noOfFiles() -> noOfFiles
    9:9:java.util.List attachments() -> attachments
com.itcinfotech.windchill.complaints.request.FreshserviceTicketCreateRequest -> com.itcinfotech.windchill.complaints.request.FreshserviceTicketCreateRequest:
# {"fileName":"FreshserviceTicketCreateRequest.java","id":"sourceFile"}
    com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest ticketRequest -> a
    java.util.List attachments -> b
    14:15:void <init>() -> <init>
    17:20:void <init>(com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest,java.util.List) -> <init>
    24:24:com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest getTicketRequest() -> getTicketRequest
    28:29:void setTicketRequest(com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest) -> setTicketRequest
    32:32:java.util.List getAttachments() -> getAttachments
    36:37:void setAttachments(java.util.List) -> setAttachments
com.itcinfotech.windchill.complaints.request.MozarcComplaintRequest -> com.itcinfotech.windchill.complaints.request.MozarcComplaintRequest:
# {"fileName":"MozarcComplaintRequest.java","id":"sourceFile"}
    java.lang.String oDataType -> a
    java.lang.String sourceIntakeSystem -> b
    java.lang.String reporterName -> c
    java.lang.String complaintId -> d
    java.lang.String sourceIntakeUserName -> e
    com.itcinfotech.windchill.complaints.dto.HowReported howReported -> f
    java.lang.Boolean dateApproximate -> g
    com.itcinfotech.windchill.complaints.dto.EventLocation eventLocation -> h
    com.itcinfotech.windchill.complaints.dto.CountryOfOrigin countryOfOrigin -> i
    com.itcinfotech.windchill.complaints.dto.CountryOfEvent countryOfEvent -> j
    java.lang.String sourceComplaintContactEmail -> k
    java.lang.String sourceComplaintContactPhone -> l
    java.lang.String sourceComplaintCreationDate -> m
    java.lang.String sourcePatientInvolvement -> n
    java.lang.String sourcePatientImpactDescription -> o
    java.lang.String sourceIntervention -> p
    java.lang.String sourcePatientOutcome -> q
    java.lang.String date -> r
    java.lang.String summary -> s
    java.lang.String context -> t
    com.itcinfotech.windchill.complaints.dto.PrimaryRelatedPersonOrLocation primaryRelatedPersonOrLocation -> u
    java.util.List additionalRelatedPersonnelOrLocation -> v
    com.itcinfotech.windchill.complaints.dto.PrimaryRelatedProduct primaryRelatedProduct -> w
    java.util.List additionalRelatedProducts -> x
    java.util.List attachments -> y
    11:11:void <init>(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.itcinfotech.windchill.complaints.dto.HowReported,java.lang.Boolean,com.itcinfotech.windchill.complaints.dto.EventLocation,com.itcinfotech.windchill.complaints.dto.CountryOfOrigin,com.itcinfotech.windchill.complaints.dto.CountryOfEvent,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.itcinfotech.windchill.complaints.dto.PrimaryRelatedPersonOrLocation,java.util.List,com.itcinfotech.windchill.complaints.dto.PrimaryRelatedProduct,java.util.List,java.util.List) -> <init>
    10:10:java.lang.String toString() -> toString
    10:10:int hashCode() -> hashCode
    10:10:boolean equals(java.lang.Object) -> equals
    10:10:java.lang.String oDataType() -> oDataType
    10:10:java.lang.String sourceIntakeSystem() -> sourceIntakeSystem
    10:10:java.lang.String reporterName() -> reporterName
    10:10:java.lang.String complaintId() -> complaintId
    10:10:java.lang.String sourceIntakeUserName() -> sourceIntakeUserName
    10:10:com.itcinfotech.windchill.complaints.dto.HowReported howReported() -> howReported
    10:10:java.lang.Boolean dateApproximate() -> dateApproximate
    10:10:com.itcinfotech.windchill.complaints.dto.EventLocation eventLocation() -> eventLocation
    10:10:com.itcinfotech.windchill.complaints.dto.CountryOfOrigin countryOfOrigin() -> countryOfOrigin
    10:10:com.itcinfotech.windchill.complaints.dto.CountryOfEvent countryOfEvent() -> countryOfEvent
    10:10:java.lang.String sourceComplaintContactEmail() -> sourceComplaintContactEmail
    10:10:java.lang.String sourceComplaintContactPhone() -> sourceComplaintContactPhone
    10:10:java.lang.String sourceComplaintCreationDate() -> sourceComplaintCreationDate
    10:10:java.lang.String sourcePatientInvolvement() -> sourcePatientInvolvement
    10:10:java.lang.String sourcePatientImpactDescription() -> sourcePatientImpactDescription
    10:10:java.lang.String sourceIntervention() -> sourceIntervention
    10:10:java.lang.String sourcePatientOutcome() -> sourcePatientOutcome
    10:10:java.lang.String date() -> date
    10:10:java.lang.String summary() -> summary
    10:10:java.lang.String context() -> context
    10:10:com.itcinfotech.windchill.complaints.dto.PrimaryRelatedPersonOrLocation primaryRelatedPersonOrLocation() -> primaryRelatedPersonOrLocation
    10:10:java.util.List additionalRelatedPersonnelOrLocation() -> additionalRelatedPersonnelOrLocation
    10:10:com.itcinfotech.windchill.complaints.dto.PrimaryRelatedProduct primaryRelatedProduct() -> primaryRelatedProduct
    10:10:java.util.List additionalRelatedProducts() -> additionalRelatedProducts
    10:10:java.util.List attachments() -> attachments
com.itcinfotech.windchill.complaints.request.UploadRequestStage1 -> com.itcinfotech.windchill.complaints.request.UploadRequestStage1:
# {"fileName":"UploadRequestStage1.java","id":"sourceFile"}
    java.lang.Integer noOfFiles -> a
    6:6:void <init>(java.lang.Integer) -> <init>
    6:6:java.lang.String toString() -> toString
    6:6:int hashCode() -> hashCode
    6:6:boolean equals(java.lang.Object) -> equals
    6:6:java.lang.Integer noOfFiles() -> noOfFiles
com.itcinfotech.windchill.complaints.request.UploadRequestStage3 -> com.itcinfotech.windchill.complaints.request.UploadRequestStage3:
# {"fileName":"UploadRequestStage3.java","id":"sourceFile"}
    java.util.List contentInfo -> a
    9:9:void <init>(java.util.List) -> <init>
    9:9:java.lang.String toString() -> toString
    9:9:int hashCode() -> hashCode
    9:9:boolean equals(java.lang.Object) -> equals
    9:9:java.util.List contentInfo() -> contentInfo
com.itcinfotech.windchill.complaints.request.WCToken -> com.itcinfotech.windchill.complaints.request.WCToken:
# {"fileName":"WCToken.java","id":"sourceFile"}
    java.lang.String nonceKey -> a
    java.lang.String nonceValue -> b
    5:5:void <init>(java.lang.String,java.lang.String) -> <init>
    5:5:java.lang.String toString() -> toString
    5:5:int hashCode() -> hashCode
    5:5:boolean equals(java.lang.Object) -> equals
    5:5:java.lang.String nonceKey() -> nonceKey
    5:5:java.lang.String nonceValue() -> nonceValue
com.itcinfotech.windchill.complaints.response.ComplaintError -> com.itcinfotech.windchill.complaints.response.ComplaintError:
# {"fileName":"ComplaintError.java","id":"sourceFile"}
    java.lang.String code -> a
    java.lang.String message -> b
    3:3:void <init>(java.lang.String,java.lang.String) -> <init>
    3:3:java.lang.String toString() -> toString
    3:3:int hashCode() -> hashCode
    3:3:boolean equals(java.lang.Object) -> equals
    3:3:java.lang.String code() -> code
    3:3:java.lang.String message() -> message
com.itcinfotech.windchill.complaints.response.ComplaintErrorResponse -> com.itcinfotech.windchill.complaints.response.ComplaintErrorResponse:
# {"fileName":"ComplaintErrorResponse.java","id":"sourceFile"}
    com.itcinfotech.windchill.complaints.response.ComplaintError error -> a
    3:3:void <init>(com.itcinfotech.windchill.complaints.response.ComplaintError) -> <init>
    3:3:java.lang.String toString() -> toString
    3:3:int hashCode() -> hashCode
    3:3:boolean equals(java.lang.Object) -> equals
    3:3:com.itcinfotech.windchill.complaints.response.ComplaintError error() -> error
com.itcinfotech.windchill.complaints.response.ComplaintResponse -> com.itcinfotech.windchill.complaints.response.ComplaintResponse:
# {"fileName":"ComplaintResponse.java","id":"sourceFile"}
    java.lang.String odataContext -> a
    java.lang.String createdOn -> b
    java.lang.String id -> c
    java.lang.String lastModified -> d
    java.lang.String additionalInformation -> e
    com.itcinfotech.windchill.complaints.dto.Circumstance circumstance -> f
    com.itcinfotech.windchill.complaints.dto.CountryOfEvent countryOfEvent -> g
    com.itcinfotech.windchill.complaints.dto.CountryOfOrigin countryOfOrigin -> h
    java.lang.String createdBy -> i
    java.lang.String date -> j
    boolean dateApproximate -> k
    java.lang.String devicifyKey -> l
    com.itcinfotech.windchill.complaints.dto.EventLocation eventLocation -> m
    com.itcinfotech.windchill.complaints.dto.HowReported howReported -> n
    java.lang.String lifeCycleTemplateName -> o
    java.lang.String modifiedBy -> p
    java.lang.String name -> q
    java.lang.String number -> r
    java.lang.String objectType -> s
    java.lang.String primaryCode -> t
    java.lang.String primaryCodePath -> u
    com.itcinfotech.windchill.complaints.dto.State state -> v
    java.lang.String summary -> w
    com.itcinfotech.windchill.complaints.dto.TypeIcon typeIcon -> x
    java.lang.String localTimeZone -> y
    8:8:void <init>(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.itcinfotech.windchill.complaints.dto.Circumstance,com.itcinfotech.windchill.complaints.dto.CountryOfEvent,com.itcinfotech.windchill.complaints.dto.CountryOfOrigin,java.lang.String,java.lang.String,boolean,java.lang.String,com.itcinfotech.windchill.complaints.dto.EventLocation,com.itcinfotech.windchill.complaints.dto.HowReported,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.itcinfotech.windchill.complaints.dto.State,java.lang.String,com.itcinfotech.windchill.complaints.dto.TypeIcon,java.lang.String) -> <init>
    8:8:java.lang.String toString() -> toString
    8:8:int hashCode() -> hashCode
    8:8:boolean equals(java.lang.Object) -> equals
    8:8:java.lang.String odataContext() -> odataContext
    8:8:java.lang.String createdOn() -> createdOn
    8:8:java.lang.String id() -> id
    8:8:java.lang.String lastModified() -> lastModified
    8:8:java.lang.String additionalInformation() -> additionalInformation
    8:8:com.itcinfotech.windchill.complaints.dto.Circumstance circumstance() -> circumstance
    8:8:com.itcinfotech.windchill.complaints.dto.CountryOfEvent countryOfEvent() -> countryOfEvent
    8:8:com.itcinfotech.windchill.complaints.dto.CountryOfOrigin countryOfOrigin() -> countryOfOrigin
    8:8:java.lang.String createdBy() -> createdBy
    8:8:java.lang.String date() -> date
    8:8:boolean dateApproximate() -> dateApproximate
    8:8:java.lang.String devicifyKey() -> devicifyKey
    8:8:com.itcinfotech.windchill.complaints.dto.EventLocation eventLocation() -> eventLocation
    8:8:com.itcinfotech.windchill.complaints.dto.HowReported howReported() -> howReported
    8:8:java.lang.String lifeCycleTemplateName() -> lifeCycleTemplateName
    8:8:java.lang.String modifiedBy() -> modifiedBy
    8:8:java.lang.String name() -> name
    8:8:java.lang.String number() -> number
    8:8:java.lang.String objectType() -> objectType
    8:8:java.lang.String primaryCode() -> primaryCode
    8:8:java.lang.String primaryCodePath() -> primaryCodePath
    8:8:com.itcinfotech.windchill.complaints.dto.State state() -> state
    8:8:java.lang.String summary() -> summary
    8:8:com.itcinfotech.windchill.complaints.dto.TypeIcon typeIcon() -> typeIcon
    8:8:java.lang.String localTimeZone() -> localTimeZone
com.itcinfotech.windchill.complaints.response.ContainerResponse -> com.itcinfotech.windchill.complaints.response.ContainerResponse:
# {"fileName":"ContainerResponse.java","id":"sourceFile"}
    java.lang.String odataContext -> a
    java.util.List value -> b
    java.lang.String localTimeZone -> c
    java.lang.String oDataNextLink -> d
    9:9:void <init>(java.lang.String,java.util.List,java.lang.String,java.lang.String) -> <init>
    9:9:java.lang.String toString() -> toString
    9:9:int hashCode() -> hashCode
    9:9:boolean equals(java.lang.Object) -> equals
    9:9:java.lang.String odataContext() -> odataContext
    9:9:java.util.List value() -> value
    9:9:java.lang.String localTimeZone() -> localTimeZone
    9:9:java.lang.String oDataNextLink() -> oDataNextLink
com.itcinfotech.windchill.complaints.response.ManufacturingResponse -> com.itcinfotech.windchill.complaints.response.ManufacturingResponse:
# {"fileName":"ManufacturingResponse.java","id":"sourceFile"}
    java.lang.String odataContext -> a
    java.util.List value -> b
    java.lang.String localTimeZone -> c
    java.lang.String oDataNextLink -> d
    10:10:void <init>(java.lang.String,java.util.List,java.lang.String,java.lang.String) -> <init>
    10:10:java.lang.String toString() -> toString
    10:10:int hashCode() -> hashCode
    10:10:boolean equals(java.lang.Object) -> equals
    10:10:java.lang.String odataContext() -> odataContext
    10:10:java.util.List value() -> value
    10:10:java.lang.String localTimeZone() -> localTimeZone
    10:10:java.lang.String oDataNextLink() -> oDataNextLink
com.itcinfotech.windchill.complaints.response.MozarcComplaintListResponse -> com.itcinfotech.windchill.complaints.response.MozarcComplaintListResponse:
# {"fileName":"MozarcComplaintListResponse.java","id":"sourceFile"}
    java.lang.String odataContext -> a
    java.util.List value -> b
    java.lang.String localTimeZone -> c
    java.lang.String oDataNextLink -> d
    10:10:void <init>(java.lang.String,java.util.List,java.lang.String,java.lang.String) -> <init>
    10:10:java.lang.String toString() -> toString
    10:10:int hashCode() -> hashCode
    10:10:boolean equals(java.lang.Object) -> equals
    10:10:java.lang.String odataContext() -> odataContext
    10:10:java.util.List value() -> value
    10:10:java.lang.String localTimeZone() -> localTimeZone
    10:10:java.lang.String oDataNextLink() -> oDataNextLink
com.itcinfotech.windchill.complaints.response.MozarcComplaintResponse -> com.itcinfotech.windchill.complaints.response.MozarcComplaintResponse:
# {"fileName":"MozarcComplaintResponse.java","id":"sourceFile"}
    java.lang.String ptcSourceIntakeSystem -> a
    com.itcinfotech.windchill.complaints.dto.EventLocation eventLocation -> b
    java.lang.String modifiedBy -> c
    java.lang.String additionalInformation -> d
    java.lang.String ptcCloudCode -> e
    boolean dateApproximate -> f
    java.lang.String ptcCloudRrDueDate -> g
    java.lang.String ptcCloudRrDecisionType -> h
    java.lang.String ptcCloudRegulatoryRptId -> i
    java.lang.String lastModified -> j
    java.lang.String ptcCloudProductEventStatus -> k
    java.lang.String lifeCycleTemplateName -> l
    java.lang.String ptcCloudReportTableDecisionId -> m
    java.lang.String date -> n
    java.lang.String ptcCloudFdaCode -> o
    java.lang.String objectType -> p
    java.lang.String summary -> q
    java.lang.String ptcCloudComplaint -> r
    java.lang.String createdOn -> s
    java.lang.String id -> t
    java.lang.Boolean ptcCloudReportTable -> u
    java.lang.String ptcCloudProductEvent -> v
    java.lang.String number -> w
    java.lang.String ptcCloudLineItemNo -> x
    java.lang.String ptcCloudInvestigationDecisionMadeBy -> y
    java.lang.String ptcCloudNcitCode -> z
    java.lang.String ptcCloudRrTimeLine -> A
    com.itcinfotech.windchill.complaints.dto.HowReported howReported -> B
    java.lang.String primaryCode -> C
    java.lang.String ptcCloudRrDateSubmitted -> D
    java.lang.String ptcCloudLot -> E
    java.lang.String ptcCloudComplaintSourceSystemID -> F
    java.lang.String ptcCloudInvestigationId -> G
    java.lang.String ptcCloudInvestigationStatus -> H
    java.lang.String name -> I
    java.lang.String ptcCloudRegulatoryBody -> J
    java.lang.String ptccloudRrStatus -> K
    java.lang.String ptccloudMfgSiteId -> L
    java.lang.String ptccloudRdDecisionMadeBy -> M
    java.lang.String ptccloudImplantDate -> N
    java.lang.String ptccloudRdDecisionType -> O
    java.lang.String ptccloudRdDecisionDate -> P
    java.lang.String ptccloudRegulatoryReportNo -> Q
    java.lang.String ptccloudInvestigationReq -> R
    java.lang.String createdBy -> S
    java.lang.String ptccloudReportabilityGrp -> T
    com.itcinfotech.windchill.complaints.dto.Circumstance circumstance -> U
    java.lang.String ptccloudProductEventType -> V
    java.lang.String testAgeComplaint -> W
    com.itcinfotech.windchill.complaints.dto.CountryOfOrigin countryOfOrigin -> X
    java.lang.String ptccloudCodeType -> Y
    java.lang.String ptccloudRdStatus -> Z
    java.lang.String primaryCodePath -> aa
    java.lang.String ptccloudInvDecisionDate -> ab
    java.lang.String ptccloudInvExplantDate -> ac
    java.lang.String ptccloudSerialNo -> ad
    java.lang.String ptccloudNotifiedDate -> ae
    java.lang.String ptccloudSourceIntakeUserName -> af
    java.lang.String ptccloudInvCompletedDate -> ag
    java.lang.String ptccloudSourceIntakeRecordID -> ah
    java.lang.String ptccloudSourceComplaintReporterName -> ai
    java.lang.String sourceComplaintContactEmail -> aj
    java.lang.String sourceComplaintContactPhone -> ak
    java.lang.String sourcePatientInvolvement -> al
    java.lang.String sourcePatientImpactDescription -> am
    java.lang.String sourceIntervention -> an
    java.lang.String sourcePatientOutcome -> ao
    java.lang.String sourceComplaintCreationDate -> ap
    com.itcinfotech.windchill.complaints.dto.CountryOfEvent countryOfEvent -> aq
    com.itcinfotech.windchill.complaints.dto.State state -> ar
    com.itcinfotech.windchill.complaints.dto.TypeIcon typeIcon -> as
    java.lang.String odataContext -> at
    java.lang.String localTimeZone -> au
    7:7:void <init>(java.lang.String,com.itcinfotech.windchill.complaints.dto.EventLocation,java.lang.String,java.lang.String,java.lang.String,boolean,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Boolean,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.itcinfotech.windchill.complaints.dto.HowReported,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.itcinfotech.windchill.complaints.dto.Circumstance,java.lang.String,java.lang.String,com.itcinfotech.windchill.complaints.dto.CountryOfOrigin,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.itcinfotech.windchill.complaints.dto.CountryOfEvent,com.itcinfotech.windchill.complaints.dto.State,com.itcinfotech.windchill.complaints.dto.TypeIcon,java.lang.String,java.lang.String) -> <init>
    7:7:java.lang.String toString() -> toString
    7:7:int hashCode() -> hashCode
    7:7:boolean equals(java.lang.Object) -> equals
    7:7:java.lang.String ptcSourceIntakeSystem() -> ptcSourceIntakeSystem
    7:7:com.itcinfotech.windchill.complaints.dto.EventLocation eventLocation() -> eventLocation
    7:7:java.lang.String modifiedBy() -> modifiedBy
    7:7:java.lang.String additionalInformation() -> additionalInformation
    7:7:java.lang.String ptcCloudCode() -> ptcCloudCode
    7:7:boolean dateApproximate() -> dateApproximate
    7:7:java.lang.String ptcCloudRrDueDate() -> ptcCloudRrDueDate
    7:7:java.lang.String ptcCloudRrDecisionType() -> ptcCloudRrDecisionType
    7:7:java.lang.String ptcCloudRegulatoryRptId() -> ptcCloudRegulatoryRptId
    7:7:java.lang.String lastModified() -> lastModified
    7:7:java.lang.String ptcCloudProductEventStatus() -> ptcCloudProductEventStatus
    7:7:java.lang.String lifeCycleTemplateName() -> lifeCycleTemplateName
    7:7:java.lang.String ptcCloudReportTableDecisionId() -> ptcCloudReportTableDecisionId
    7:7:java.lang.String date() -> date
    7:7:java.lang.String ptcCloudFdaCode() -> ptcCloudFdaCode
    7:7:java.lang.String objectType() -> objectType
    7:7:java.lang.String summary() -> summary
    7:7:java.lang.String ptcCloudComplaint() -> ptcCloudComplaint
    7:7:java.lang.String createdOn() -> createdOn
    7:7:java.lang.String id() -> id
    7:7:java.lang.Boolean ptcCloudReportTable() -> ptcCloudReportTable
    7:7:java.lang.String ptcCloudProductEvent() -> ptcCloudProductEvent
    7:7:java.lang.String number() -> number
    7:7:java.lang.String ptcCloudLineItemNo() -> ptcCloudLineItemNo
    7:7:java.lang.String ptcCloudInvestigationDecisionMadeBy() -> ptcCloudInvestigationDecisionMadeBy
    7:7:java.lang.String ptcCloudNcitCode() -> ptcCloudNcitCode
    7:7:java.lang.String ptcCloudRrTimeLine() -> ptcCloudRrTimeLine
    7:7:com.itcinfotech.windchill.complaints.dto.HowReported howReported() -> howReported
    7:7:java.lang.String primaryCode() -> primaryCode
    7:7:java.lang.String ptcCloudRrDateSubmitted() -> ptcCloudRrDateSubmitted
    7:7:java.lang.String ptcCloudLot() -> ptcCloudLot
    7:7:java.lang.String ptcCloudComplaintSourceSystemID() -> ptcCloudComplaintSourceSystemID
    7:7:java.lang.String ptcCloudInvestigationId() -> ptcCloudInvestigationId
    7:7:java.lang.String ptcCloudInvestigationStatus() -> ptcCloudInvestigationStatus
    7:7:java.lang.String name() -> name
    7:7:java.lang.String ptcCloudRegulatoryBody() -> ptcCloudRegulatoryBody
    7:7:java.lang.String ptccloudRrStatus() -> ptccloudRrStatus
    7:7:java.lang.String ptccloudMfgSiteId() -> ptccloudMfgSiteId
    7:7:java.lang.String ptccloudRdDecisionMadeBy() -> ptccloudRdDecisionMadeBy
    7:7:java.lang.String ptccloudImplantDate() -> ptccloudImplantDate
    7:7:java.lang.String ptccloudRdDecisionType() -> ptccloudRdDecisionType
    7:7:java.lang.String ptccloudRdDecisionDate() -> ptccloudRdDecisionDate
    7:7:java.lang.String ptccloudRegulatoryReportNo() -> ptccloudRegulatoryReportNo
    7:7:java.lang.String ptccloudInvestigationReq() -> ptccloudInvestigationReq
    7:7:java.lang.String createdBy() -> createdBy
    7:7:java.lang.String ptccloudReportabilityGrp() -> ptccloudReportabilityGrp
    7:7:com.itcinfotech.windchill.complaints.dto.Circumstance circumstance() -> circumstance
    7:7:java.lang.String ptccloudProductEventType() -> ptccloudProductEventType
    7:7:java.lang.String testAgeComplaint() -> testAgeComplaint
    7:7:com.itcinfotech.windchill.complaints.dto.CountryOfOrigin countryOfOrigin() -> countryOfOrigin
    7:7:java.lang.String ptccloudCodeType() -> ptccloudCodeType
    7:7:java.lang.String ptccloudRdStatus() -> ptccloudRdStatus
    7:7:java.lang.String primaryCodePath() -> primaryCodePath
    7:7:java.lang.String ptccloudInvDecisionDate() -> ptccloudInvDecisionDate
    7:7:java.lang.String ptccloudInvExplantDate() -> ptccloudInvExplantDate
    7:7:java.lang.String ptccloudSerialNo() -> ptccloudSerialNo
    7:7:java.lang.String ptccloudNotifiedDate() -> ptccloudNotifiedDate
    7:7:java.lang.String ptccloudSourceIntakeUserName() -> ptccloudSourceIntakeUserName
    7:7:java.lang.String ptccloudInvCompletedDate() -> ptccloudInvCompletedDate
    7:7:java.lang.String ptccloudSourceIntakeRecordID() -> ptccloudSourceIntakeRecordID
    7:7:java.lang.String ptccloudSourceComplaintReporterName() -> ptccloudSourceComplaintReporterName
    7:7:java.lang.String sourceComplaintContactEmail() -> sourceComplaintContactEmail
    7:7:java.lang.String sourceComplaintContactPhone() -> sourceComplaintContactPhone
    7:7:java.lang.String sourcePatientInvolvement() -> sourcePatientInvolvement
    7:7:java.lang.String sourcePatientImpactDescription() -> sourcePatientImpactDescription
    7:7:java.lang.String sourceIntervention() -> sourceIntervention
    7:7:java.lang.String sourcePatientOutcome() -> sourcePatientOutcome
    7:7:java.lang.String sourceComplaintCreationDate() -> sourceComplaintCreationDate
    7:7:com.itcinfotech.windchill.complaints.dto.CountryOfEvent countryOfEvent() -> countryOfEvent
    7:7:com.itcinfotech.windchill.complaints.dto.State state() -> state
    7:7:com.itcinfotech.windchill.complaints.dto.TypeIcon typeIcon() -> typeIcon
    7:7:java.lang.String odataContext() -> odataContext
    7:7:java.lang.String localTimeZone() -> localTimeZone
com.itcinfotech.windchill.complaints.response.PartResponse -> com.itcinfotech.windchill.complaints.response.PartResponse:
# {"fileName":"PartResponse.java","id":"sourceFile"}
    java.lang.String odataContext -> a
    java.util.List value -> b
    java.lang.String localTimeZone -> c
    java.lang.String oDataNextLink -> d
    10:10:void <init>(java.lang.String,java.util.List,java.lang.String,java.lang.String) -> <init>
    10:10:java.lang.String toString() -> toString
    10:10:int hashCode() -> hashCode
    10:10:boolean equals(java.lang.Object) -> equals
    10:10:java.lang.String odataContext() -> odataContext
    10:10:java.util.List value() -> value
    10:10:java.lang.String localTimeZone() -> localTimeZone
    10:10:java.lang.String oDataNextLink() -> oDataNextLink
com.itcinfotech.windchill.complaints.response.PeopleResponse -> com.itcinfotech.windchill.complaints.response.PeopleResponse:
# {"fileName":"PeopleResponse.java","id":"sourceFile"}
    java.lang.String odataContext -> a
    java.util.List value -> b
    java.lang.String localTimeZone -> c
    java.lang.String oDataNextLink -> d
    9:9:void <init>(java.lang.String,java.util.List,java.lang.String,java.lang.String) -> <init>
    9:9:java.lang.String toString() -> toString
    9:9:int hashCode() -> hashCode
    9:9:boolean equals(java.lang.Object) -> equals
    9:9:java.lang.String odataContext() -> odataContext
    9:9:java.util.List value() -> value
    9:9:java.lang.String localTimeZone() -> localTimeZone
    9:9:java.lang.String oDataNextLink() -> oDataNextLink
com.itcinfotech.windchill.complaints.response.UploadResponseStage1 -> com.itcinfotech.windchill.complaints.response.UploadResponseStage1:
# {"fileName":"UploadResponseStage1.java","id":"sourceFile"}
    java.lang.String context -> a
    java.util.List value -> b
    java.lang.String localTimeZone -> c
    9:9:void <init>(java.lang.String,java.util.List,java.lang.String) -> <init>
    9:9:java.lang.String toString() -> toString
    9:9:int hashCode() -> hashCode
    9:9:boolean equals(java.lang.Object) -> equals
    9:9:java.lang.String context() -> context
    9:9:java.util.List value() -> value
    9:9:java.lang.String localTimeZone() -> localTimeZone
com.itcinfotech.windchill.complaints.response.UploadResponseStage2 -> com.itcinfotech.windchill.complaints.response.UploadResponseStage2:
# {"fileName":"UploadResponseStage2.java","id":"sourceFile"}
    java.util.List contentInfos -> a
    8:8:void <init>(java.util.List) -> <init>
    8:8:java.lang.String toString() -> toString
    8:8:int hashCode() -> hashCode
    8:8:boolean equals(java.lang.Object) -> equals
    8:8:java.util.List contentInfos() -> contentInfos
com.itcinfotech.windchill.complaints.response.UploadResponseStage3 -> com.itcinfotech.windchill.complaints.response.UploadResponseStage3:
# {"fileName":"UploadResponseStage3.java","id":"sourceFile"}
    java.lang.String oDataContext -> a
    java.lang.String localTimeZone -> b
    java.util.List value -> c
    10:10:void <init>(java.lang.String,java.lang.String,java.util.List) -> <init>
    10:10:java.lang.String toString() -> toString
    10:10:int hashCode() -> hashCode
    10:10:boolean equals(java.lang.Object) -> equals
    10:10:java.lang.String oDataContext() -> oDataContext
    10:10:java.lang.String localTimeZone() -> localTimeZone
    10:10:java.util.List value() -> value
com.itcinfotech.windchill.complaints.service.AuthenticationService -> com.itcinfotech.windchill.complaints.service.AuthenticationService:
# {"fileName":"AuthenticationService.java","id":"sourceFile"}
    org.slf4j.Logger logger -> a
    java.lang.String authType -> b
    java.lang.String basicAuthUser -> c
    java.lang.String basicAuthPassword -> d
    com.itcinfotech.windchill.complaints.service.BearerTokenService bearerTokenService -> e
    33:35:void <init>(com.itcinfotech.windchill.complaints.service.BearerTokenService) -> <init>
    43:48:java.lang.String getAuthorizationHeader() -> getAuthorizationHeader
    58:58:boolean isBearerTokenAuth() -> isBearerTokenAuth
    67:67:boolean isBasicAuth() -> isBasicAuth
    76:76:java.lang.String getAuthType() -> getAuthType
    86:86:java.lang.String encodeBase64(java.lang.String) -> a
    19:19:void <clinit>() -> <clinit>
com.itcinfotech.windchill.complaints.service.AzureBlobService -> com.itcinfotech.windchill.complaints.service.AzureBlobService:
# {"fileName":"AzureBlobService.java","id":"sourceFile"}
    org.slf4j.Logger logger -> a
    java.lang.String localDownloadDir -> b
    java.lang.String endpoint -> c
    java.lang.String erpFilesDir -> d
    java.lang.String container -> e
    java.lang.String XML_SUFFIX -> f
    java.lang.String JSON_SUFFIX -> g
    com.azure.storage.blob.BlobServiceClient blobServiceClient -> h
    com.azure.storage.blob.BlobContainerClient containerClient -> i
    java.lang.Object lock -> j
    18:39:void <init>() -> <init>
    43:44:void init() -> init
    47:63:void connect() -> a
    66:78:java.util.ArrayList listESIItems() -> listESIItems
    83:101:java.util.ArrayList downloadNewFiles() -> downloadNewFiles
    105:108:void handleDownloadedFile(java.lang.String,java.lang.String) -> a
    111:130:boolean downloadBlobItem(com.azure.storage.blob.models.BlobItem,java.lang.String) -> a
    135:135:java.util.ArrayList filterPrevDownloadedFiles(java.util.ArrayList) -> a
    139:139:java.lang.String getFilenameFromBlobName(java.lang.String) -> a
    143:158:java.util.ArrayList listAllItems() -> listAllItems
    20:20:void <clinit>() -> <clinit>
com.itcinfotech.windchill.complaints.service.BearerTokenService -> com.itcinfotech.windchill.complaints.service.BearerTokenService:
# {"fileName":"BearerTokenService.java","id":"sourceFile"}
    org.slf4j.Logger logger -> a
    java.lang.String tokenUrl -> b
    java.lang.String clientId -> c
    java.lang.String clientSecret -> d
    java.lang.String grantType -> e
    java.lang.String scope -> f
    org.springframework.web.client.RestTemplate restTemplate -> g
    com.fasterxml.jackson.databind.ObjectMapper objectMapper -> h
    java.util.concurrent.atomic.AtomicReference bearerToken -> i
    long tokenExpiryTime -> j
    37:43:void <init>(org.springframework.web.client.RestTemplate,com.fasterxml.jackson.databind.ObjectMapper) -> <init>
    55:56:void run(java.lang.String[]) -> run
    65:68:java.lang.String getBearerToken() -> getBearerToken
    81:108:void fetchAndStoreToken() -> a
    18:18:void <clinit>() -> <clinit>
com.itcinfotech.windchill.complaints.service.ComplaintService -> com.itcinfotech.windchill.complaints.service.ComplaintService:
# {"fileName":"ComplaintService.java","id":"sourceFile"}
    java.lang.String host -> a
    java.lang.String apiUrl -> b
    java.lang.String integrationRootDir -> c
    com.itcinfotech.windchill.complaints.service.CsrfTokenService csrfTokenService -> d
    org.springframework.web.client.RestTemplate restTemplate -> e
    com.fasterxml.jackson.databind.ObjectMapper objectMapper -> f
    org.slf4j.Logger logger -> g
    com.itcinfotech.windchill.complaints.service.UploadService uploadService -> h
    com.itcinfotech.windchill.complaints.service.ContainerService containerService -> i
    com.itcinfotech.windchill.complaints.service.PartService partService -> j
    com.itcinfotech.windchill.complaints.service.ManufacturingService manufacturingService -> k
    com.itcinfotech.windchill.complaints.service.PeopleService peopleService -> l
    com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketService freshserviceTicketService -> m
    com.itcinfotech.windchill.complaints.service.AuthenticationService authenticationService -> n
    84:88:void <init>(org.springframework.web.client.RestTemplate,com.itcinfotech.windchill.complaints.service.CsrfTokenService,com.fasterxml.jackson.databind.ObjectMapper,com.itcinfotech.windchill.complaints.service.UploadService) -> <init>
    91:117:org.springframework.http.ResponseEntity createComplaintFromJson(com.itcinfotech.windchill.complaints.request.MozarcComplaintRequest,java.lang.Integer) -> createComplaintFromJson
    123:326:java.util.List extractDataFromCsv(java.io.File,java.io.File) -> extractDataFromCsv
    331:368:org.springframework.http.ResponseEntity createComplaintFromCsv(java.util.List,java.io.File,boolean) -> createComplaintFromCsv
    372:377:org.springframework.http.HttpHeaders buildHeaders(java.lang.String) -> a
    382:396:java.lang.String checkComplaint(java.lang.String) -> checkComplaint
    400:428:void cleanAndMoveZip(java.lang.String,java.lang.String) -> cleanAndMoveZip
    431:443:void deleteRecursively(java.nio.file.Path) -> a
    446:469:void moveZipAndCleanProcessing(java.lang.String,boolean) -> moveZipAndCleanProcessing
    475:480:java.lang.String buildDescriptionFromComplaintIds(java.util.List) -> a
    485:500:com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest buildTicketRequest(java.util.List) -> b
    506:510:org.springframework.web.multipart.MultipartFile convertFileToMultipart(java.io.File) -> convertFileToMultipart
    515:517:com.itcinfotech.windchill.complaints.request.FreshserviceTicketCreateRequest buildTicketWithAttachment(java.util.List,java.io.File) -> buildTicketWithAttachment
    522:536:void openFreshserviceTicketForComplaints(java.util.List,java.io.File) -> openFreshserviceTicketForComplaints
    546:562:void openFreshserviceTicketForErrors(java.util.List,java.util.List,java.util.List,java.io.File) -> openFreshserviceTicketForErrors
    566:578:java.io.File getZipFileFromDirectory(java.lang.String) -> getZipFileFromDirectory
    590:624:com.itcinfotech.windchill.complaints.request.MozarcComplaintRequest createErrorComplaintRequest(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,int) -> a
    632:644:com.itcinfotech.windchill.complaints.request.MozarcComplaintRequest createBasicErrorComplaintRequest(java.lang.String,java.lang.String,int) -> a
    656:700:java.lang.String buildDetailedErrorDescription(java.util.List,java.util.List,java.util.List) -> a
    709:728:com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest buildErrorTicketRequest(java.util.List,java.util.List,java.util.List) -> b
    740:742:com.itcinfotech.windchill.complaints.request.FreshserviceTicketCreateRequest buildTicketWithAttachmentForErrors(java.util.List,java.util.List,java.util.List,java.io.File) -> buildTicketWithAttachmentForErrors
    437:442:void lambda$deleteRecursively$0(java.nio.file.Path) -> b
    60:60:void <clinit>() -> <clinit>
com.itcinfotech.windchill.complaints.service.ContainerService -> com.itcinfotech.windchill.complaints.service.ContainerService:
# {"fileName":"ContainerService.java","id":"sourceFile"}
    java.lang.String host -> a
    java.lang.String apiUrl -> b
    com.itcinfotech.windchill.complaints.service.CsrfTokenService csrfTokenService -> c
    org.springframework.web.client.RestTemplate restTemplate -> d
    com.fasterxml.jackson.databind.ObjectMapper objectMapper -> e
    com.itcinfotech.windchill.complaints.service.AuthenticationService authenticationService -> f
    42:47:void <init>(org.springframework.web.client.RestTemplate,com.itcinfotech.windchill.complaints.service.CsrfTokenService,com.fasterxml.jackson.databind.ObjectMapper,com.itcinfotech.windchill.complaints.service.AuthenticationService) -> <init>
    50:74:java.lang.String getContainerID(java.lang.String) -> getContainerID
com.itcinfotech.windchill.complaints.service.CsrfTokenService -> com.itcinfotech.windchill.complaints.service.CsrfTokenService:
# {"fileName":"CsrfTokenService.java","id":"sourceFile"}
    org.springframework.web.client.RestTemplate restTemplate -> a
    com.fasterxml.jackson.databind.ObjectMapper objectMapper -> b
    java.lang.String host -> c
    java.lang.String baseUrl -> d
    com.itcinfotech.windchill.complaints.service.AuthenticationService authenticationService -> e
    26:30:void <init>(org.springframework.web.client.RestTemplate,com.fasterxml.jackson.databind.ObjectMapper,com.itcinfotech.windchill.complaints.service.AuthenticationService) -> <init>
    34:45:java.lang.String getCsrfToken() -> getCsrfToken
com.itcinfotech.windchill.complaints.service.ManufacturingService -> com.itcinfotech.windchill.complaints.service.ManufacturingService:
# {"fileName":"ManufacturingService.java","id":"sourceFile"}
    java.lang.String host -> a
    java.lang.String apiUrl -> b
    com.itcinfotech.windchill.complaints.service.CsrfTokenService csrfTokenService -> c
    org.springframework.web.client.RestTemplate restTemplate -> d
    com.fasterxml.jackson.databind.ObjectMapper objectMapper -> e
    com.itcinfotech.windchill.complaints.service.AuthenticationService authenticationService -> f
    31:36:void <init>(org.springframework.web.client.RestTemplate,com.itcinfotech.windchill.complaints.service.CsrfTokenService,com.fasterxml.jackson.databind.ObjectMapper,com.itcinfotech.windchill.complaints.service.AuthenticationService) -> <init>
    39:64:java.lang.String getManufacturingLocationID(java.lang.String) -> getManufacturingLocationID
com.itcinfotech.windchill.complaints.service.PartService -> com.itcinfotech.windchill.complaints.service.PartService:
# {"fileName":"PartService.java","id":"sourceFile"}
    java.lang.String host -> a
    java.lang.String apiUrl -> b
    com.itcinfotech.windchill.complaints.service.CsrfTokenService csrfTokenService -> c
    org.springframework.web.client.RestTemplate restTemplate -> d
    com.fasterxml.jackson.databind.ObjectMapper objectMapper -> e
    com.itcinfotech.windchill.complaints.service.AuthenticationService authenticationService -> f
    com.itcinfotech.windchill.complaints.service.ManufacturingService manufacturingService -> g
    38:43:void <init>(org.springframework.web.client.RestTemplate,com.itcinfotech.windchill.complaints.service.CsrfTokenService,com.fasterxml.jackson.databind.ObjectMapper,com.itcinfotech.windchill.complaints.service.AuthenticationService) -> <init>
    47:62:java.util.List getAdditionalProducts(java.lang.String[]) -> getAdditionalProducts
    66:90:java.lang.String getPartID(java.lang.String) -> getPartID
com.itcinfotech.windchill.complaints.service.PeopleService -> com.itcinfotech.windchill.complaints.service.PeopleService:
# {"fileName":"PeopleService.java","id":"sourceFile"}
    java.lang.String host -> a
    java.lang.String apiUrl -> b
    com.itcinfotech.windchill.complaints.service.CsrfTokenService csrfTokenService -> c
    org.springframework.web.client.RestTemplate restTemplate -> d
    com.fasterxml.jackson.databind.ObjectMapper objectMapper -> e
    com.itcinfotech.windchill.complaints.service.AuthenticationService authenticationService -> f
    33:38:void <init>(org.springframework.web.client.RestTemplate,com.itcinfotech.windchill.complaints.service.CsrfTokenService,com.fasterxml.jackson.databind.ObjectMapper,com.itcinfotech.windchill.complaints.service.AuthenticationService) -> <init>
    41:71:java.lang.String getPatientID(java.lang.String,java.lang.String) -> getPatientID
    77:84:java.util.List getAdditionalPeople(java.lang.String) -> getAdditionalPeople
com.itcinfotech.windchill.complaints.service.UploadService -> com.itcinfotech.windchill.complaints.service.UploadService:
# {"fileName":"UploadService.java","id":"sourceFile"}
    java.lang.String host -> a
    java.lang.String stage1Url -> b
    java.lang.String stage3Url -> c
    java.lang.String stage1Action -> d
    java.lang.String stage3Action -> e
    org.springframework.web.client.RestTemplate restTemplate -> f
    com.fasterxml.jackson.databind.ObjectMapper objectMapper -> g
    com.itcinfotech.windchill.complaints.service.AuthenticationService authenticationService -> h
    java.lang.String mimeType -> i
    53:66:void <init>(org.springframework.web.client.RestTemplate,com.fasterxml.jackson.databind.ObjectMapper,com.itcinfotech.windchill.complaints.service.AuthenticationService) -> <init>
    70:107:org.springframework.http.ResponseEntity uploadStage1(com.itcinfotech.windchill.complaints.request.UploadRequestStage1,java.lang.String,java.lang.String,java.lang.String,java.util.List) -> uploadStage1
    117:174:org.springframework.http.ResponseEntity uploadStage2(java.lang.String,java.lang.String,java.util.List,java.lang.String,java.lang.String,java.lang.String,java.lang.String) -> uploadStage2
    180:261:org.springframework.http.ResponseEntity uploadStage3(java.lang.String,java.util.List,java.util.List,java.lang.String,java.lang.String,java.lang.String,java.util.List) -> uploadStage3
    270:285:java.lang.StringBuilder getStringBuilder(java.net.HttpURLConnection) -> a
    294:326:void getFiles(java.util.List,java.io.OutputStream,java.lang.String,java.lang.String[],java.util.List,java.util.List) -> a
    331:336:org.springframework.web.multipart.MultipartFile createMultipartFile(java.io.File) -> a
    342:342:java.lang.String getBoundary() -> a
    346:346:java.lang.String getFirstBoundary() -> b
    350:362:void writeFormField(java.io.DataOutputStream,java.lang.String,java.lang.String) -> a
    366:391:void writeFileField2(java.io.DataOutputStream,java.lang.String,java.io.File) -> a
    395:420:void writeFileField(java.io.DataOutputStream,java.lang.String,java.io.File) -> b
    424:428:java.lang.String getMimeType(java.lang.String) -> a
    436:442:java.util.List createFilesList() -> c
    446:457:java.util.List getFilesList(java.lang.String,java.util.List) -> a
    200:200:java.lang.String lambda$uploadStage3$2(java.util.Map) -> a
    199:199:boolean lambda$uploadStage3$1(java.lang.Integer,java.util.Map) -> a
    98:98:java.lang.String lambda$uploadStage1$0(java.lang.Integer) -> a
com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketExample -> com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketExample:
# {"fileName":"FreshserviceTicketExample.java","id":"sourceFile"}
    org.slf4j.Logger logger -> a
    com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketService freshserviceTicketService -> b
    org.springframework.web.client.RestTemplate restTemplate -> c
    39:42:void <init>(com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketService,org.springframework.web.client.RestTemplate) -> <init>
    52:114:com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketResponse createExampleTicket() -> createExampleTicket
    126:148:com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketResponse createExampleTicketWithAttachments(java.util.List) -> createExampleTicketWithAttachments
    160:182:com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketResponse createExampleTicketWithAttachmentsInSingleCall(java.util.List) -> createExampleTicketWithAttachmentsInSingleCall
    193:209:com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest createSampleTicketRequest() -> createSampleTicketRequest
    33:33:void <clinit>() -> <clinit>
com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketService -> com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketService:
# {"fileName":"FreshserviceTicketService.java","id":"sourceFile"}
    org.slf4j.Logger logger -> a
    java.lang.String apiUrl -> b
    java.lang.String apiKey -> c
    long maxAttachmentSize -> d
    java.lang.String allowedExtensions -> e
    org.springframework.web.client.RestTemplate restTemplate -> f
    com.fasterxml.jackson.databind.ObjectMapper objectMapper -> g
    java.nio.file.Path tempAttachmentDirectory -> h
    55:59:void <init>(org.springframework.web.client.RestTemplate,com.fasterxml.jackson.databind.ObjectMapper,java.nio.file.Path) -> <init>
    70:143:org.springframework.http.ResponseEntity createTicket(com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest) -> createTicket
    156:175:org.springframework.http.ResponseEntity createTicketWithAttachments(com.itcinfotech.windchill.complaints.request.FreshserviceTicketCreateRequest) -> createTicketWithAttachments
    187:239:org.springframework.http.ResponseEntity addAttachmentsToTicket(java.lang.Long,java.util.List) -> addAttachmentsToTicket
    252:362:org.springframework.http.ResponseEntity uploadAttachmentsWithRestTemplate(java.lang.Long,java.util.List) -> a
    373:507:org.springframework.http.ResponseEntity createTicketWithAttachmentsInSingleCall(com.itcinfotech.windchill.complaints.request.FreshserviceTicketCreateRequest) -> createTicketWithAttachmentsInSingleCall
    518:593:org.springframework.http.ResponseEntity getTicketById(java.lang.Long) -> getTicketById
    614:696:org.springframework.http.ResponseEntity getTicketAttachments(java.lang.Long) -> getTicketAttachments
    717:782:org.springframework.http.ResponseEntity downloadAttachment(java.lang.Long) -> downloadAttachment
    792:792:java.lang.String getApiUrl() -> getApiUrl
    801:801:java.lang.String getApiKey() -> getApiKey
    821:858:org.springframework.http.ResponseEntity getTicketConversations(java.lang.Long) -> getTicketConversations
    869:906:void validateAttachments(java.util.List) -> a
    914:919:org.springframework.http.HttpHeaders createHeaders() -> a
    931:945:com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketResponse createTicketSimple(com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest) -> createTicketSimple
    959:972:boolean addAttachmentsToTicketSimple(java.lang.Long,java.util.List) -> addAttachmentsToTicketSimple
    991:1013:com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketResponse createTicketWithAttachmentsSimple(com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest,java.util.List) -> createTicketWithAttachmentsSimple
    1033:1074:org.springframework.http.ResponseEntity getTicketsByIds(java.util.List) -> getTicketsByIds
    1084:1094:void sanitizeCustomFields(java.util.Map) -> sanitizeCustomFields
    36:36:void <clinit>() -> <clinit>
com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketService$1 -> com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketService$1:
# {"fileName":"FreshserviceTicketService.java","id":"sourceFile"}
    java.io.File val$file -> a
    com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketService this$0 -> b
    262:262:void <init>(com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketService,byte[],java.io.File) -> <init>
    265:265:java.lang.String getFilename() -> getFilename
com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketService$2 -> com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketService$2:
# {"fileName":"FreshserviceTicketService.java","id":"sourceFile"}
    java.io.File val$file -> a
    com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketService this$0 -> b
    429:429:void <init>(com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketService,byte[],java.io.File) -> <init>
    432:432:java.lang.String getFilename() -> getFilename
com.itcinfotech.windchill.complaints.test.FreshserviceApiTest -> com.itcinfotech.windchill.complaints.test.FreshserviceApiTest:
# {"fileName":"FreshserviceApiTest.java","id":"sourceFile"}
    org.slf4j.Logger logger -> a
    com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketExample freshserviceTicketExample -> b
    22:22:void <init>() -> <init>
    31:31:org.springframework.boot.CommandLineRunner testFreshserviceApi() -> testFreshserviceApi
    65:99:void createTestZipFile() -> a
    32:57:void lambda$testFreshserviceApi$0(java.lang.String[]) -> a
    24:24:void <clinit>() -> <clinit>
com.itcinfotech.windchill.complaints.utils.CustomMultipartFile -> com.itcinfotech.windchill.complaints.utils.CustomMultipartFile:
# {"fileName":"CustomMultipartFile.java","id":"sourceFile"}
    java.lang.String name -> a
    java.lang.String contentType -> b
    byte[] content -> c
    17:21:void <init>(java.lang.String,java.lang.String,byte[]) -> <init>
    25:25:java.lang.String getName() -> getName
    30:30:java.lang.String getOriginalFilename() -> getOriginalFilename
    35:35:java.lang.String getContentType() -> getContentType
    40:40:boolean isEmpty() -> isEmpty
    45:45:long getSize() -> getSize
    50:50:byte[] getBytes() -> getBytes
    55:55:java.io.InputStream getInputStream() -> getInputStream
    60:61:void transferTo(java.io.File) -> transferTo
com.itcinfotech.windchill.complaints.utils.FreshserviceFileUtils -> com.itcinfotech.windchill.complaints.utils.FreshserviceFileUtils:
# {"fileName":"FreshserviceFileUtils.java","id":"sourceFile"}
    org.slf4j.Logger logger -> a
    19:19:void <init>() -> <init>
    32:55:java.util.List convertMultipartFilesToFiles(java.util.List,java.lang.String) -> convertMultipartFilesToFiles
    66:71:java.lang.String getMimeType(java.io.File) -> getMimeType
    83:98:boolean isExtensionAllowed(java.lang.String,java.lang.String) -> isExtensionAllowed
    107:119:void cleanupTempFiles(java.util.List) -> cleanupTempFiles
    21:21:void <clinit>() -> <clinit>
com.itcinfotech.windchill.complaints.utils.ZipUtil -> com.itcinfotech.windchill.complaints.utils.ZipUtil:
# {"fileName":"ZipUtil.java","id":"sourceFile"}
    com.itcinfotech.windchill.complaints.service.ComplaintService complaintService -> a
    16:16:void <init>() -> <init>
    24:119:void unzip(java.lang.String,java.lang.String) -> unzip
    125:141:void deleteDirectoryRecursively(java.io.File) -> a
org.springframework.boot.loader.jar.JarEntriesStream -> org.springframework.boot.loader.jar.JarEntriesStream:
# {"fileName":"JarEntriesStream.java","id":"sourceFile"}
    int BUFFER_SIZE -> BUFFER_SIZE
    java.util.jar.JarInputStream in -> in
    byte[] inBuffer -> inBuffer
    byte[] compareBuffer -> compareBuffer
    java.util.zip.Inflater inflater -> inflater
    java.util.jar.JarEntry entry -> entry
    43:53:void <init>(java.io.InputStream) -> <init>
    56:58:java.util.jar.JarEntry getNextEntry() -> getNextEntry
    63:76:boolean matches(boolean,int,int,org.springframework.boot.loader.jar.JarEntriesStream$InputStreamSupplier) -> matches
    80:82:java.io.InputStream getInputStream(int,org.springframework.boot.loader.jar.JarEntriesStream$InputStreamSupplier) -> getInputStream
    87:102:void assertSameContent(java.io.DataInputStream) -> assertSameContent
    105:106:void fail(java.lang.String) -> fail
    111:113:void close() -> close
org.springframework.boot.loader.jar.JarEntriesStream$InputStreamSupplier -> org.springframework.boot.loader.jar.JarEntriesStream$InputStreamSupplier:
# {"fileName":"JarEntriesStream.java","id":"sourceFile"}
    java.io.InputStream get() -> get
org.springframework.boot.loader.jar.ManifestInfo -> org.springframework.boot.loader.jar.ManifestInfo:
# {"fileName":"ManifestInfo.java","id":"sourceFile"}
    java.util.jar.Attributes$Name MULTI_RELEASE -> MULTI_RELEASE
    org.springframework.boot.loader.jar.ManifestInfo NONE -> NONE
    java.util.jar.Manifest manifest -> manifest
    java.lang.Boolean multiRelease -> multiRelease
    45:46:void <init>(java.util.jar.Manifest) -> <init>
    48:51:void <init>(java.util.jar.Manifest,java.lang.Boolean) -> <init>
    58:58:java.util.jar.Manifest getManifest() -> getManifest
    66:76:boolean isMultiRelease() -> isMultiRelease
    32:34:void <clinit>() -> <clinit>
org.springframework.boot.loader.jar.MetaInfVersionsInfo -> org.springframework.boot.loader.jar.MetaInfVersionsInfo:
# {"fileName":"MetaInfVersionsInfo.java","id":"sourceFile"}
    org.springframework.boot.loader.jar.MetaInfVersionsInfo NONE -> NONE
    java.lang.String META_INF_VERSIONS -> META_INF_VERSIONS
    int[] versions -> versions
    java.lang.String[] directories -> directories
    42:45:void <init>(java.util.Set) -> <init>
    52:52:int[] versions() -> versions
    60:60:java.lang.String[] directories() -> directories
    69:69:org.springframework.boot.loader.jar.MetaInfVersionsInfo get(org.springframework.boot.loader.zip.ZipContent) -> get
    79:99:org.springframework.boot.loader.jar.MetaInfVersionsInfo get(int,java.util.function.IntFunction) -> get
    44:44:java.lang.String[] lambda$new$1(int) -> lambda$new$1
    44:44:java.lang.String lambda$new$0(java.lang.Integer) -> lambda$new$0
    34:34:void <clinit>() -> <clinit>
org.springframework.boot.loader.jar.NestedJarFile -> org.springframework.boot.loader.jar.NestedJarFile:
# {"fileName":"NestedJarFile.java","id":"sourceFile"}
    int DECIMAL -> DECIMAL
    java.lang.String META_INF -> META_INF
    java.lang.String META_INF_VERSIONS -> META_INF_VERSIONS
    int BASE_VERSION -> BASE_VERSION
    org.springframework.boot.loader.log.DebugLogger debug -> debug
    org.springframework.boot.loader.ref.Cleaner cleaner -> cleaner
    org.springframework.boot.loader.jar.NestedJarFileResources resources -> resources
    java.lang.ref.Cleaner$Cleanable cleanup -> cleanup
    java.lang.String name -> name
    int version -> version
    org.springframework.boot.loader.jar.NestedJarFile$NestedJarEntry lastEntry -> lastEntry
    boolean closed -> closed
    org.springframework.boot.loader.jar.ManifestInfo manifestInfo -> manifestInfo
    org.springframework.boot.loader.jar.MetaInfVersionsInfo metaInfVersionsInfo -> metaInfVersionsInfo
    97:98:void <init>(java.io.File) -> <init>
    110:111:void <init>(java.io.File,java.lang.String) -> <init>
    124:125:void <init>(java.io.File,java.lang.String,java.lang.Runtime$Version) -> <init>
    141:151:void <init>(java.io.File,java.lang.String,java.lang.Runtime$Version,boolean,org.springframework.boot.loader.ref.Cleaner) -> <init>
    154:157:java.io.InputStream getRawZipDataInputStream() -> getRawZipDataInputStream
    163:168:java.util.jar.Manifest getManifest() -> getManifest
    174:177:java.util.Enumeration entries() -> entries
    182:185:java.util.stream.Stream stream() -> stream
    190:197:java.util.stream.Stream versionedStream() -> versionedStream
    201:202:java.util.stream.Stream streamContentEntries() -> streamContentEntries
    206:224:java.lang.String getBaseName(org.springframework.boot.loader.zip.ZipContent$Entry) -> getBaseName
    229:229:java.util.jar.JarEntry getJarEntry(java.lang.String) -> getJarEntry
    234:234:java.util.jar.JarEntry getEntry(java.lang.String) -> getEntry
    243:254:boolean hasEntry(java.lang.String) -> hasEntry
    258:270:org.springframework.boot.loader.jar.NestedJarFile$NestedJarEntry getNestedJarEntry(java.lang.String) -> getNestedJarEntry
    276:290:org.springframework.boot.loader.zip.ZipContent$Entry getVersionedContentEntry(java.lang.String) -> getVersionedContentEntry
    294:297:org.springframework.boot.loader.zip.ZipContent$Entry getContentEntry(java.lang.String,java.lang.String) -> getContentEntry
    301:310:org.springframework.boot.loader.jar.ManifestInfo getManifestInfo() -> getManifestInfo
    314:325:org.springframework.boot.loader.jar.ManifestInfo getManifestInfo(org.springframework.boot.loader.zip.ZipContent) -> getManifestInfo
    330:340:org.springframework.boot.loader.jar.MetaInfVersionsInfo getMetaInfVersionsInfo() -> getMetaInfVersionsInfo
    345:349:java.io.InputStream getInputStream(java.util.zip.ZipEntry) -> getInputStream
    353:371:java.io.InputStream getInputStream(org.springframework.boot.loader.zip.ZipContent$Entry) -> getInputStream
    376:379:java.lang.String getComment() -> getComment
    384:387:int size() -> size
    392:405:void close() -> close
    409:409:java.lang.String getName() -> getName
    413:419:void ensureOpen() -> ensureOpen
    425:428:void clearCache() -> clearCache
    60:60:java.util.zip.ZipEntry getEntry(java.lang.String) -> getEntry
    184:184:java.util.jar.JarEntry lambda$stream$0(org.springframework.boot.loader.zip.ZipContent$Entry) -> lambda$stream$0
    68:70:void <clinit>() -> <clinit>
org.springframework.boot.loader.jar.NestedJarFile$JarEntriesEnumeration -> org.springframework.boot.loader.jar.NestedJarFile$JarEntriesEnumeration:
# {"fileName":"NestedJarFile.java","id":"sourceFile"}
    org.springframework.boot.loader.zip.ZipContent zipContent -> zipContent
    int cursor -> cursor
    org.springframework.boot.loader.jar.NestedJarFile this$0 -> this$0
    638:640:void <init>(org.springframework.boot.loader.jar.NestedJarFile,org.springframework.boot.loader.zip.ZipContent) -> <init>
    644:644:boolean hasMoreElements() -> hasMoreElements
    649:655:org.springframework.boot.loader.jar.NestedJarFile$NestedJarEntry nextElement() -> nextElement
    632:632:java.lang.Object nextElement() -> nextElement
org.springframework.boot.loader.jar.NestedJarFile$JarEntryInflaterInputStream -> org.springframework.boot.loader.jar.NestedJarFile$JarEntryInflaterInputStream:
# {"fileName":"NestedJarFile.java","id":"sourceFile"}
    java.lang.ref.Cleaner$Cleanable cleanup -> cleanup
    boolean closed -> closed
    org.springframework.boot.loader.jar.NestedJarFile this$0 -> this$0
    796:797:void <init>(org.springframework.boot.loader.jar.NestedJarFile,org.springframework.boot.loader.jar.NestedJarFile$JarEntryInputStream,org.springframework.boot.loader.jar.NestedJarFileResources) -> <init>
    800:803:void <init>(org.springframework.boot.loader.jar.NestedJarFile,org.springframework.boot.loader.jar.NestedJarFile$JarEntryInputStream,org.springframework.boot.loader.jar.NestedJarFileResources,java.util.zip.Inflater) -> <init>
    807:814:void close() -> close
org.springframework.boot.loader.jar.NestedJarFile$JarEntryInputStream -> org.springframework.boot.loader.jar.NestedJarFile$JarEntryInputStream:
# {"fileName":"NestedJarFile.java","id":"sourceFile"}
    int uncompressedSize -> uncompressedSize
    org.springframework.boot.loader.zip.CloseableDataBlock content -> content
    long pos -> pos
    long remaining -> remaining
    boolean closed -> closed
    org.springframework.boot.loader.jar.NestedJarFile this$0 -> this$0
    706:709:void <init>(org.springframework.boot.loader.jar.NestedJarFile,org.springframework.boot.loader.zip.ZipContent$Entry) -> <init>
    713:714:int read() -> read
    720:733:int read(byte[],int,int) -> read
    739:747:long skip(long) -> skip
    751:752:long maxForwardSkip(long) -> maxForwardSkip
    756:756:long maxBackwardSkip(long) -> maxBackwardSkip
    761:761:int available() -> available
    765:768:void ensureOpen() -> ensureOpen
    772:778:void close() -> close
    781:781:int getUncompressedSize() -> getUncompressedSize
org.springframework.boot.loader.jar.NestedJarFile$NestedJarEntry -> org.springframework.boot.loader.jar.NestedJarFile$NestedJarEntry:
# {"fileName":"NestedJarFile.java","id":"sourceFile"}
    java.lang.IllegalStateException CANNOT_BE_MODIFIED_EXCEPTION -> CANNOT_BE_MODIFIED_EXCEPTION
    org.springframework.boot.loader.zip.ZipContent$Entry contentEntry -> contentEntry
    java.lang.String name -> name
    boolean populated -> populated
    org.springframework.boot.loader.jar.NestedJarFile this$0 -> this$0
    445:446:void <init>(org.springframework.boot.loader.jar.NestedJarFile,org.springframework.boot.loader.zip.ZipContent$Entry) -> <init>
    448:452:void <init>(org.springframework.boot.loader.jar.NestedJarFile,org.springframework.boot.loader.zip.ZipContent$Entry,java.lang.String) -> <init>
    456:457:long getTime() -> getTime
    462:463:java.time.LocalDateTime getTimeLocal() -> getTimeLocal
    468:468:void setTime(long) -> setTime
    473:473:void setTimeLocal(java.time.LocalDateTime) -> setTimeLocal
    478:479:java.nio.file.attribute.FileTime getLastModifiedTime() -> getLastModifiedTime
    484:484:java.util.zip.ZipEntry setLastModifiedTime(java.nio.file.attribute.FileTime) -> setLastModifiedTime
    489:490:java.nio.file.attribute.FileTime getLastAccessTime() -> getLastAccessTime
    495:495:java.util.zip.ZipEntry setLastAccessTime(java.nio.file.attribute.FileTime) -> setLastAccessTime
    500:501:java.nio.file.attribute.FileTime getCreationTime() -> getCreationTime
    506:506:java.util.zip.ZipEntry setCreationTime(java.nio.file.attribute.FileTime) -> setCreationTime
    511:511:long getSize() -> getSize
    516:516:void setSize(long) -> setSize
    521:522:long getCompressedSize() -> getCompressedSize
    527:527:void setCompressedSize(long) -> setCompressedSize
    532:533:long getCrc() -> getCrc
    538:538:void setCrc(long) -> setCrc
    543:544:int getMethod() -> getMethod
    549:549:void setMethod(int) -> setMethod
    554:555:byte[] getExtra() -> getExtra
    560:560:void setExtra(byte[]) -> setExtra
    565:566:java.lang.String getComment() -> getComment
    571:571:void setComment(java.lang.String) -> setComment
    575:575:boolean isOwnedBy(org.springframework.boot.loader.jar.NestedJarFile) -> isOwnedBy
    580:580:java.lang.String getRealName() -> getRealName
    585:585:java.lang.String getName() -> getName
    590:591:java.util.jar.Attributes getAttributes() -> getAttributes
    596:596:java.security.cert.Certificate[] getCertificates() -> getCertificates
    601:601:java.security.CodeSigner[] getCodeSigners() -> getCodeSigners
    605:605:org.springframework.boot.loader.jar.SecurityInfo getSecurityInfo() -> getSecurityInfo
    609:609:org.springframework.boot.loader.zip.ZipContent$Entry contentEntry() -> contentEntry
    613:625:void populate() -> populate
    435:435:void <clinit>() -> <clinit>
org.springframework.boot.loader.jar.NestedJarFile$RawZipDataInputStream -> org.springframework.boot.loader.jar.NestedJarFile$RawZipDataInputStream:
# {"fileName":"NestedJarFile.java","id":"sourceFile"}
    boolean closed -> closed
    org.springframework.boot.loader.jar.NestedJarFile this$0 -> this$0
    825:827:void <init>(org.springframework.boot.loader.jar.NestedJarFile,java.io.InputStream) -> <init>
    831:837:void close() -> close
org.springframework.boot.loader.jar.NestedJarFile$ZipContentEntriesSpliterator -> org.springframework.boot.loader.jar.NestedJarFile$ZipContentEntriesSpliterator:
# {"fileName":"NestedJarFile.java","id":"sourceFile"}
    int ADDITIONAL_CHARACTERISTICS -> ADDITIONAL_CHARACTERISTICS
    org.springframework.boot.loader.zip.ZipContent zipContent -> zipContent
    int cursor -> cursor
    org.springframework.boot.loader.jar.NestedJarFile this$0 -> this$0
    672:675:void <init>(org.springframework.boot.loader.jar.NestedJarFile,org.springframework.boot.loader.zip.ZipContent) -> <init>
    679:686:boolean tryAdvance(java.util.function.Consumer) -> tryAdvance
org.springframework.boot.loader.jar.NestedJarFileResources -> org.springframework.boot.loader.jar.NestedJarFileResources:
# {"fileName":"NestedJarFileResources.java","id":"sourceFile"}
    int INFLATER_CACHE_LIMIT -> INFLATER_CACHE_LIMIT
    org.springframework.boot.loader.zip.ZipContent zipContent -> zipContent
    org.springframework.boot.loader.zip.ZipContent zipContentForManifest -> zipContentForManifest
    java.util.Set inputStreams -> inputStreams
    java.util.Deque inflaterCache -> inflaterCache
    49:63:void <init>(java.io.File,java.lang.String) -> <init>
    70:70:org.springframework.boot.loader.zip.ZipContent zipContent() -> zipContent
    79:79:org.springframework.boot.loader.zip.ZipContent zipContentForManifest() -> zipContentForManifest
    87:90:void addInputStream(java.io.InputStream) -> addInputStream
    97:100:void removeInputStream(java.io.InputStream) -> removeInputStream
    108:108:java.lang.Runnable createInflatorCleanupAction(java.util.zip.Inflater) -> createInflatorCleanupAction
    116:125:java.util.zip.Inflater getOrCreateInflater() -> getOrCreateInflater
    134:145:void endOrCacheInflater(java.util.zip.Inflater) -> endOrCacheInflater
    153:154:void run() -> run
    157:165:void releaseAll() -> releaseAll
    168:179:java.io.IOException releaseInflators(java.io.IOException) -> releaseInflators
    183:194:java.io.IOException releaseInputStreams(java.io.IOException) -> releaseInputStreams
    198:210:java.io.IOException releaseZipContent(java.io.IOException) -> releaseZipContent
    214:226:java.io.IOException releaseZipContentForManifest(java.io.IOException) -> releaseZipContentForManifest
    230:234:java.io.IOException addToExceptionChain(java.io.IOException,java.io.IOException) -> addToExceptionChain
    108:108:void lambda$createInflatorCleanupAction$0(java.util.zip.Inflater) -> lambda$createInflatorCleanupAction$0
org.springframework.boot.loader.jar.SecurityInfo -> org.springframework.boot.loader.jar.SecurityInfo:
# {"fileName":"SecurityInfo.java","id":"sourceFile"}
    org.springframework.boot.loader.jar.SecurityInfo NONE -> NONE
    java.security.cert.Certificate[][] certificateLookups -> certificateLookups
    java.security.CodeSigner[][] codeSignerLookups -> codeSignerLookups
    42:45:void <init>(java.security.cert.Certificate[][],java.security.CodeSigner[][]) -> <init>
    48:48:java.security.cert.Certificate[] getCertificates(org.springframework.boot.loader.zip.ZipContent$Entry) -> getCertificates
    52:52:java.security.CodeSigner[] getCodeSigners(org.springframework.boot.loader.zip.ZipContent$Entry) -> getCodeSigners
    56:56:java.lang.Object[] clone(java.lang.Object[]) -> clone
    65:72:org.springframework.boot.loader.jar.SecurityInfo get(org.springframework.boot.loader.zip.ZipContent) -> get
    86:108:org.springframework.boot.loader.jar.SecurityInfo load(org.springframework.boot.loader.zip.ZipContent) -> load
    96:96:java.io.InputStream lambda$load$0(org.springframework.boot.loader.zip.ZipContent$Entry) -> lambda$load$0
    36:36:void <clinit>() -> <clinit>
org.springframework.boot.loader.jar.ZipInflaterInputStream -> org.springframework.boot.loader.jar.ZipInflaterInputStream:
# {"fileName":"ZipInflaterInputStream.java","id":"sourceFile"}
    int available -> available
    boolean extraBytesWritten -> extraBytesWritten
    39:41:void <init>(java.io.InputStream,java.util.zip.Inflater,int) -> <init>
    44:47:int getInflaterBufferSize(long) -> getInflaterBufferSize
    52:52:int available() -> available
    57:61:int read(byte[],int,int) -> read
    67:78:void fill() -> fill
org.springframework.boot.loader.jarmode.JarMode -> org.springframework.boot.loader.jarmode.JarMode:
# {"fileName":"JarMode.java","id":"sourceFile"}
    boolean accepts(java.lang.String) -> accepts
    void run(java.lang.String,java.lang.String[]) -> run
org.springframework.boot.loader.jarmode.JarModeErrorException -> org.springframework.boot.loader.jarmode.JarModeErrorException:
# {"fileName":"JarModeErrorException.java","id":"sourceFile"}
    29:30:void <init>(java.lang.String) -> <init>
    33:34:void <init>(java.lang.String,java.lang.Throwable) -> <init>
org.springframework.boot.loader.launch.Archive -> org.springframework.boot.loader.launch.Archive:
# {"fileName":"Archive.java","id":"sourceFile"}
    java.util.function.Predicate ALL_ENTRIES -> ALL_ENTRIES
    java.util.jar.Manifest getManifest() -> getManifest
    57:57:java.util.Set getClassPathUrls(java.util.function.Predicate) -> getClassPathUrls
    java.util.Set getClassPathUrls(java.util.function.Predicate,java.util.function.Predicate) -> getClassPathUrls
    77:77:boolean isExploded() -> isExploded
    86:86:java.io.File getRootDirectory() -> getRootDirectory
    95:95:void close() -> close
    105:105:org.springframework.boot.loader.launch.Archive create(java.lang.Class) -> create
    109:114:org.springframework.boot.loader.launch.Archive create(java.security.ProtectionDomain) -> create
    125:128:org.springframework.boot.loader.launch.Archive create(java.io.File) -> create
    41:41:boolean lambda$static$0(org.springframework.boot.loader.launch.Archive$Entry) -> lambda$static$0
    41:41:void <clinit>() -> <clinit>
org.springframework.boot.loader.launch.Archive$Entry -> org.springframework.boot.loader.launch.Archive$Entry:
# {"fileName":"Archive.java","id":"sourceFile"}
    java.lang.String name() -> name
    boolean isDirectory() -> isDirectory
org.springframework.boot.loader.launch.ClassPathIndexFile -> org.springframework.boot.loader.launch.ClassPathIndexFile:
# {"fileName":"ClassPathIndexFile.java","id":"sourceFile"}
    java.io.File root -> root
    java.util.Set lines -> lines
    41:44:void <init>(java.io.File,java.util.List) -> <init>
    47:50:java.lang.String extractName(java.lang.String) -> extractName
    54:54:int size() -> size
    58:61:boolean containsEntry(java.lang.String) -> containsEntry
    65:65:java.util.List getUrls() -> getUrls
    70:73:java.net.URL asUrl(java.lang.String) -> asUrl
    78:78:org.springframework.boot.loader.launch.ClassPathIndexFile loadIfPossible(java.io.File,java.lang.String) -> loadIfPossible
    82:89:org.springframework.boot.loader.launch.ClassPathIndexFile loadIfPossible(java.io.File,java.io.File) -> loadIfPossible
    93:93:boolean lineHasText(java.lang.String) -> lineHasText
org.springframework.boot.loader.launch.ExecutableArchiveLauncher -> org.springframework.boot.loader.launch.ExecutableArchiveLauncher:
# {"fileName":"ExecutableArchiveLauncher.java","id":"sourceFile"}
    java.lang.String START_CLASS_ATTRIBUTE -> START_CLASS_ATTRIBUTE
    org.springframework.boot.loader.launch.Archive archive -> archive
    43:44:void <init>() -> <init>
    46:49:void <init>(org.springframework.boot.loader.launch.Archive) -> <init>
    53:57:java.lang.ClassLoader createClassLoader(java.util.Collection) -> createClassLoader
    62:62:org.springframework.boot.loader.launch.Archive getArchive() -> getArchive
    67:72:java.lang.String getMainClass() -> getMainClass
    77:77:java.util.Set getClassPathUrls() -> getClassPathUrls
    86:87:boolean isSearchedDirectory(org.springframework.boot.loader.launch.Archive$Entry) -> isSearchedDirectory
org.springframework.boot.loader.launch.ExplodedArchive -> org.springframework.boot.loader.launch.ExplodedArchive:
# {"fileName":"ExplodedArchive.java","id":"sourceFile"}
    java.lang.Object NO_MANIFEST -> NO_MANIFEST
    java.util.Set SKIPPED_NAMES -> SKIPPED_NAMES
    java.util.Comparator entryComparator -> entryComparator
    java.io.File rootDirectory -> rootDirectory
    java.lang.String rootUriPath -> rootUriPath
    java.lang.Object manifest -> manifest
    58:64:void <init>(java.io.File) -> <init>
    68:73:java.util.jar.Manifest getManifest() -> getManifest
    77:83:java.lang.Object loadManifest() -> loadManifest
    89:105:java.util.Set getClassPathUrls(java.util.function.Predicate,java.util.function.Predicate) -> getClassPathUrls
    109:114:java.util.List listFiles(java.io.File) -> listFiles
    119:119:java.io.File getRootDirectory() -> getRootDirectory
    124:124:java.lang.String toString() -> toString
    42:46:void <clinit>() -> <clinit>
org.springframework.boot.loader.launch.ExplodedArchive$FileArchiveEntry -> org.springframework.boot.loader.launch.ExplodedArchive$FileArchiveEntry:
# {"fileName":"ExplodedArchive.java","id":"sourceFile"}
    java.lang.String name -> name
    java.io.File file -> file
    130:130:void <init>(java.lang.String,java.io.File) -> <init>
    134:134:boolean isDirectory() -> isDirectory
    130:130:java.lang.String toString() -> toString
    130:130:int hashCode() -> hashCode
    130:130:boolean equals(java.lang.Object) -> equals
    130:130:java.lang.String name() -> name
    130:130:java.io.File file() -> file
org.springframework.boot.loader.launch.JarFileArchive -> org.springframework.boot.loader.launch.JarFileArchive:
# {"fileName":"JarFileArchive.java","id":"sourceFile"}
    java.lang.String UNPACK_MARKER -> UNPACK_MARKER
    java.nio.file.attribute.FileAttribute[] NO_FILE_ATTRIBUTES -> NO_FILE_ATTRIBUTES
    java.nio.file.attribute.FileAttribute[] DIRECTORY_PERMISSION_ATTRIBUTES -> DIRECTORY_PERMISSION_ATTRIBUTES
    java.nio.file.attribute.FileAttribute[] FILE_PERMISSION_ATTRIBUTES -> FILE_PERMISSION_ATTRIBUTES
    java.nio.file.Path TEMP -> TEMP
    java.io.File file -> file
    java.util.jar.JarFile jarFile -> jarFile
    java.nio.file.Path tempUnpackDirectory -> tempUnpackDirectory
    70:71:void <init>(java.io.File) -> <init>
    73:76:void <init>(java.io.File,java.util.jar.JarFile) -> <init>
    80:80:java.util.jar.Manifest getManifest() -> getManifest
    86:90:java.util.Set getClassPathUrls(java.util.function.Predicate,java.util.function.Predicate) -> getClassPathUrls
    95:103:java.net.URL getNestedJarUrl(org.springframework.boot.loader.launch.JarFileArchive$JarArchiveEntry) -> getNestedJarUrl
    108:116:java.net.URL getUnpackedNestedJarUrl(java.util.jar.JarEntry) -> getUnpackedNestedJarUrl
    120:131:java.nio.file.Path getTempUnpackDirectory() -> getTempUnpackDirectory
    135:147:java.nio.file.Path createUnpackDirectory(java.nio.file.Path) -> createUnpackDirectory
    151:152:void createDirectory(java.nio.file.Path) -> createDirectory
    155:160:void unpack(java.util.jar.JarEntry,java.nio.file.Path) -> unpack
    163:164:void createFile(java.nio.file.Path) -> createFile
    167:167:java.nio.file.attribute.FileAttribute[] getFileAttributes(java.nio.file.Path,java.nio.file.attribute.FileAttribute[]) -> getFileAttributes
    171:171:boolean supportsPosix(java.nio.file.FileSystem) -> supportsPosix
    176:177:void close() -> close
    181:181:java.lang.String toString() -> toString
    185:185:java.nio.file.attribute.FileAttribute[] asFileAttributes(java.nio.file.attribute.PosixFilePermission[]) -> asFileAttributes
    53:61:void <clinit>() -> <clinit>
org.springframework.boot.loader.launch.JarFileArchive$JarArchiveEntry -> org.springframework.boot.loader.launch.JarFileArchive$JarArchiveEntry:
# {"fileName":"JarFileArchive.java","id":"sourceFile"}
    java.util.jar.JarEntry jarEntry -> jarEntry
    191:191:void <init>(java.util.jar.JarEntry) -> <init>
    195:195:java.lang.String name() -> name
    200:200:boolean isDirectory() -> isDirectory
    191:191:java.lang.String toString() -> toString
    191:191:int hashCode() -> hashCode
    191:191:boolean equals(java.lang.Object) -> equals
    191:191:java.util.jar.JarEntry jarEntry() -> jarEntry
org.springframework.boot.loader.launch.JarLauncher -> org.springframework.boot.loader.launch.JarLauncher:
# {"fileName":"JarLauncher.java","id":"sourceFile"}
    32:33:void <init>() -> <init>
    36:37:void <init>(org.springframework.boot.loader.launch.Archive) -> <init>
    40:41:void main(java.lang.String[]) -> main
org.springframework.boot.loader.launch.JarModeRunner -> org.springframework.boot.loader.launch.JarModeRunner:
# {"fileName":"JarModeRunner.java","id":"sourceFile"}
    java.lang.String DISABLE_SYSTEM_EXIT -> DISABLE_SYSTEM_EXIT
    java.lang.String SUPPRESSED_SYSTEM_EXIT_CODE -> SUPPRESSED_SYSTEM_EXIT_CODE
    37:38:void <init>() -> <init>
    41:57:void main(java.lang.String[]) -> main
    60:68:void runJarMode(java.lang.String,java.lang.String[]) -> runJarMode
    72:79:void printError(java.lang.Throwable) -> printError
    33:35:void <clinit>() -> <clinit>
org.springframework.boot.loader.launch.LaunchedClassLoader -> org.springframework.boot.loader.launch.LaunchedClassLoader:
# {"fileName":"LaunchedClassLoader.java","id":"sourceFile"}
    java.lang.String JAR_MODE_PACKAGE_PREFIX -> JAR_MODE_PACKAGE_PREFIX
    java.lang.String JAR_MODE_RUNNER_CLASS_NAME -> JAR_MODE_RUNNER_CLASS_NAME
    boolean exploded -> exploded
    org.springframework.boot.loader.launch.Archive rootArchive -> rootArchive
    java.lang.Object definePackageLock -> definePackageLock
    org.springframework.boot.loader.launch.LaunchedClassLoader$DefinePackageCallType definePackageCallType -> definePackageCallType
    61:62:void <init>(boolean,java.net.URL[],java.lang.ClassLoader) -> <init>
    50:75:void <init>(boolean,org.springframework.boot.loader.launch.Archive,java.net.URL[],java.lang.ClassLoader) -> <init>
    79:91:java.lang.Class loadClass(java.lang.String,boolean) -> loadClass
    96:110:java.lang.Class loadClassInLaunchedClassLoader(java.lang.String) -> loadClassInLaunchedClassLoader
    116:116:java.lang.Package definePackage(java.lang.String,java.util.jar.Manifest,java.net.URL) -> definePackage
    120:122:java.lang.Package definePackageForExploded(java.lang.String,java.util.jar.Manifest,java.net.URL) -> definePackageForExploded
    128:132:java.lang.Package definePackage(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.net.URL) -> definePackage
    137:148:java.lang.Package definePackageForExploded(java.lang.String,java.net.URL,java.util.function.Supplier) -> definePackageForExploded
    152:159:java.lang.Object definePackage(org.springframework.boot.loader.launch.LaunchedClassLoader$DefinePackageCallType,java.util.function.Supplier) -> definePackage
    164:167:java.util.jar.Manifest getManifest(org.springframework.boot.loader.launch.Archive) -> getManifest
    132:132:java.lang.Package lambda$definePackage$1(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.net.URL) -> lambda$definePackage$1
    121:121:java.lang.Package lambda$definePackageForExploded$0(java.lang.String,java.util.jar.Manifest,java.net.URL) -> lambda$definePackageForExploded$0
    40:44:void <clinit>() -> <clinit>
org.springframework.boot.loader.launch.LaunchedClassLoader$DefinePackageCallType -> org.springframework.boot.loader.launch.LaunchedClassLoader$DefinePackageCallType:
# {"fileName":"LaunchedClassLoader.java","id":"sourceFile"}
    org.springframework.boot.loader.launch.LaunchedClassLoader$DefinePackageCallType MANIFEST -> MANIFEST
    org.springframework.boot.loader.launch.LaunchedClassLoader$DefinePackageCallType ATTRIBUTES -> ATTRIBUTES
    org.springframework.boot.loader.launch.LaunchedClassLoader$DefinePackageCallType[] $VALUES -> $VALUES
    175:175:org.springframework.boot.loader.launch.LaunchedClassLoader$DefinePackageCallType[] values() -> values
    175:175:org.springframework.boot.loader.launch.LaunchedClassLoader$DefinePackageCallType valueOf(java.lang.String) -> valueOf
    175:175:void <init>(java.lang.String,int) -> <init>
    175:175:org.springframework.boot.loader.launch.LaunchedClassLoader$DefinePackageCallType[] $values() -> $values
    175:185:void <clinit>() -> <clinit>
org.springframework.boot.loader.launch.Launcher -> org.springframework.boot.loader.launch.Launcher:
# {"fileName":"Launcher.java","id":"sourceFile"}
    java.lang.String JAR_MODE_RUNNER_CLASS_NAME -> JAR_MODE_RUNNER_CLASS_NAME
    java.lang.String BOOT_CLASSPATH_INDEX_ATTRIBUTE -> BOOT_CLASSPATH_INDEX_ATTRIBUTE
    java.lang.String DEFAULT_CLASSPATH_INDEX_FILE_NAME -> DEFAULT_CLASSPATH_INDEX_FILE_NAME
    org.springframework.boot.loader.launch.ClassPathIndexFile classPathIndex -> classPathIndex
    40:40:void <init>() -> <init>
    57:69:void launch(java.lang.String[]) -> launch
    72:72:boolean hasLength(java.lang.String) -> hasLength
    82:82:java.lang.ClassLoader createClassLoader(java.util.Collection) -> createClassLoader
    86:87:java.lang.ClassLoader createClassLoader(java.net.URL[]) -> createClassLoader
    98:103:void launch(java.lang.ClassLoader,java.lang.String,java.lang.String[]) -> launch
    112:113:boolean isExploded() -> isExploded
    117:121:org.springframework.boot.loader.launch.ClassPathIndexFile getClassPathIndex(org.springframework.boot.loader.launch.Archive) -> getClassPathIndex
    125:128:java.lang.String getClassPathIndexFileLocation(org.springframework.boot.loader.launch.Archive) -> getClassPathIndexFileLocation
    org.springframework.boot.loader.launch.Archive getArchive() -> getArchive
    java.lang.String getMainClass() -> getMainClass
    java.util.Set getClassPathUrls() -> getClassPathUrls
    156:156:java.lang.String getEntryPathPrefix() -> getEntryPathPrefix
    166:166:boolean isIncludedOnClassPath(org.springframework.boot.loader.launch.Archive$Entry) -> isIncludedOnClassPath
    170:174:boolean isLibraryFileOrClassesDirectory(org.springframework.boot.loader.launch.Archive$Entry) -> isLibraryFileOrClassesDirectory
    178:181:boolean isIncludedOnClassPathAndNotIndexed(org.springframework.boot.loader.launch.Archive$Entry) -> isIncludedOnClassPathAndNotIndexed
    42:42:void <clinit>() -> <clinit>
org.springframework.boot.loader.launch.PropertiesLauncher -> org.springframework.boot.loader.launch.PropertiesLauncher:
# {"fileName":"PropertiesLauncher.java","id":"sourceFile"}
    java.lang.String MAIN -> MAIN
    java.lang.String PATH -> PATH
    java.lang.String HOME -> HOME
    java.lang.String ARGS -> ARGS
    java.lang.String CONFIG_NAME -> CONFIG_NAME
    java.lang.String CONFIG_LOCATION -> CONFIG_LOCATION
    java.lang.String SET_SYSTEM_PROPERTIES -> SET_SYSTEM_PROPERTIES
    java.net.URL[] NO_URLS -> NO_URLS
    java.util.regex.Pattern WORD_SEPARATOR -> WORD_SEPARATOR
    java.lang.String NESTED_ARCHIVE_SEPARATOR -> NESTED_ARCHIVE_SEPARATOR
    java.lang.String JAR_FILE_PREFIX -> JAR_FILE_PREFIX
    org.springframework.boot.loader.log.DebugLogger debug -> debug
    org.springframework.boot.loader.launch.Archive archive -> archive
    java.io.File homeDirectory -> homeDirectory
    java.util.List paths -> paths
    java.util.Properties properties -> properties
    144:145:void <init>() -> <init>
    141:153:void <init>(org.springframework.boot.loader.launch.Archive) -> <init>
    156:156:java.io.File getHomeDirectory() -> getHomeDirectory
    160:184:void initializeProperties() -> initializeProperties
    187:194:java.io.InputStream getResource(java.lang.String) -> getResource
    198:201:java.io.InputStream getClasspathResource(java.lang.String) -> getClasspathResource
    205:214:java.lang.String handleUrl(java.lang.String) -> handleUrl
    218:218:boolean isUrl(java.lang.String) -> isUrl
    222:233:java.io.InputStream getURLResource(java.lang.String) -> getURLResource
    237:254:boolean exists(java.net.URL) -> exists
    258:261:void disconnect(java.net.URLConnection) -> disconnect
    264:266:java.io.InputStream getFileResource(java.lang.String) -> getFileResource
    270:275:void loadResource(java.io.InputStream) -> loadResource
    278:285:void resolvePropertyPlaceholders() -> resolvePropertyPlaceholders
    288:293:void addToSystemProperties() -> addToSystemProperties
    296:299:java.util.List getPaths() -> getPaths
    303:313:java.util.List parsePathsProperty(java.lang.String) -> parsePathsProperty
    317:329:java.lang.String cleanupPath(java.lang.String) -> cleanupPath
    334:347:java.lang.ClassLoader createClassLoader(java.util.Collection) -> createClassLoader
    351:359:java.lang.ClassLoader wrapWithCustomClassLoader(java.lang.ClassLoader,java.lang.String) -> wrapWithCustomClassLoader
    364:364:org.springframework.boot.loader.launch.Archive getArchive() -> getArchive
    369:373:java.lang.String getMainClass() -> getMainClass
    377:378:java.lang.String[] getArgs(java.lang.String[]) -> getArgs
    382:385:java.lang.String[] merge(java.lang.String[],java.lang.String[]) -> merge
    389:389:java.lang.String getProperty(java.lang.String) -> getProperty
    393:393:java.lang.String getProperty(java.lang.String,java.lang.String) -> getProperty
    397:397:java.lang.String getPropertyWithDefault(java.lang.String,java.lang.String) -> getPropertyWithDefault
    401:429:java.lang.String getProperty(java.lang.String,java.lang.String,java.lang.String) -> getProperty
    433:434:java.lang.String getManifestValue(org.springframework.boot.loader.launch.Archive,java.lang.String) -> getManifestValue
    438:441:java.lang.String getResolvedProperty(java.lang.String,java.lang.String,java.lang.String,java.lang.String) -> getResolvedProperty
    446:449:void close() -> close
    452:463:java.lang.String toCamelCase(java.lang.CharSequence) -> toCamelCase
    467:467:java.lang.String capitalize(java.lang.String) -> capitalize
    472:479:java.util.Set getClassPathUrls() -> getClassPathUrls
    483:503:java.util.Set getClassPathUrlsForPath(java.lang.String) -> getClassPathUrlsForPath
    507:543:java.util.Set getClassPathUrlsForNested(java.lang.String) -> getClassPathUrlsForNested
    547:548:java.util.Set getClassPathUrlsForRoot() -> getClassPathUrlsForRoot
    552:552:java.util.function.Predicate includeByPrefix(java.lang.String) -> includeByPrefix
    557:557:boolean isArchive(org.springframework.boot.loader.launch.Archive$Entry) -> isArchive
    561:562:boolean isArchive(java.lang.String) -> isArchive
    567:567:boolean isAbsolutePath(java.lang.String) -> isAbsolutePath
    571:574:java.lang.String stripLeadingSlashes(java.lang.String) -> stripLeadingSlashes
    578:581:void main(java.lang.String[]) -> main
    552:553:boolean lambda$includeByPrefix$0(java.lang.String,org.springframework.boot.loader.launch.Archive$Entry) -> lambda$includeByPrefix$0
    125:133:void <clinit>() -> <clinit>
org.springframework.boot.loader.launch.PropertiesLauncher$Instantiator -> org.springframework.boot.loader.launch.PropertiesLauncher$Instantiator:
# {"fileName":"PropertiesLauncher.java","id":"sourceFile"}
    java.lang.ClassLoader parent -> parent
    java.lang.Class type -> type
    589:590:void <init>(java.lang.ClassLoader,java.lang.String) -> <init>
    586:586:void <init>(java.lang.ClassLoader,java.lang.Class) -> <init>
    593:593:java.lang.Object constructWithoutParameters() -> constructWithoutParameters
    597:597:org.springframework.boot.loader.launch.PropertiesLauncher$Instantiator$Using declaredConstructor(java.lang.Class[]) -> declaredConstructor
    586:586:java.lang.String toString() -> toString
    586:586:int hashCode() -> hashCode
    586:586:boolean equals(java.lang.Object) -> equals
    586:586:java.lang.ClassLoader parent() -> parent
    586:586:java.lang.Class type() -> type
org.springframework.boot.loader.launch.PropertiesLauncher$Instantiator$Using -> org.springframework.boot.loader.launch.PropertiesLauncher$Instantiator$Using:
# {"fileName":"PropertiesLauncher.java","id":"sourceFile"}
    org.springframework.boot.loader.launch.PropertiesLauncher$Instantiator instantiator -> instantiator
    java.lang.Class[] parameterTypes -> parameterTypes
    600:600:void <init>(org.springframework.boot.loader.launch.PropertiesLauncher$Instantiator,java.lang.Class[]) -> <init>
    605:610:java.lang.Object newInstance(java.lang.Object[]) -> newInstance
    600:600:java.lang.String toString() -> toString
    600:600:int hashCode() -> hashCode
    600:600:boolean equals(java.lang.Object) -> equals
    600:600:org.springframework.boot.loader.launch.PropertiesLauncher$Instantiator instantiator() -> instantiator
    600:600:java.lang.Class[] parameterTypes() -> parameterTypes
org.springframework.boot.loader.launch.SystemPropertyUtils -> org.springframework.boot.loader.launch.SystemPropertyUtils:
# {"fileName":"SystemPropertyUtils.java","id":"sourceFile"}
    java.lang.String PLACEHOLDER_PREFIX -> PLACEHOLDER_PREFIX
    java.lang.String PLACEHOLDER_SUFFIX -> PLACEHOLDER_SUFFIX
    java.lang.String VALUE_SEPARATOR -> VALUE_SEPARATOR
    java.lang.String SIMPLE_PREFIX -> SIMPLE_PREFIX
    43:44:void <init>() -> <init>
    47:47:java.lang.String resolvePlaceholders(java.util.Properties,java.lang.String) -> resolvePlaceholders
    52:87:java.lang.String parseStringValue(java.util.Properties,java.lang.String,java.lang.String,java.util.Set) -> parseStringValue
    91:95:java.lang.String resolvePlaceholder(java.util.Properties,java.lang.String,java.lang.String) -> resolvePlaceholder
    99:99:java.lang.String getProperty(java.lang.String) -> getProperty
    104:113:java.lang.String getProperty(java.lang.String,java.lang.String,java.lang.String) -> getProperty
    118:138:int findPlaceholderEndIndex(java.lang.CharSequence,int) -> findPlaceholderEndIndex
    142:148:boolean substringMatch(java.lang.CharSequence,int,java.lang.CharSequence) -> substringMatch
    41:41:void <clinit>() -> <clinit>
org.springframework.boot.loader.launch.WarLauncher -> org.springframework.boot.loader.launch.WarLauncher:
# {"fileName":"WarLauncher.java","id":"sourceFile"}
    31:32:void <init>() -> <init>
    35:36:void <init>(org.springframework.boot.loader.launch.Archive) -> <init>
    40:40:java.lang.String getEntryPathPrefix() -> getEntryPathPrefix
    45:49:boolean isLibraryFileOrClassesDirectory(org.springframework.boot.loader.launch.Archive$Entry) -> isLibraryFileOrClassesDirectory
    53:54:void main(java.lang.String[]) -> main
org.springframework.boot.loader.log.DebugLogger -> org.springframework.boot.loader.log.DebugLogger:
# {"fileName":"DebugLogger.java","id":"sourceFile"}
    java.lang.String ENABLED_PROPERTY -> ENABLED_PROPERTY
    org.springframework.boot.loader.log.DebugLogger disabled -> disabled
    25:25:void <init>() -> <init>
    void log(java.lang.String) -> log
    void log(java.lang.String,java.lang.Object) -> log
    void log(java.lang.String,java.lang.Object,java.lang.Object) -> log
    void log(java.lang.String,java.lang.Object,java.lang.Object,java.lang.Object) -> log
    void log(java.lang.String,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object) -> log
    80:80:org.springframework.boot.loader.log.DebugLogger get(java.lang.Class) -> get
    31:32:void <clinit>() -> <clinit>
org.springframework.boot.loader.log.DebugLogger$DisabledDebugLogger -> org.springframework.boot.loader.log.DebugLogger$DisabledDebugLogger:
# {"fileName":"DebugLogger.java","id":"sourceFile"}
    86:86:void <init>() -> <init>
    90:90:void log(java.lang.String) -> log
    94:94:void log(java.lang.String,java.lang.Object) -> log
    98:98:void log(java.lang.String,java.lang.Object,java.lang.Object) -> log
    102:102:void log(java.lang.String,java.lang.Object,java.lang.Object,java.lang.Object) -> log
    106:106:void log(java.lang.String,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object) -> log
org.springframework.boot.loader.log.DebugLogger$SystemErrDebugLogger -> org.springframework.boot.loader.log.DebugLogger$SystemErrDebugLogger:
# {"fileName":"DebugLogger.java","id":"sourceFile"}
    java.lang.String prefix -> prefix
    117:119:void <init>(java.lang.Class) -> <init>
    123:124:void log(java.lang.String) -> log
    128:129:void log(java.lang.String,java.lang.Object) -> log
    133:134:void log(java.lang.String,java.lang.Object,java.lang.Object) -> log
    138:139:void log(java.lang.String,java.lang.Object,java.lang.Object,java.lang.Object) -> log
    143:144:void log(java.lang.String,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object) -> log
    147:148:void print(java.lang.String) -> print
org.springframework.boot.loader.net.protocol.Handlers -> org.springframework.boot.loader.net.protocol.Handlers:
# {"fileName":"Handlers.java","id":"sourceFile"}
    java.lang.String PROTOCOL_HANDLER_PACKAGES -> PROTOCOL_HANDLER_PACKAGES
    java.lang.String PACKAGE -> PACKAGE
    35:36:void <init>() -> <init>
    43:47:void register() -> register
    56:61:void resetCachedUrlHandlers() -> resetCachedUrlHandlers
    33:33:void <clinit>() -> <clinit>
org.springframework.boot.loader.net.protocol.jar.Canonicalizer -> org.springframework.boot.loader.net.protocol.jar.Canonicalizer:
# {"fileName":"Canonicalizer.java","id":"sourceFile"}
    28:29:void <init>() -> <init>
    32:39:java.lang.String canonicalizeAfter(java.lang.String,int) -> canonicalizeAfter
    43:47:java.lang.String canonicalize(java.lang.String) -> canonicalize
    52:57:java.lang.String removeEmbeddedSlashDotDotSlash(java.lang.String) -> removeEmbeddedSlashDotDotSlash
    62:67:java.lang.String removeEmbeddedSlashDotSlash(java.lang.String) -> removeEmbeddedSlashDotSlash
    71:71:java.lang.String removeTrailingSlashDot(java.lang.String) -> removeTrailingSlashDot
    76:81:java.lang.String removeTrailingSlashDotDot(java.lang.String) -> removeTrailingSlashDotDot
org.springframework.boot.loader.net.protocol.jar.Handler -> org.springframework.boot.loader.net.protocol.jar.Handler:
# {"fileName":"Handler.java","id":"sourceFile"}
    java.lang.String PROTOCOL -> PROTOCOL
    java.lang.String SEPARATOR -> SEPARATOR
    org.springframework.boot.loader.net.protocol.jar.Handler INSTANCE -> INSTANCE
    33:33:void <init>() -> <init>
    46:46:java.net.URLConnection openConnection(java.net.URL) -> openConnection
    51:58:void parseURL(java.net.URL,java.lang.String,int,int) -> parseURL
    61:67:java.lang.String extractPath(java.net.URL,java.lang.String,int,int,int) -> extractPath
    71:71:java.lang.String extractAnchorOnlyPath(java.net.URL) -> extractAnchorOnlyPath
    75:81:java.lang.String extractAbsolutePath(java.lang.String,int,int) -> extractAbsolutePath
    85:87:java.lang.String extractRelativePath(java.net.URL,java.lang.String,int,int) -> extractRelativePath
    91:103:java.lang.String extractContextPath(java.net.URL,java.lang.String,int) -> extractContextPath
    107:117:void assertInnerUrlIsNotMalformed(java.lang.String,java.lang.String) -> assertInnerUrlIsNotMalformed
    121:136:int hashCode(java.net.URL) -> hashCode
    141:166:boolean sameFile(java.net.URL,java.net.URL) -> sameFile
    170:170:int indexOfSeparator(java.lang.String) -> indexOfSeparator
    174:179:int indexOfSeparator(java.lang.String,int,int) -> indexOfSeparator
    186:188:void clearCache() -> clearCache
    42:42:void <clinit>() -> <clinit>
org.springframework.boot.loader.net.protocol.jar.JarFileUrlKey -> org.springframework.boot.loader.net.protocol.jar.JarFileUrlKey:
# {"fileName":"JarFileUrlKey.java","id":"sourceFile"}
    java.lang.ref.SoftReference cache -> cache
    35:36:void <init>() -> <init>
    44:49:java.lang.String get(java.net.URL) -> get
    53:68:java.lang.String create(java.net.URL) -> create
    72:73:void clearCache() -> clearCache
org.springframework.boot.loader.net.protocol.jar.JarUrl -> org.springframework.boot.loader.net.protocol.jar.JarUrl:
# {"fileName":"JarUrl.java","id":"sourceFile"}
    32:33:void <init>() -> <init>
    41:41:java.net.URL create(java.io.File) -> create
    51:51:java.net.URL create(java.io.File,java.util.jar.JarEntry) -> create
    61:61:java.net.URL create(java.io.File,java.lang.String) -> create
    73:77:java.net.URL create(java.io.File,java.lang.String,java.lang.String) -> create
    82:83:java.lang.String getJarReference(java.io.File,java.lang.String) -> getJarReference
org.springframework.boot.loader.net.protocol.jar.JarUrlClassLoader -> org.springframework.boot.loader.net.protocol.jar.JarUrlClassLoader:
# {"fileName":"JarUrlClassLoader.java","id":"sourceFile"}
    java.net.URL[] urls -> urls
    boolean hasJarUrls -> hasJarUrls
    java.util.Map jarFiles -> jarFiles
    java.util.Set undefinablePackages -> undefinablePackages
    51:64:void <init>(java.net.URL[],java.lang.ClassLoader) -> <init>
    68:77:java.net.URL findResource(java.lang.String) -> findResource
    82:91:java.util.Enumeration findResources(java.lang.String) -> findResources
    96:111:java.lang.Class loadClass(java.lang.String,boolean) -> loadClass
    121:136:void definePackageIfNecessary(java.lang.String) -> definePackageIfNecessary
    139:160:void definePackage(java.lang.String,java.lang.String) -> definePackage
    164:171:void tolerateRaceConditionDueToBeingParallelCapable(java.lang.IllegalArgumentException,java.lang.String) -> tolerateRaceConditionDueToBeingParallelCapable
    174:175:boolean hasEntry(java.util.jar.JarFile,java.lang.String) -> hasEntry
    179:196:java.util.jar.JarFile getJarFile(java.net.URL) -> getJarFile
    204:217:void clearCache() -> clearCache
    221:229:void clearCache(java.net.URL) -> clearCache
    232:236:void clearCache(java.net.JarURLConnection) -> clearCache
    239:239:boolean isJarUrl(java.net.URL) -> isJarUrl
    244:246:void close() -> close
    249:255:void clearJarFiles() -> clearJarFiles
    44:45:void <clinit>() -> <clinit>
org.springframework.boot.loader.net.protocol.jar.JarUrlClassLoader$OptimizedEnumeration -> org.springframework.boot.loader.net.protocol.jar.JarUrlClassLoader$OptimizedEnumeration:
# {"fileName":"JarUrlClassLoader.java","id":"sourceFile"}
    java.util.Enumeration delegate -> delegate
    264:266:void <init>(java.util.Enumeration) -> <init>
    270:276:boolean hasMoreElements() -> hasMoreElements
    282:288:java.net.URL nextElement() -> nextElement
    260:260:java.lang.Object nextElement() -> nextElement
org.springframework.boot.loader.net.protocol.jar.JarUrlConnection -> org.springframework.boot.loader.net.protocol.jar.JarUrlConnection:
# {"fileName":"JarUrlConnection.java","id":"sourceFile"}
    org.springframework.boot.loader.net.protocol.jar.UrlJarFiles jarFiles -> jarFiles
    java.io.InputStream emptyInputStream -> emptyInputStream
    java.io.FileNotFoundException FILE_NOT_FOUND_EXCEPTION -> FILE_NOT_FOUND_EXCEPTION
    java.net.URL NOT_FOUND_URL -> NOT_FOUND_URL
    org.springframework.boot.loader.net.protocol.jar.JarUrlConnection NOT_FOUND_CONNECTION -> NOT_FOUND_CONNECTION
    java.lang.String entryName -> entryName
    java.util.function.Supplier notFound -> notFound
    java.util.jar.JarFile jarFile -> jarFile
    java.net.URLConnection jarFileConnection -> jarFileConnection
    java.util.jar.JarEntry jarEntry -> jarEntry
    java.lang.String contentType -> contentType
    84:89:void <init>(java.net.URL) -> <init>
    92:95:void <init>(java.util.function.Supplier) -> <init>
    99:100:java.util.jar.JarFile getJarFile() -> getJarFile
    105:106:java.util.jar.JarEntry getJarEntry() -> getJarEntry
    111:112:int getContentLength() -> getContentLength
    118:122:long getContentLengthLong() -> getContentLengthLong
    128:131:java.lang.String getContentType() -> getContentType
    135:138:java.lang.String deduceContentType() -> deduceContentType
    143:149:java.lang.String deduceContentTypeFromStream() -> deduceContentTypeFromStream
    154:154:java.lang.String deduceContentTypeFromEntryName() -> deduceContentTypeFromEntryName
    159:159:long getLastModified() -> getLastModified
    164:164:java.lang.String getHeaderField(java.lang.String) -> getHeaderField
    169:170:java.lang.Object getContent() -> getContent
    175:175:java.security.Permission getPermission() -> getPermission
    180:205:java.io.InputStream getInputStream() -> getInputStream
    210:210:boolean getAllowUserInteraction() -> getAllowUserInteraction
    215:218:void setAllowUserInteraction(boolean) -> setAllowUserInteraction
    222:222:boolean getUseCaches() -> getUseCaches
    227:230:void setUseCaches(boolean) -> setUseCaches
    234:234:boolean getDefaultUseCaches() -> getDefaultUseCaches
    239:242:void setDefaultUseCaches(boolean) -> setDefaultUseCaches
    246:249:void setIfModifiedSince(long) -> setIfModifiedSince
    253:253:java.lang.String getRequestProperty(java.lang.String) -> getRequestProperty
    258:261:void setRequestProperty(java.lang.String,java.lang.String) -> setRequestProperty
    265:268:void addRequestProperty(java.lang.String,java.lang.String) -> addRequestProperty
    272:273:java.util.Map getRequestProperties() -> getRequestProperties
    278:296:void connect() -> connect
    306:310:void assertCachedJarFileHasEntry(java.net.URL,java.lang.String) -> assertCachedJarFileHasEntry
    313:321:java.util.jar.JarEntry getJarEntry(java.net.URL) -> getJarEntry
    325:331:void throwFileNotFound() -> throwFileNotFound
    335:352:org.springframework.boot.loader.net.protocol.jar.JarUrlConnection open(java.net.URL) -> open
    356:357:boolean hasEntry(java.util.jar.JarFile,java.lang.String) -> hasEntry
    361:364:org.springframework.boot.loader.net.protocol.jar.JarUrlConnection notFoundConnection(java.lang.String,java.lang.String) -> notFoundConnection
    369:370:void clearCache() -> clearCache
    365:365:java.io.FileNotFoundException lambda$notFoundConnection$1(java.lang.String,java.lang.String) -> lambda$notFoundConnection$1
    64:64:java.io.FileNotFoundException lambda$static$0() -> lambda$static$0
    51:69:void <clinit>() -> <clinit>
org.springframework.boot.loader.net.protocol.jar.JarUrlConnection$ConnectionInputStream -> org.springframework.boot.loader.net.protocol.jar.JarUrlConnection$ConnectionInputStream:
# {"fileName":"JarUrlConnection.java","id":"sourceFile"}
    org.springframework.boot.loader.net.protocol.jar.JarUrlConnection this$0 -> this$0
    377:377:void <init>(org.springframework.boot.loader.net.protocol.jar.JarUrlConnection) -> <init>
    382:389:void close() -> close
    393:393:java.io.InputStream getDelegateInputStream() -> getDelegateInputStream
org.springframework.boot.loader.net.protocol.jar.JarUrlConnection$EmptyUrlStreamHandler -> org.springframework.boot.loader.net.protocol.jar.JarUrlConnection$EmptyUrlStreamHandler:
# {"fileName":"JarUrlConnection.java","id":"sourceFile"}
    402:402:void <init>() -> <init>
    406:406:java.net.URLConnection openConnection(java.net.URL) -> openConnection
org.springframework.boot.loader.net.protocol.jar.LazyDelegatingInputStream -> org.springframework.boot.loader.net.protocol.jar.LazyDelegatingInputStream:
# {"fileName":"LazyDelegatingInputStream.java","id":"sourceFile"}
    java.io.InputStream in -> in
    27:27:void <init>() -> <init>
    33:33:int read() -> read
    38:38:int read(byte[]) -> read
    43:43:int read(byte[],int,int) -> read
    48:48:long skip(long) -> skip
    53:53:int available() -> available
    59:62:boolean markSupported() -> markSupported
    69:74:void mark(int) -> mark
    78:79:void reset() -> reset
    82:92:java.io.InputStream in() -> in
    97:106:void close() -> close
    java.io.InputStream getDelegateInputStream() -> getDelegateInputStream
org.springframework.boot.loader.net.protocol.jar.Optimizations -> org.springframework.boot.loader.net.protocol.jar.Optimizations:
# {"fileName":"Optimizations.java","id":"sourceFile"}
    java.lang.ThreadLocal status -> status
    28:29:void <init>() -> <init>
    32:33:void enable(boolean) -> enable
    36:37:void disable() -> disable
    40:40:boolean isEnabled() -> isEnabled
    44:44:boolean isEnabled(boolean) -> isEnabled
    26:26:void <clinit>() -> <clinit>
org.springframework.boot.loader.net.protocol.jar.UrlJarEntry -> org.springframework.boot.loader.net.protocol.jar.UrlJarEntry:
# {"fileName":"UrlJarEntry.java","id":"sourceFile"}
    org.springframework.boot.loader.net.protocol.jar.UrlJarManifest manifest -> manifest
    34:36:void <init>(java.util.jar.JarEntry,org.springframework.boot.loader.net.protocol.jar.UrlJarManifest) -> <init>
    40:40:java.util.jar.Attributes getAttributes() -> getAttributes
    44:44:org.springframework.boot.loader.net.protocol.jar.UrlJarEntry of(java.util.zip.ZipEntry,org.springframework.boot.loader.net.protocol.jar.UrlJarManifest) -> of
org.springframework.boot.loader.net.protocol.jar.UrlJarFile -> org.springframework.boot.loader.net.protocol.jar.UrlJarFile:
# {"fileName":"UrlJarFile.java","id":"sourceFile"}
    org.springframework.boot.loader.net.protocol.jar.UrlJarManifest manifest -> manifest
    java.util.function.Consumer closeAction -> closeAction
    41:46:void <init>(java.io.File,java.lang.Runtime$Version,java.util.function.Consumer) -> <init>
    50:50:java.util.zip.ZipEntry getEntry(java.lang.String) -> getEntry
    55:55:java.util.jar.Manifest getManifest() -> getManifest
    60:64:void close() -> close
    44:44:java.util.jar.Manifest lambda$new$0() -> lambda$new$0
org.springframework.boot.loader.net.protocol.jar.UrlJarFileFactory -> org.springframework.boot.loader.net.protocol.jar.UrlJarFileFactory:
# {"fileName":"UrlJarFileFactory.java","id":"sourceFile"}
    40:40:void <init>() -> <init>
    50:57:java.util.jar.JarFile createJarFile(java.net.URL,java.util.function.Consumer) -> createJarFile
    66:66:java.lang.Runtime$Version getVersion(java.net.URL) -> getVersion
    70:70:boolean isLocalFileUrl(java.net.URL) -> isLocalFileUrl
    74:74:boolean isLocal(java.lang.String) -> isLocal
    79:80:java.util.jar.JarFile createJarFileForLocalFile(java.net.URL,java.lang.Runtime$Version,java.util.function.Consumer) -> createJarFileForLocalFile
    85:86:java.util.jar.JarFile createJarFileForNested(java.net.URL,java.lang.Runtime$Version,java.util.function.Consumer) -> createJarFileForNested
    90:92:java.util.jar.JarFile createJarFileForStream(java.net.URL,java.lang.Runtime$Version,java.util.function.Consumer) -> createJarFileForStream
    97:106:java.util.jar.JarFile createJarFileForStream(java.io.InputStream,java.lang.Runtime$Version,java.util.function.Consumer) -> createJarFileForStream
    112:117:void deleteIfPossible(java.nio.file.Path,java.lang.Throwable) -> deleteIfPossible
    120:120:boolean isNestedUrl(java.net.URL) -> isNestedUrl
org.springframework.boot.loader.net.protocol.jar.UrlJarFiles -> org.springframework.boot.loader.net.protocol.jar.UrlJarFiles:
# {"fileName":"UrlJarFiles.java","id":"sourceFile"}
    org.springframework.boot.loader.net.protocol.jar.UrlJarFileFactory factory -> factory
    org.springframework.boot.loader.net.protocol.jar.UrlJarFiles$Cache cache -> cache
    45:46:void <init>() -> <init>
    39:54:void <init>(org.springframework.boot.loader.net.protocol.jar.UrlJarFileFactory) -> <init>
    66:72:java.util.jar.JarFile getOrCreate(boolean,java.net.URL) -> getOrCreate
    81:81:java.util.jar.JarFile getCached(java.net.URL) -> getCached
    93:96:boolean cacheIfAbsent(boolean,java.net.URL,java.util.jar.JarFile) -> cacheIfAbsent
    106:110:void closeIfNotCached(java.net.URL,java.util.jar.JarFile) -> closeIfNotCached
    121:126:java.net.URLConnection reconnect(java.util.jar.JarFile,java.net.URLConnection) -> reconnect
    130:131:java.net.URLConnection openConnection(java.util.jar.JarFile) -> openConnection
    135:136:void onClose(java.util.jar.JarFile) -> onClose
    139:140:void clearCache() -> clearCache
org.springframework.boot.loader.net.protocol.jar.UrlJarFiles$Cache -> org.springframework.boot.loader.net.protocol.jar.UrlJarFiles$Cache:
# {"fileName":"UrlJarFiles.java","id":"sourceFile"}
    java.util.Map jarFileUrlToJarFile -> jarFileUrlToJarFile
    java.util.Map jarFileToJarFileUrl -> jarFileToJarFileUrl
    145:149:void <init>() -> <init>
    157:160:java.util.jar.JarFile get(java.net.URL) -> get
    169:171:java.net.URL get(java.util.jar.JarFile) -> get
    183:192:boolean putIfAbsent(java.net.URL,java.util.jar.JarFile) -> putIfAbsent
    200:206:void remove(java.util.jar.JarFile) -> remove
    209:213:void clear() -> clear
org.springframework.boot.loader.net.protocol.jar.UrlJarManifest -> org.springframework.boot.loader.net.protocol.jar.UrlJarManifest:
# {"fileName":"UrlJarManifest.java","id":"sourceFile"}
    java.lang.Object NONE -> NONE
    org.springframework.boot.loader.net.protocol.jar.UrlJarManifest$ManifestSupplier supplier -> supplier
    java.lang.Object supplied -> supplied
    39:41:void <init>(org.springframework.boot.loader.net.protocol.jar.UrlJarManifest$ManifestSupplier) -> <init>
    44:51:java.util.jar.Manifest get() -> get
    55:60:java.util.jar.Attributes getEntryAttributes(java.util.jar.JarEntry) -> getEntryAttributes
    64:64:java.util.jar.Attributes cloneAttributes(java.util.jar.Attributes) -> cloneAttributes
    68:73:java.util.jar.Manifest supply() -> supply
    50:50:void lambda$get$0(java.util.jar.Manifest,java.lang.String,java.util.jar.Attributes) -> lambda$get$0
    33:33:void <clinit>() -> <clinit>
org.springframework.boot.loader.net.protocol.jar.UrlJarManifest$ManifestSupplier -> org.springframework.boot.loader.net.protocol.jar.UrlJarManifest$ManifestSupplier:
# {"fileName":"UrlJarManifest.java","id":"sourceFile"}
    java.util.jar.Manifest getManifest() -> getManifest
org.springframework.boot.loader.net.protocol.jar.UrlNestedJarFile -> org.springframework.boot.loader.net.protocol.jar.UrlNestedJarFile:
# {"fileName":"UrlNestedJarFile.java","id":"sourceFile"}
    org.springframework.boot.loader.net.protocol.jar.UrlJarManifest manifest -> manifest
    java.util.function.Consumer closeAction -> closeAction
    42:45:void <init>(java.io.File,java.lang.String,java.lang.Runtime$Version,java.util.function.Consumer) -> <init>
    49:49:java.util.jar.Manifest getManifest() -> getManifest
    54:54:java.util.jar.JarEntry getEntry(java.lang.String) -> getEntry
    59:63:void close() -> close
    34:34:java.util.zip.ZipEntry getEntry(java.lang.String) -> getEntry
    43:43:java.util.jar.Manifest lambda$new$0() -> lambda$new$0
org.springframework.boot.loader.net.protocol.nested.Handler -> org.springframework.boot.loader.net.protocol.nested.Handler:
# {"fileName":"Handler.java","id":"sourceFile"}
    java.lang.String PREFIX -> PREFIX
    31:31:void <init>() -> <init>
    40:40:java.net.URLConnection openConnection(java.net.URL) -> openConnection
    48:52:void assertUrlIsNotMalformed(java.lang.String) -> assertUrlIsNotMalformed
    58:59:void clearCache() -> clearCache
org.springframework.boot.loader.net.protocol.nested.NestedLocation -> org.springframework.boot.loader.net.protocol.nested.NestedLocation:
# {"fileName":"NestedLocation.java","id":"sourceFile"}
    java.nio.file.Path path -> path
    java.lang.String nestedEntryName -> nestedEntryName
    java.util.Map locationCache -> locationCache
    java.util.Map pathCache -> pathCache
    60:66:void <init>(java.nio.file.Path,java.lang.String) -> <init>
    75:78:org.springframework.boot.loader.net.protocol.nested.NestedLocation fromUrl(java.net.URL) -> fromUrl
    88:91:org.springframework.boot.loader.net.protocol.nested.NestedLocation fromUri(java.net.URI) -> fromUri
    95:98:org.springframework.boot.loader.net.protocol.nested.NestedLocation parse(java.lang.String) -> parse
    102:105:org.springframework.boot.loader.net.protocol.nested.NestedLocation create(java.lang.String) -> create
    109:109:java.nio.file.Path asPath(java.lang.String) -> asPath
    114:114:boolean isWindows() -> isWindows
    119:126:java.lang.String fixWindowsLocationPath(java.lang.String) -> fixWindowsLocationPath
    130:132:void clearCache() -> clearCache
    54:54:java.lang.String toString() -> toString
    54:54:int hashCode() -> hashCode
    54:54:boolean equals(java.lang.Object) -> equals
    54:54:java.nio.file.Path path() -> path
    54:54:java.lang.String nestedEntryName() -> nestedEntryName
    110:110:java.nio.file.Path lambda$asPath$1(java.lang.String,java.lang.String) -> lambda$asPath$1
    98:98:org.springframework.boot.loader.net.protocol.nested.NestedLocation lambda$parse$0(java.lang.String,java.lang.String) -> lambda$parse$0
    56:58:void <clinit>() -> <clinit>
org.springframework.boot.loader.net.protocol.nested.NestedUrlConnection -> org.springframework.boot.loader.net.protocol.nested.NestedUrlConnection:
# {"fileName":"NestedUrlConnection.java","id":"sourceFile"}
    java.time.format.DateTimeFormatter RFC_1123_DATE_TIME -> RFC_1123_DATE_TIME
    java.lang.String CONTENT_TYPE -> CONTENT_TYPE
    org.springframework.boot.loader.net.protocol.nested.NestedUrlConnectionResources resources -> resources
    java.lang.ref.Cleaner$Cleanable cleanup -> cleanup
    long lastModified -> lastModified
    java.io.FilePermission permission -> permission
    java.util.Map headerFields -> headerFields
    67:68:void <init>(java.net.URL) -> <init>
    60:75:void <init>(java.net.URL,org.springframework.boot.loader.ref.Cleaner) -> <init>
    79:82:org.springframework.boot.loader.net.protocol.nested.NestedLocation parseNestedLocation(java.net.URL) -> parseNestedLocation
    88:89:java.lang.String getHeaderField(java.lang.String) -> getHeaderField
    94:96:java.lang.String getHeaderField(int) -> getHeaderField
    101:102:java.lang.String getHeaderFieldKey(int) -> getHeaderFieldKey
    106:111:java.util.Map$Entry getHeaderEntry(int) -> getHeaderEntry
    117:137:java.util.Map getHeaderFields() -> getHeaderFields
    142:143:int getContentLength() -> getContentLength
    149:153:long getContentLengthLong() -> getContentLengthLong
    159:159:java.lang.String getContentType() -> getContentType
    164:172:long getLastModified() -> getLastModified
    177:181:java.security.Permission getPermission() -> getPermission
    186:187:java.io.InputStream getInputStream() -> getInputStream
    192:197:void connect() -> connect
    51:52:void <clinit>() -> <clinit>
org.springframework.boot.loader.net.protocol.nested.NestedUrlConnection$ConnectionInputStream -> org.springframework.boot.loader.net.protocol.nested.NestedUrlConnection$ConnectionInputStream:
# {"fileName":"NestedUrlConnection.java","id":"sourceFile"}
    boolean closing -> closing
    org.springframework.boot.loader.net.protocol.nested.NestedUrlConnection this$0 -> this$0
    206:208:void <init>(org.springframework.boot.loader.net.protocol.nested.NestedUrlConnection,java.io.InputStream) -> <init>
    212:227:void close() -> close
org.springframework.boot.loader.net.protocol.nested.NestedUrlConnectionResources -> org.springframework.boot.loader.net.protocol.nested.NestedUrlConnectionResources:
# {"fileName":"NestedUrlConnectionResources.java","id":"sourceFile"}
    org.springframework.boot.loader.net.protocol.nested.NestedLocation location -> location
    org.springframework.boot.loader.zip.ZipContent zipContent -> zipContent
    long size -> size
    java.io.InputStream inputStream -> inputStream
    39:45:void <init>(org.springframework.boot.loader.net.protocol.nested.NestedLocation) -> <init>
    48:48:org.springframework.boot.loader.net.protocol.nested.NestedLocation getLocation() -> getLocation
    52:65:void connect() -> connect
    68:76:void connectData() -> connectData
    79:84:java.io.InputStream getInputStream() -> getInputStream
    88:88:long getContentLength() -> getContentLength
    93:94:void run() -> run
    97:118:void releaseAll() -> releaseAll
    121:125:java.io.IOException addToExceptionChain(java.io.IOException,java.io.IOException) -> addToExceptionChain
org.springframework.boot.loader.net.util.UrlDecoder -> org.springframework.boot.loader.net.util.UrlDecoder:
# {"fileName":"UrlDecoder.java","id":"sourceFile"}
    34:35:void <init>() -> <init>
    45:71:java.lang.String decode(java.lang.String) -> decode
    75:84:int fillByteBuffer(java.nio.ByteBuffer,java.lang.String,int,int) -> fillByteBuffer
    89:92:byte unescape(java.lang.String,int) -> unescape
    97:101:void decodeToCharBuffer(java.nio.ByteBuffer,java.nio.CharBuffer,java.nio.charset.CharsetDecoder) -> decodeToCharBuffer
    104:107:void assertNoError(java.nio.charset.CoderResult) -> assertNoError
org.springframework.boot.loader.nio.file.NestedByteChannel -> org.springframework.boot.loader.nio.file.NestedByteChannel:
# {"fileName":"NestedByteChannel.java","id":"sourceFile"}
    long position -> position
    org.springframework.boot.loader.nio.file.NestedByteChannel$Resources resources -> resources
    java.lang.ref.Cleaner$Cleanable cleanup -> cleanup
    long size -> size
    boolean closed -> closed
    53:54:void <init>(java.nio.file.Path,java.lang.String) -> <init>
    56:60:void <init>(java.nio.file.Path,java.lang.String,org.springframework.boot.loader.ref.Cleaner) -> <init>
    64:64:boolean isOpen() -> isOpen
    69:79:void close() -> close
    83:93:int read(java.nio.ByteBuffer) -> read
    98:98:int write(java.nio.ByteBuffer) -> write
    103:104:long position() -> position
    109:114:java.nio.channels.SeekableByteChannel position(long) -> position
    119:120:long size() -> size
    125:125:java.nio.channels.SeekableByteChannel truncate(long) -> truncate
    129:132:void assertNotClosed() -> assertNotClosed
org.springframework.boot.loader.nio.file.NestedByteChannel$Resources -> org.springframework.boot.loader.nio.file.NestedByteChannel$Resources:
# {"fileName":"NestedByteChannel.java","id":"sourceFile"}
    org.springframework.boot.loader.zip.ZipContent zipContent -> zipContent
    org.springframework.boot.loader.zip.CloseableDataBlock data -> data
    143:146:void <init>(java.nio.file.Path,java.lang.String) -> <init>
    149:149:org.springframework.boot.loader.zip.DataBlock getData() -> getData
    154:155:void run() -> run
    158:177:void releaseAll() -> releaseAll
org.springframework.boot.loader.nio.file.NestedFileStore -> org.springframework.boot.loader.nio.file.NestedFileStore:
# {"fileName":"NestedFileStore.java","id":"sourceFile"}
    org.springframework.boot.loader.nio.file.NestedFileSystem fileSystem -> fileSystem
    38:40:void <init>(org.springframework.boot.loader.nio.file.NestedFileSystem) -> <init>
    44:44:java.lang.String name() -> name
    49:49:java.lang.String type() -> type
    54:54:boolean isReadOnly() -> isReadOnly
    59:59:long getTotalSpace() -> getTotalSpace
    64:64:long getUsableSpace() -> getUsableSpace
    69:69:long getUnallocatedSpace() -> getUnallocatedSpace
    74:74:boolean supportsFileAttributeView(java.lang.Class) -> supportsFileAttributeView
    79:79:boolean supportsFileAttributeView(java.lang.String) -> supportsFileAttributeView
    84:84:java.nio.file.attribute.FileStoreAttributeView getFileStoreAttributeView(java.lang.Class) -> getFileStoreAttributeView
    90:93:java.lang.Object getAttribute(java.lang.String) -> getAttribute
    99:102:java.nio.file.FileStore getJarPathFileStore() -> getJarPathFileStore
org.springframework.boot.loader.nio.file.NestedFileSystem -> org.springframework.boot.loader.nio.file.NestedFileSystem:
# {"fileName":"NestedFileSystem.java","id":"sourceFile"}
    java.util.Set SUPPORTED_FILE_ATTRIBUTE_VIEWS -> SUPPORTED_FILE_ATTRIBUTE_VIEWS
    java.lang.String FILE_SYSTEMS_CLASS_NAME -> FILE_SYSTEMS_CLASS_NAME
    java.lang.Object EXISTING_FILE_SYSTEM -> EXISTING_FILE_SYSTEM
    org.springframework.boot.loader.nio.file.NestedFileSystemProvider provider -> provider
    java.nio.file.Path jarPath -> jarPath
    boolean closed -> closed
    java.util.Map zipFileSystems -> zipFileSystems
    58:66:void <init>(org.springframework.boot.loader.nio.file.NestedFileSystemProvider,java.nio.file.Path) -> <init>
    71:87:void installZipFileSystemIfNecessary(java.lang.String) -> installZipFileSystemIfNecessary
    91:95:boolean hasFileSystem(java.net.URI) -> hasFileSystem
    100:106:boolean isCreatingNewFileSystem() -> isCreatingNewFileSystem
    111:111:java.nio.file.spi.FileSystemProvider provider() -> provider
    115:115:java.nio.file.Path getJarPath() -> getJarPath
    120:132:void close() -> close
    136:141:void closeZipFileSystem(java.nio.file.FileSystem) -> closeZipFileSystem
    145:145:boolean isOpen() -> isOpen
    150:150:boolean isReadOnly() -> isReadOnly
    155:155:java.lang.String getSeparator() -> getSeparator
    160:161:java.lang.Iterable getRootDirectories() -> getRootDirectories
    166:167:java.lang.Iterable getFileStores() -> getFileStores
    172:173:java.util.Set supportedFileAttributeViews() -> supportedFileAttributeViews
    178:182:java.nio.file.Path getPath(java.lang.String,java.lang.String[]) -> getPath
    187:187:java.nio.file.PathMatcher getPathMatcher(java.lang.String) -> getPathMatcher
    192:192:java.nio.file.attribute.UserPrincipalLookupService getUserPrincipalLookupService() -> getUserPrincipalLookupService
    197:197:java.nio.file.WatchService newWatchService() -> newWatchService
    202:209:boolean equals(java.lang.Object) -> equals
    214:214:int hashCode() -> hashCode
    219:219:java.lang.String toString() -> toString
    223:226:void assertNotClosed() -> assertNotClosed
    46:50:void <clinit>() -> <clinit>
org.springframework.boot.loader.nio.file.NestedFileSystemProvider -> org.springframework.boot.loader.nio.file.NestedFileSystemProvider:
# {"fileName":"NestedFileSystemProvider.java","id":"sourceFile"}
    java.util.Map fileSystems -> fileSystems
    51:53:void <init>() -> <init>
    57:57:java.lang.String getScheme() -> getScheme
    62:71:java.nio.file.FileSystem newFileSystem(java.net.URI,java.util.Map) -> newFileSystem
    76:83:java.nio.file.FileSystem getFileSystem(java.net.URI) -> getFileSystem
    89:95:java.nio.file.Path getPath(java.net.URI) -> getPath
    99:102:void removeFileSystem(org.springframework.boot.loader.nio.file.NestedFileSystem) -> removeFileSystem
    107:108:java.nio.channels.SeekableByteChannel newByteChannel(java.nio.file.Path,java.util.Set,java.nio.file.attribute.FileAttribute[]) -> newByteChannel
    113:113:java.nio.file.DirectoryStream newDirectoryStream(java.nio.file.Path,java.nio.file.DirectoryStream$Filter) -> newDirectoryStream
    118:118:void createDirectory(java.nio.file.Path,java.nio.file.attribute.FileAttribute[]) -> createDirectory
    123:123:void delete(java.nio.file.Path) -> delete
    128:128:void copy(java.nio.file.Path,java.nio.file.Path,java.nio.file.CopyOption[]) -> copy
    133:133:void move(java.nio.file.Path,java.nio.file.Path,java.nio.file.CopyOption[]) -> move
    138:138:boolean isSameFile(java.nio.file.Path,java.nio.file.Path) -> isSameFile
    143:143:boolean isHidden(java.nio.file.Path) -> isHidden
    148:150:java.nio.file.FileStore getFileStore(java.nio.file.Path) -> getFileStore
    155:157:void checkAccess(java.nio.file.Path,java.nio.file.AccessMode[]) -> checkAccess
    161:162:java.nio.file.attribute.FileAttributeView getFileAttributeView(java.nio.file.Path,java.lang.Class,java.nio.file.LinkOption[]) -> getFileAttributeView
    168:169:java.nio.file.attribute.BasicFileAttributes readAttributes(java.nio.file.Path,java.lang.Class,java.nio.file.LinkOption[]) -> readAttributes
    174:175:java.util.Map readAttributes(java.nio.file.Path,java.lang.String,java.nio.file.LinkOption[]) -> readAttributes
    179:179:java.nio.file.Path getJarPath(java.nio.file.Path) -> getJarPath
    184:184:void setAttribute(java.nio.file.Path,java.lang.String,java.lang.Object,java.nio.file.LinkOption[]) -> setAttribute
    92:92:org.springframework.boot.loader.nio.file.NestedFileSystem lambda$getPath$0(java.nio.file.Path) -> lambda$getPath$0
org.springframework.boot.loader.nio.file.NestedPath -> org.springframework.boot.loader.nio.file.NestedPath:
# {"fileName":"NestedPath.java","id":"sourceFile"}
    org.springframework.boot.loader.nio.file.NestedFileSystem fileSystem -> fileSystem
    java.lang.String nestedEntryName -> nestedEntryName
    java.lang.Boolean entryExists -> entryExists
    51:57:void <init>(org.springframework.boot.loader.nio.file.NestedFileSystem,java.lang.String) -> <init>
    60:60:java.nio.file.Path getJarPath() -> getJarPath
    64:64:java.lang.String getNestedEntryName() -> getNestedEntryName
    69:69:org.springframework.boot.loader.nio.file.NestedFileSystem getFileSystem() -> getFileSystem
    74:74:boolean isAbsolute() -> isAbsolute
    79:79:java.nio.file.Path getRoot() -> getRoot
    84:84:java.nio.file.Path getFileName() -> getFileName
    89:89:java.nio.file.Path getParent() -> getParent
    94:94:int getNameCount() -> getNameCount
    99:102:java.nio.file.Path getName(int) -> getName
    107:110:java.nio.file.Path subpath(int,int) -> subpath
    115:115:boolean startsWith(java.nio.file.Path) -> startsWith
    120:120:boolean endsWith(java.nio.file.Path) -> endsWith
    125:125:java.nio.file.Path normalize() -> normalize
    130:130:java.nio.file.Path resolve(java.nio.file.Path) -> resolve
    135:135:java.nio.file.Path relativize(java.nio.file.Path) -> relativize
    141:148:java.net.URI toUri() -> toUri
    154:154:java.nio.file.Path toAbsolutePath() -> toAbsolutePath
    159:159:java.nio.file.Path toRealPath(java.nio.file.LinkOption[]) -> toRealPath
    164:164:java.nio.file.WatchKey register(java.nio.file.WatchService,java.nio.file.WatchEvent$Kind[],java.nio.file.WatchEvent$Modifier[]) -> register
    169:170:int compareTo(java.nio.file.Path) -> compareTo
    175:183:boolean equals(java.lang.Object) -> equals
    188:188:int hashCode() -> hashCode
    193:197:java.lang.String toString() -> toString
    201:219:void assertExists() -> assertExists
    222:225:org.springframework.boot.loader.nio.file.NestedPath cast(java.nio.file.Path) -> cast
    43:43:java.nio.file.FileSystem getFileSystem() -> getFileSystem
    43:43:int compareTo(java.lang.Object) -> compareTo
org.springframework.boot.loader.nio.file.UriPathEncoder -> org.springframework.boot.loader.nio.file.UriPathEncoder:
# {"fileName":"UriPathEncoder.java","id":"sourceFile"}
    char[] ALLOWED -> ALLOWED
    33:34:void <init>() -> <init>
    37:43:java.lang.String encode(java.lang.String) -> encode
    47:58:java.lang.String encode(byte[]) -> encode
    62:67:boolean isAllowed(int) -> isAllowed
    71:71:boolean isAlpha(int) -> isAlpha
    75:75:boolean isDigit(int) -> isDigit
    31:31:void <clinit>() -> <clinit>
org.springframework.boot.loader.ref.Cleaner -> org.springframework.boot.loader.ref.Cleaner:
# {"fileName":"Cleaner.java","id":"sourceFile"}
    org.springframework.boot.loader.ref.Cleaner instance -> instance
    java.lang.ref.Cleaner$Cleanable register(java.lang.Object,java.lang.Runnable) -> register
    33:33:void <clinit>() -> <clinit>
org.springframework.boot.loader.ref.DefaultCleaner -> org.springframework.boot.loader.ref.DefaultCleaner:
# {"fileName":"DefaultCleaner.java","id":"sourceFile"}
    org.springframework.boot.loader.ref.DefaultCleaner instance -> instance
    java.util.function.BiConsumer tracker -> tracker
    java.lang.ref.Cleaner cleaner -> cleaner
    27:33:void <init>() -> <init>
    37:41:java.lang.ref.Cleaner$Cleanable register(java.lang.Object,java.lang.Runnable) -> register
    29:29:void <clinit>() -> <clinit>
org.springframework.boot.loader.zip.ByteArrayDataBlock -> org.springframework.boot.loader.zip.ByteArrayDataBlock:
# {"fileName":"ByteArrayDataBlock.java","id":"sourceFile"}
    byte[] bytes -> bytes
    int maxReadSize -> maxReadSize
    38:39:void <init>(byte[]) -> <init>
    41:44:void <init>(byte[],int) -> <init>
    48:48:long size() -> size
    53:53:int read(java.nio.ByteBuffer,long) -> read
    57:63:int read(java.nio.ByteBuffer,int) -> read
    68:68:void close() -> close
org.springframework.boot.loader.zip.CloseableDataBlock -> org.springframework.boot.loader.zip.CloseableDataBlock:
# {"fileName":"CloseableDataBlock.java","id":"sourceFile"}
org.springframework.boot.loader.zip.DataBlock -> org.springframework.boot.loader.zip.DataBlock:
# {"fileName":"DataBlock.java","id":"sourceFile"}
    long size() -> size
    int read(java.nio.ByteBuffer,long) -> read
    64:71:void readFully(java.nio.ByteBuffer,long) -> readFully
    79:79:java.io.InputStream asInputStream() -> asInputStream
org.springframework.boot.loader.zip.DataBlockInputStream -> org.springframework.boot.loader.zip.DataBlockInputStream:
# {"fileName":"DataBlockInputStream.java","id":"sourceFile"}
    org.springframework.boot.loader.zip.DataBlock dataBlock -> dataBlock
    long pos -> pos
    long remaining -> remaining
    boolean closed -> closed
    39:42:void <init>(org.springframework.boot.loader.zip.DataBlock) -> <init>
    46:47:int read() -> read
    52:59:int read(byte[],int,int) -> read
    64:67:long skip(long) -> skip
    71:72:long maxForwardSkip(long) -> maxForwardSkip
    76:76:long maxBackwardSkip(long) -> maxBackwardSkip
    81:84:int available() -> available
    88:91:void ensureOpen() -> ensureOpen
    95:102:void close() -> close
org.springframework.boot.loader.zip.FileDataBlock -> org.springframework.boot.loader.zip.FileDataBlock:
# {"fileName":"FileDataBlock.java","id":"sourceFile"}
    org.springframework.boot.loader.log.DebugLogger debug -> debug
    org.springframework.boot.loader.zip.FileDataBlock$Tracker tracker -> tracker
    org.springframework.boot.loader.zip.FileDataBlock$FileAccess fileAccess -> fileAccess
    long offset -> offset
    long size -> size
    51:55:void <init>(java.nio.file.Path) -> <init>
    57:61:void <init>(org.springframework.boot.loader.zip.FileDataBlock$FileAccess,long,long) -> <init>
    65:65:long size() -> size
    70:88:int read(java.nio.ByteBuffer,long) -> read
    97:98:void open() -> open
    107:108:void close() -> close
    117:118:void ensureOpen(java.util.function.Supplier) -> ensureOpen
    129:129:org.springframework.boot.loader.zip.FileDataBlock slice(long) -> slice
    141:151:org.springframework.boot.loader.zip.FileDataBlock slice(long,long) -> slice
    41:43:void <clinit>() -> <clinit>
org.springframework.boot.loader.zip.FileDataBlock$FileAccess -> org.springframework.boot.loader.zip.FileDataBlock$FileAccess:
# {"fileName":"FileDataBlock.java","id":"sourceFile"}
    int BUFFER_SIZE -> BUFFER_SIZE
    java.nio.file.Path path -> path
    int referenceCount -> referenceCount
    java.nio.channels.FileChannel fileChannel -> fileChannel
    boolean fileChannelInterrupted -> fileChannelInterrupted
    java.io.RandomAccessFile randomAccessFile -> randomAccessFile
    java.nio.ByteBuffer buffer -> buffer
    long bufferPosition -> bufferPosition
    int bufferSize -> bufferSize
    java.lang.Object lock -> lock
    173:184:void <init>(java.nio.file.Path) -> <init>
    187:199:int read(java.nio.ByteBuffer,long) -> read
    203:220:void fillBuffer(long) -> fillBuffer
    223:236:void fillBufferUsingRandomAccessFile(long) -> fillBufferUsingRandomAccessFile
    239:242:void repairFileChannel() -> repairFileChannel
    245:255:void open() -> open
    258:279:void close() -> close
    282:287:void ensureOpen(java.util.function.Supplier) -> ensureOpen
    291:291:java.lang.String toString() -> toString
org.springframework.boot.loader.zip.FileDataBlock$Tracker -> org.springframework.boot.loader.zip.FileDataBlock$Tracker:
# {"fileName":"FileDataBlock.java","id":"sourceFile"}
    org.springframework.boot.loader.zip.FileDataBlock$Tracker NONE -> NONE
    void openedFileChannel(java.nio.file.Path) -> openedFileChannel
    void closedFileChannel(java.nio.file.Path) -> closedFileChannel
    301:301:void <clinit>() -> <clinit>
org.springframework.boot.loader.zip.FileDataBlock$Tracker$1 -> org.springframework.boot.loader.zip.FileDataBlock$Tracker$1:
# {"fileName":"FileDataBlock.java","id":"sourceFile"}
    301:301:void <init>() -> <init>
    305:305:void openedFileChannel(java.nio.file.Path) -> openedFileChannel
    309:309:void closedFileChannel(java.nio.file.Path) -> closedFileChannel
org.springframework.boot.loader.zip.NameOffsetLookups -> org.springframework.boot.loader.zip.NameOffsetLookups:
# {"fileName":"NameOffsetLookups.java","id":"sourceFile"}
    org.springframework.boot.loader.zip.NameOffsetLookups NONE -> NONE
    int offset -> offset
    java.util.BitSet enabled -> enabled
    36:39:void <init>(int,int) -> <init>
    42:47:void swap(int,int) -> swap
    50:50:int get(int) -> get
    54:57:int enable(int,boolean) -> enable
    61:61:boolean isEnabled(int) -> isEnabled
    65:65:boolean hasAnyEnabled() -> hasAnyEnabled
    69:69:org.springframework.boot.loader.zip.NameOffsetLookups emptyCopy() -> emptyCopy
    30:30:void <clinit>() -> <clinit>
org.springframework.boot.loader.zip.VirtualDataBlock -> org.springframework.boot.loader.zip.VirtualDataBlock:
# {"fileName":"VirtualDataBlock.java","id":"sourceFile"}
    org.springframework.boot.loader.zip.DataBlock[] parts -> parts
    long[] offsets -> offsets
    long size -> size
    int lastReadPart -> lastReadPart
    37:44:void <init>() -> <init>
    37:53:void <init>(java.util.Collection) -> <init>
    61:71:void setParts(java.util.Collection) -> setParts
    75:75:long size() -> size
    80:105:int read(java.nio.ByteBuffer,long) -> read
    61:61:org.springframework.boot.loader.zip.DataBlock[] lambda$setParts$0(int) -> lambda$setParts$0
org.springframework.boot.loader.zip.VirtualZipDataBlock -> org.springframework.boot.loader.zip.VirtualZipDataBlock:
# {"fileName":"VirtualZipDataBlock.java","id":"sourceFile"}
    org.springframework.boot.loader.zip.CloseableDataBlock data -> data
    44:71:void <init>(org.springframework.boot.loader.zip.CloseableDataBlock,org.springframework.boot.loader.zip.NameOffsetLookups,org.springframework.boot.loader.zip.ZipCentralDirectoryFileHeaderRecord[],long[]) -> <init>
    75:86:long addToCentral(java.util.List,org.springframework.boot.loader.zip.ZipCentralDirectoryFileHeaderRecord,long,org.springframework.boot.loader.zip.DataBlock,int) -> addToCentral
    92:104:long addToLocal(java.util.List,org.springframework.boot.loader.zip.ZipCentralDirectoryFileHeaderRecord,org.springframework.boot.loader.zip.ZipLocalFileHeaderRecord,org.springframework.boot.loader.zip.ZipDataDescriptorRecord,org.springframework.boot.loader.zip.DataBlock,org.springframework.boot.loader.zip.DataBlock) -> addToLocal
    109:110:void close() -> close
org.springframework.boot.loader.zip.VirtualZipDataBlock$DataPart -> org.springframework.boot.loader.zip.VirtualZipDataBlock$DataPart:
# {"fileName":"VirtualZipDataBlock.java","id":"sourceFile"}
    long offset -> offset
    long size -> size
    org.springframework.boot.loader.zip.VirtualZipDataBlock this$0 -> this$0
    121:124:void <init>(org.springframework.boot.loader.zip.VirtualZipDataBlock,long,long) -> <init>
    128:128:long size() -> size
    133:146:int read(java.nio.ByteBuffer,long) -> read
org.springframework.boot.loader.zip.Zip64EndOfCentralDirectoryLocator -> org.springframework.boot.loader.zip.Zip64EndOfCentralDirectoryLocator:
# {"fileName":"Zip64EndOfCentralDirectoryLocator.java","id":"sourceFile"}
    long pos -> pos
    int numberOfThisDisk -> numberOfThisDisk
    long offsetToZip64EndOfCentralDirectoryRecord -> offsetToZip64EndOfCentralDirectoryRecord
    int totalNumberOfDisks -> totalNumberOfDisks
    org.springframework.boot.loader.log.DebugLogger debug -> debug
    int SIGNATURE -> SIGNATURE
    int SIZE -> SIZE
    39:39:void <init>(long,int,long,int) -> <init>
    61:77:org.springframework.boot.loader.zip.Zip64EndOfCentralDirectoryLocator find(org.springframework.boot.loader.zip.DataBlock,long) -> find
    39:39:java.lang.String toString() -> toString
    39:39:int hashCode() -> hashCode
    39:39:boolean equals(java.lang.Object) -> equals
    39:39:long pos() -> pos
    39:39:int numberOfThisDisk() -> numberOfThisDisk
    39:39:long offsetToZip64EndOfCentralDirectoryRecord() -> offsetToZip64EndOfCentralDirectoryRecord
    39:39:int totalNumberOfDisks() -> totalNumberOfDisks
    42:42:void <clinit>() -> <clinit>
org.springframework.boot.loader.zip.Zip64EndOfCentralDirectoryRecord -> org.springframework.boot.loader.zip.Zip64EndOfCentralDirectoryRecord:
# {"fileName":"Zip64EndOfCentralDirectoryRecord.java","id":"sourceFile"}
    long size -> size
    long sizeOfZip64EndOfCentralDirectoryRecord -> sizeOfZip64EndOfCentralDirectoryRecord
    short versionMadeBy -> versionMadeBy
    short versionNeededToExtract -> versionNeededToExtract
    int numberOfThisDisk -> numberOfThisDisk
    int diskWhereCentralDirectoryStarts -> diskWhereCentralDirectoryStarts
    long numberOfCentralDirectoryEntriesOnThisDisk -> numberOfCentralDirectoryEntriesOnThisDisk
    long totalNumberOfCentralDirectoryEntries -> totalNumberOfCentralDirectoryEntries
    long sizeOfCentralDirectory -> sizeOfCentralDirectory
    long offsetToStartOfCentralDirectory -> offsetToStartOfCentralDirectory
    org.springframework.boot.loader.log.DebugLogger debug -> debug
    int SIGNATURE -> SIGNATURE
    int MINIMUM_SIZE -> MINIMUM_SIZE
    46:46:void <init>(long,long,short,short,int,int,long,long,long,long) -> <init>
    68:86:org.springframework.boot.loader.zip.Zip64EndOfCentralDirectoryRecord load(org.springframework.boot.loader.zip.DataBlock,org.springframework.boot.loader.zip.Zip64EndOfCentralDirectoryLocator) -> load
    46:46:java.lang.String toString() -> toString
    46:46:int hashCode() -> hashCode
    46:46:boolean equals(java.lang.Object) -> equals
    46:46:long size() -> size
    46:46:long sizeOfZip64EndOfCentralDirectoryRecord() -> sizeOfZip64EndOfCentralDirectoryRecord
    46:46:short versionMadeBy() -> versionMadeBy
    46:46:short versionNeededToExtract() -> versionNeededToExtract
    46:46:int numberOfThisDisk() -> numberOfThisDisk
    46:46:int diskWhereCentralDirectoryStarts() -> diskWhereCentralDirectoryStarts
    46:46:long numberOfCentralDirectoryEntriesOnThisDisk() -> numberOfCentralDirectoryEntriesOnThisDisk
    46:46:long totalNumberOfCentralDirectoryEntries() -> totalNumberOfCentralDirectoryEntries
    46:46:long sizeOfCentralDirectory() -> sizeOfCentralDirectory
    46:46:long offsetToStartOfCentralDirectory() -> offsetToStartOfCentralDirectory
    51:51:void <clinit>() -> <clinit>
org.springframework.boot.loader.zip.ZipCentralDirectoryFileHeaderRecord -> org.springframework.boot.loader.zip.ZipCentralDirectoryFileHeaderRecord:
# {"fileName":"ZipCentralDirectoryFileHeaderRecord.java","id":"sourceFile"}
    short versionMadeBy -> versionMadeBy
    short versionNeededToExtract -> versionNeededToExtract
    short generalPurposeBitFlag -> generalPurposeBitFlag
    short compressionMethod -> compressionMethod
    short lastModFileTime -> lastModFileTime
    short lastModFileDate -> lastModFileDate
    int crc32 -> crc32
    int compressedSize -> compressedSize
    int uncompressedSize -> uncompressedSize
    short fileNameLength -> fileNameLength
    short extraFieldLength -> extraFieldLength
    short fileCommentLength -> fileCommentLength
    short diskNumberStart -> diskNumberStart
    short internalFileAttributes -> internalFileAttributes
    int externalFileAttributes -> externalFileAttributes
    int offsetToLocalHeader -> offsetToLocalHeader
    org.springframework.boot.loader.log.DebugLogger debug -> debug
    int SIGNATURE -> SIGNATURE
    int MINIMUM_SIZE -> MINIMUM_SIZE
    int FILE_NAME_OFFSET -> FILE_NAME_OFFSET
    54:54:void <init>(short,short,short,short,short,short,int,int,int,short,short,short,short,short,int,int) -> <init>
    75:75:long size() -> size
    86:104:void copyTo(org.springframework.boot.loader.zip.DataBlock,long,java.util.zip.ZipEntry) -> copyTo
    115:124:long decodeMsDosFormatDateTime(short,short) -> decodeMsDosFormatDateTime
    128:129:int getChronoValue(long,java.time.temporal.ChronoField) -> getChronoValue
    139:143:org.springframework.boot.loader.zip.ZipCentralDirectoryFileHeaderRecord withFileNameLength(short) -> withFileNameLength
    153:157:org.springframework.boot.loader.zip.ZipCentralDirectoryFileHeaderRecord withOffsetToLocalHeader(int) -> withOffsetToLocalHeader
    165:184:byte[] asByteArray() -> asByteArray
    195:208:org.springframework.boot.loader.zip.ZipCentralDirectoryFileHeaderRecord load(org.springframework.boot.loader.zip.DataBlock,long) -> load
    54:54:java.lang.String toString() -> toString
    54:54:int hashCode() -> hashCode
    54:54:boolean equals(java.lang.Object) -> equals
    54:54:short versionMadeBy() -> versionMadeBy
    54:54:short versionNeededToExtract() -> versionNeededToExtract
    54:54:short generalPurposeBitFlag() -> generalPurposeBitFlag
    54:54:short compressionMethod() -> compressionMethod
    54:54:short lastModFileTime() -> lastModFileTime
    54:54:short lastModFileDate() -> lastModFileDate
    54:54:int crc32() -> crc32
    54:54:int compressedSize() -> compressedSize
    54:54:int uncompressedSize() -> uncompressedSize
    54:54:short fileNameLength() -> fileNameLength
    54:54:short extraFieldLength() -> extraFieldLength
    54:54:short fileCommentLength() -> fileCommentLength
    54:54:short diskNumberStart() -> diskNumberStart
    54:54:short internalFileAttributes() -> internalFileAttributes
    54:54:int externalFileAttributes() -> externalFileAttributes
    54:54:int offsetToLocalHeader() -> offsetToLocalHeader
    59:59:void <clinit>() -> <clinit>
org.springframework.boot.loader.zip.ZipContent -> org.springframework.boot.loader.zip.ZipContent:
# {"fileName":"ZipContent.java","id":"sourceFile"}
    java.lang.String META_INF -> META_INF
    byte[] SIGNATURE_SUFFIX -> SIGNATURE_SUFFIX
    org.springframework.boot.loader.log.DebugLogger debug -> debug
    java.util.Map cache -> cache
    org.springframework.boot.loader.zip.ZipContent$Source source -> source
    org.springframework.boot.loader.zip.ZipContent$Kind kind -> kind
    org.springframework.boot.loader.zip.FileDataBlock data -> data
    long centralDirectoryPos -> centralDirectoryPos
    long commentPos -> commentPos
    long commentLength -> commentLength
    int[] lookupIndexes -> lookupIndexes
    int[] nameHashLookups -> nameHashLookups
    int[] relativeCentralDirectoryOffsetLookups -> relativeCentralDirectoryOffsetLookups
    org.springframework.boot.loader.zip.NameOffsetLookups nameOffsetLookups -> nameOffsetLookups
    boolean hasJarSignatureFile -> hasJarSignatureFile
    java.lang.ref.SoftReference virtualData -> virtualData
    java.lang.ref.SoftReference info -> info
    101:113:void <init>(org.springframework.boot.loader.zip.ZipContent$Source,org.springframework.boot.loader.zip.ZipContent$Kind,org.springframework.boot.loader.zip.FileDataBlock,long,long,long,int[],int[],int[],org.springframework.boot.loader.zip.NameOffsetLookups,boolean) -> <init>
    121:121:org.springframework.boot.loader.zip.ZipContent$Kind getKind() -> getKind
    142:143:org.springframework.boot.loader.zip.CloseableDataBlock openRawZipData() -> openRawZipData
    147:153:org.springframework.boot.loader.zip.CloseableDataBlock getVirtualData() -> getVirtualData
    157:168:org.springframework.boot.loader.zip.CloseableDataBlock createVirtualData() -> createVirtualData
    176:176:int size() -> size
    185:191:java.lang.String getComment() -> getComment
    201:201:org.springframework.boot.loader.zip.ZipContent$Entry getEntry(java.lang.CharSequence) -> getEntry
    211:222:org.springframework.boot.loader.zip.ZipContent$Entry getEntry(java.lang.CharSequence,java.lang.CharSequence) -> getEntry
    232:243:boolean hasEntry(java.lang.CharSequence,java.lang.CharSequence) -> hasEntry
    253:256:org.springframework.boot.loader.zip.ZipContent$Entry getEntry(int) -> getEntry
    261:267:org.springframework.boot.loader.zip.ZipCentralDirectoryFileHeaderRecord loadZipCentralDirectoryFileHeaderRecord(long) -> loadZipCentralDirectoryFileHeaderRecord
    272:275:int nameHash(java.lang.CharSequence,java.lang.CharSequence) -> nameHash
    279:286:int getFirstLookupIndex(int) -> getFirstLookupIndex
    290:290:long getCentralDirectoryFileHeaderRecordPos(int) -> getCentralDirectoryFileHeaderRecordPos
    295:307:boolean hasName(int,org.springframework.boot.loader.zip.ZipCentralDirectoryFileHeaderRecord,long,java.lang.CharSequence,java.lang.CharSequence) -> hasName
    319:324:java.lang.Object getInfo(java.lang.Class,java.util.function.Function) -> getInfo
    336:336:boolean hasJarSignatureFile() -> hasJarSignatureFile
    345:346:void close() -> close
    350:350:java.lang.String toString() -> toString
    361:361:org.springframework.boot.loader.zip.ZipContent open(java.nio.file.Path) -> open
    373:373:org.springframework.boot.loader.zip.ZipContent open(java.nio.file.Path,java.lang.String) -> open
    377:392:org.springframework.boot.loader.zip.ZipContent open(org.springframework.boot.loader.zip.ZipContent$Source) -> open
    325:326:java.lang.Object lambda$getInfo$0(java.lang.Class,java.util.function.Function,java.lang.Class) -> lambda$getInfo$0
    67:71:void <clinit>() -> <clinit>
org.springframework.boot.loader.zip.ZipContent$Entry -> org.springframework.boot.loader.zip.ZipContent$Entry:
# {"fileName":"ZipContent.java","id":"sourceFile"}
    int lookupIndex -> lookupIndex
    org.springframework.boot.loader.zip.ZipCentralDirectoryFileHeaderRecord centralRecord -> centralRecord
    java.lang.String name -> name
    org.springframework.boot.loader.zip.FileDataBlock content -> content
    org.springframework.boot.loader.zip.ZipContent this$0 -> this$0
    711:714:void <init>(org.springframework.boot.loader.zip.ZipContent,int,org.springframework.boot.loader.zip.ZipCentralDirectoryFileHeaderRecord) -> <init>
    722:722:int getLookupIndex() -> getLookupIndex
    730:730:boolean isDirectory() -> isDirectory
    739:745:boolean hasNameStartingWith(java.lang.CharSequence) -> hasNameStartingWith
    754:762:java.lang.String getName() -> getName
    772:772:int getCompressionMethod() -> getCompressionMethod
    780:780:int getUncompressedSize() -> getUncompressedSize
    793:795:org.springframework.boot.loader.zip.CloseableDataBlock openContent() -> openContent
    799:809:org.springframework.boot.loader.zip.FileDataBlock getContent() -> getContent
    813:816:void checkNotZip64Extended(long) -> checkNotZip64Extended
    825:825:java.util.zip.ZipEntry as(java.util.function.Function) -> as
    836:842:java.util.zip.ZipEntry as(java.util.function.BiFunction) -> as
    825:825:java.util.zip.ZipEntry lambda$as$0(java.util.function.Function,org.springframework.boot.loader.zip.ZipContent$Entry,java.lang.String) -> lambda$as$0
org.springframework.boot.loader.zip.ZipContent$Kind -> org.springframework.boot.loader.zip.ZipContent$Kind:
# {"fileName":"ZipContent.java","id":"sourceFile"}
    org.springframework.boot.loader.zip.ZipContent$Kind ZIP -> ZIP
    org.springframework.boot.loader.zip.ZipContent$Kind NESTED_ZIP -> NESTED_ZIP
    org.springframework.boot.loader.zip.ZipContent$Kind NESTED_DIRECTORY -> NESTED_DIRECTORY
    org.springframework.boot.loader.zip.ZipContent$Kind[] $VALUES -> $VALUES
    400:400:org.springframework.boot.loader.zip.ZipContent$Kind[] values() -> values
    400:400:org.springframework.boot.loader.zip.ZipContent$Kind valueOf(java.lang.String) -> valueOf
    400:400:void <init>(java.lang.String,int) -> <init>
    400:400:org.springframework.boot.loader.zip.ZipContent$Kind[] $values() -> $values
    400:415:void <clinit>() -> <clinit>
org.springframework.boot.loader.zip.ZipContent$Loader -> org.springframework.boot.loader.zip.ZipContent$Loader:
# {"fileName":"ZipContent.java","id":"sourceFile"}
    java.nio.ByteBuffer buffer -> buffer
    org.springframework.boot.loader.zip.ZipContent$Source source -> source
    org.springframework.boot.loader.zip.FileDataBlock data -> data
    long centralDirectoryPos -> centralDirectoryPos
    int[] index -> index
    int[] nameHashLookups -> nameHashLookups
    int[] relativeCentralDirectoryOffsetLookups -> relativeCentralDirectoryOffsetLookups
    org.springframework.boot.loader.zip.NameOffsetLookups nameOffsetLookups -> nameOffsetLookups
    int cursor -> cursor
    448:475:void <init>(org.springframework.boot.loader.zip.ZipContent$Source,org.springframework.boot.loader.zip.ZipContent$Entry,org.springframework.boot.loader.zip.FileDataBlock,long,int) -> <init>
    479:487:void add(org.springframework.boot.loader.zip.ZipCentralDirectoryFileHeaderRecord,long,boolean) -> add
    490:501:org.springframework.boot.loader.zip.ZipContent finish(org.springframework.boot.loader.zip.ZipContent$Kind,long,long,boolean) -> finish
    508:532:void sort(int,int) -> sort
    535:539:void swap(int,int) -> swap
    542:545:void swap(int[],int,int) -> swap
    548:558:org.springframework.boot.loader.zip.ZipContent load(org.springframework.boot.loader.zip.ZipContent$Source) -> load
    562:563:org.springframework.boot.loader.zip.ZipContent loadNonNested(org.springframework.boot.loader.zip.ZipContent$Source) -> loadNonNested
    567:572:org.springframework.boot.loader.zip.ZipContent loadNestedZip(org.springframework.boot.loader.zip.ZipContent$Source,org.springframework.boot.loader.zip.ZipContent$Entry) -> loadNestedZip
    577:582:org.springframework.boot.loader.zip.ZipContent openAndLoad(org.springframework.boot.loader.zip.ZipContent$Source,org.springframework.boot.loader.zip.ZipContent$Kind,org.springframework.boot.loader.zip.FileDataBlock) -> openAndLoad
    587:623:org.springframework.boot.loader.zip.ZipContent loadContent(org.springframework.boot.loader.zip.ZipContent$Source,org.springframework.boot.loader.zip.ZipContent$Kind,org.springframework.boot.loader.zip.FileDataBlock) -> loadContent
    638:643:long getStartOfZipContent(org.springframework.boot.loader.zip.FileDataBlock,org.springframework.boot.loader.zip.ZipEndOfCentralDirectoryRecord,org.springframework.boot.loader.zip.Zip64EndOfCentralDirectoryRecord) -> getStartOfZipContent
    648:656:long getSizeOfCentralDirectoryAndEndRecords(org.springframework.boot.loader.zip.ZipEndOfCentralDirectoryRecord,org.springframework.boot.loader.zip.Zip64EndOfCentralDirectoryRecord) -> getSizeOfCentralDirectoryAndEndRecords
    661:686:org.springframework.boot.loader.zip.ZipContent loadNestedDirectory(org.springframework.boot.loader.zip.ZipContent$Source,org.springframework.boot.loader.zip.ZipContent,org.springframework.boot.loader.zip.ZipContent$Entry) -> loadNestedDirectory
org.springframework.boot.loader.zip.ZipContent$Source -> org.springframework.boot.loader.zip.ZipContent$Source:
# {"fileName":"ZipContent.java","id":"sourceFile"}
    java.nio.file.Path path -> path
    java.lang.String nestedEntryName -> nestedEntryName
    425:425:void <init>(java.nio.file.Path,java.lang.String) -> <init>
    432:432:boolean isNested() -> isNested
    437:437:java.lang.String toString() -> toString
    425:425:int hashCode() -> hashCode
    425:425:boolean equals(java.lang.Object) -> equals
    425:425:java.nio.file.Path path() -> path
    425:425:java.lang.String nestedEntryName() -> nestedEntryName
org.springframework.boot.loader.zip.ZipDataDescriptorRecord -> org.springframework.boot.loader.zip.ZipDataDescriptorRecord:
# {"fileName":"ZipDataDescriptorRecord.java","id":"sourceFile"}
    boolean includeSignature -> includeSignature
    int crc32 -> crc32
    int compressedSize -> compressedSize
    int uncompressedSize -> uncompressedSize
    org.springframework.boot.loader.log.DebugLogger debug -> debug
    int SIGNATURE -> SIGNATURE
    int DATA_SIZE -> DATA_SIZE
    int SIGNATURE_SIZE -> SIGNATURE_SIZE
    36:36:void <init>(boolean,int,int,int) -> <init>
    47:47:long size() -> size
    55:63:byte[] asByteArray() -> asByteArray
    74:87:org.springframework.boot.loader.zip.ZipDataDescriptorRecord load(org.springframework.boot.loader.zip.DataBlock,long) -> load
    97:97:boolean isPresentBasedOnFlag(org.springframework.boot.loader.zip.ZipLocalFileHeaderRecord) -> isPresentBasedOnFlag
    107:107:boolean isPresentBasedOnFlag(org.springframework.boot.loader.zip.ZipCentralDirectoryFileHeaderRecord) -> isPresentBasedOnFlag
    117:117:boolean isPresentBasedOnFlag(int) -> isPresentBasedOnFlag
    36:36:java.lang.String toString() -> toString
    36:36:int hashCode() -> hashCode
    36:36:boolean equals(java.lang.Object) -> equals
    36:36:boolean includeSignature() -> includeSignature
    36:36:int crc32() -> crc32
    36:36:int compressedSize() -> compressedSize
    36:36:int uncompressedSize() -> uncompressedSize
    38:38:void <clinit>() -> <clinit>
org.springframework.boot.loader.zip.ZipEndOfCentralDirectoryRecord -> org.springframework.boot.loader.zip.ZipEndOfCentralDirectoryRecord:
# {"fileName":"ZipEndOfCentralDirectoryRecord.java","id":"sourceFile"}
    short numberOfThisDisk -> numberOfThisDisk
    short diskWhereCentralDirectoryStarts -> diskWhereCentralDirectoryStarts
    short numberOfCentralDirectoryEntriesOnThisDisk -> numberOfCentralDirectoryEntriesOnThisDisk
    short totalNumberOfCentralDirectoryEntries -> totalNumberOfCentralDirectoryEntries
    int sizeOfCentralDirectory -> sizeOfCentralDirectory
    int offsetToStartOfCentralDirectory -> offsetToStartOfCentralDirectory
    short commentLength -> commentLength
    org.springframework.boot.loader.log.DebugLogger debug -> debug
    int SIGNATURE -> SIGNATURE
    int MAXIMUM_COMMENT_LENGTH -> MAXIMUM_COMMENT_LENGTH
    int MINIMUM_SIZE -> MINIMUM_SIZE
    int MAXIMUM_SIZE -> MAXIMUM_SIZE
    int BUFFER_SIZE -> BUFFER_SIZE
    int COMMENT_OFFSET -> COMMENT_OFFSET
    50:52:void <init>(short,int,int) -> <init>
    44:44:void <init>(short,short,short,short,int,int,short) -> <init>
    76:76:long size() -> size
    84:94:byte[] asByteArray() -> asByteArray
    106:110:org.springframework.boot.loader.zip.ZipEndOfCentralDirectoryRecord$Located load(org.springframework.boot.loader.zip.DataBlock) -> load
    114:137:long locate(org.springframework.boot.loader.zip.DataBlock,java.nio.ByteBuffer) -> locate
    141:147:int findInBuffer(java.nio.ByteBuffer) -> findInBuffer
    44:44:java.lang.String toString() -> toString
    44:44:int hashCode() -> hashCode
    44:44:boolean equals(java.lang.Object) -> equals
    44:44:short numberOfThisDisk() -> numberOfThisDisk
    44:44:short diskWhereCentralDirectoryStarts() -> diskWhereCentralDirectoryStarts
    44:44:short numberOfCentralDirectoryEntriesOnThisDisk() -> numberOfCentralDirectoryEntriesOnThisDisk
    44:44:short totalNumberOfCentralDirectoryEntries() -> totalNumberOfCentralDirectoryEntries
    44:44:int sizeOfCentralDirectory() -> sizeOfCentralDirectory
    44:44:int offsetToStartOfCentralDirectory() -> offsetToStartOfCentralDirectory
    44:44:short commentLength() -> commentLength
    54:54:void <clinit>() -> <clinit>
org.springframework.boot.loader.zip.ZipEndOfCentralDirectoryRecord$Located -> org.springframework.boot.loader.zip.ZipEndOfCentralDirectoryRecord$Located:
# {"fileName":"ZipEndOfCentralDirectoryRecord.java","id":"sourceFile"}
    long pos -> pos
    org.springframework.boot.loader.zip.ZipEndOfCentralDirectoryRecord endOfCentralDirectoryRecord -> endOfCentralDirectoryRecord
    156:156:void <init>(long,org.springframework.boot.loader.zip.ZipEndOfCentralDirectoryRecord) -> <init>
    156:156:java.lang.String toString() -> toString
    156:156:int hashCode() -> hashCode
    156:156:boolean equals(java.lang.Object) -> equals
    156:156:long pos() -> pos
    156:156:org.springframework.boot.loader.zip.ZipEndOfCentralDirectoryRecord endOfCentralDirectoryRecord() -> endOfCentralDirectoryRecord
org.springframework.boot.loader.zip.ZipLocalFileHeaderRecord -> org.springframework.boot.loader.zip.ZipLocalFileHeaderRecord:
# {"fileName":"ZipLocalFileHeaderRecord.java","id":"sourceFile"}
    short versionNeededToExtract -> versionNeededToExtract
    short generalPurposeBitFlag -> generalPurposeBitFlag
    short compressionMethod -> compressionMethod
    short lastModFileTime -> lastModFileTime
    short lastModFileDate -> lastModFileDate
    int crc32 -> crc32
    int compressedSize -> compressedSize
    int uncompressedSize -> uncompressedSize
    short fileNameLength -> fileNameLength
    short extraFieldLength -> extraFieldLength
    org.springframework.boot.loader.log.DebugLogger debug -> debug
    int SIGNATURE -> SIGNATURE
    int MINIMUM_SIZE -> MINIMUM_SIZE
    42:42:void <init>(short,short,short,short,short,int,int,int,short,short) -> <init>
    57:57:long size() -> size
    67:67:org.springframework.boot.loader.zip.ZipLocalFileHeaderRecord withExtraFieldLength(short) -> withExtraFieldLength
    78:78:org.springframework.boot.loader.zip.ZipLocalFileHeaderRecord withFileNameLength(short) -> withFileNameLength
    88:101:byte[] asByteArray() -> asByteArray
    112:122:org.springframework.boot.loader.zip.ZipLocalFileHeaderRecord load(org.springframework.boot.loader.zip.DataBlock,long) -> load
    42:42:java.lang.String toString() -> toString
    42:42:int hashCode() -> hashCode
    42:42:boolean equals(java.lang.Object) -> equals
    42:42:short versionNeededToExtract() -> versionNeededToExtract
    42:42:short generalPurposeBitFlag() -> generalPurposeBitFlag
    42:42:short compressionMethod() -> compressionMethod
    42:42:short lastModFileTime() -> lastModFileTime
    42:42:short lastModFileDate() -> lastModFileDate
    42:42:int crc32() -> crc32
    42:42:int compressedSize() -> compressedSize
    42:42:int uncompressedSize() -> uncompressedSize
    42:42:short fileNameLength() -> fileNameLength
    42:42:short extraFieldLength() -> extraFieldLength
    46:46:void <clinit>() -> <clinit>
org.springframework.boot.loader.zip.ZipString -> org.springframework.boot.loader.zip.ZipString:
# {"fileName":"ZipString.java","id":"sourceFile"}
    org.springframework.boot.loader.log.DebugLogger debug -> debug
    int BUFFER_SIZE -> BUFFER_SIZE
    int[] INITIAL_BYTE_BITMASK -> INITIAL_BYTE_BITMASK
    int SUBSEQUENT_BYTE_BITMASK -> SUBSEQUENT_BYTE_BITMASK
    int EMPTY_HASH -> EMPTY_HASH
    int EMPTY_SLASH_HASH -> EMPTY_SLASH_HASH
    49:50:void <init>() -> <init>
    60:60:int hash(java.lang.CharSequence,boolean) -> hash
    72:89:int hash(int,java.lang.CharSequence,boolean) -> hash
    104:137:int hash(java.nio.ByteBuffer,org.springframework.boot.loader.zip.DataBlock,long,int,boolean) -> hash
    154:163:boolean matches(java.nio.ByteBuffer,org.springframework.boot.loader.zip.DataBlock,long,int,java.lang.CharSequence,boolean) -> matches
    179:187:int startsWith(java.nio.ByteBuffer,org.springframework.boot.loader.zip.DataBlock,long,int,java.lang.CharSequence) -> startsWith
    193:239:int compare(java.nio.ByteBuffer,org.springframework.boot.loader.zip.DataBlock,long,int,java.lang.CharSequence,org.springframework.boot.loader.zip.ZipString$CompareType) -> compare
    243:243:boolean hasEnoughBytes(int,int,int) -> hasEnoughBytes
    247:247:boolean endsWith(java.lang.CharSequence,char) -> endsWith
    251:251:char getChar(java.lang.CharSequence,int) -> getChar
    263:272:java.lang.String readString(org.springframework.boot.loader.zip.DataBlock,long,long) -> readString
    278:291:int readInBuffer(org.springframework.boot.loader.zip.DataBlock,long,java.nio.ByteBuffer,int,int) -> readInBuffer
    295:305:int getCodePointSize(byte[],int) -> getCodePointSize
    309:314:int getCodePoint(byte[],int,int) -> getCodePoint
    37:47:void <clinit>() -> <clinit>
org.springframework.boot.loader.zip.ZipString$CompareType -> org.springframework.boot.loader.zip.ZipString$CompareType:
# {"fileName":"ZipString.java","id":"sourceFile"}
    org.springframework.boot.loader.zip.ZipString$CompareType MATCHES -> MATCHES
    org.springframework.boot.loader.zip.ZipString$CompareType MATCHES_ADDING_SLASH -> MATCHES_ADDING_SLASH
    org.springframework.boot.loader.zip.ZipString$CompareType STARTS_WITH -> STARTS_WITH
    org.springframework.boot.loader.zip.ZipString$CompareType[] $VALUES -> $VALUES
    320:320:org.springframework.boot.loader.zip.ZipString$CompareType[] values() -> values
    320:320:org.springframework.boot.loader.zip.ZipString$CompareType valueOf(java.lang.String) -> valueOf
    320:320:void <init>(java.lang.String,int) -> <init>
    320:320:org.springframework.boot.loader.zip.ZipString$CompareType[] $values() -> $values
    320:322:void <clinit>() -> <clinit>
