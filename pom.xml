<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.4</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.itcinfotech</groupId>
	<artifactId>windchill.complaints</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>windchill.complaints</name>
	<description>Microservice for Windchill Complaint Management</description>
	<url/>
	<licenses>
		<license/>
	</licenses>
	<developers>
		<developer/>
	</developers>
	<scm>
		<connection/>
		<developerConnection/>
		<tag/>
		<url/>
	</scm>
	<properties>
		<java.version>17</java.version>
		<azure.version>12.29.0</azure.version>
	</properties>
	<repositories>
		<repository>
			<id>central</id>
			<url>https://repo.maven.apache.org/maven2</url>
		</repository>
	</repositories>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.36</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-webflux</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.14.0</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>2.14.0</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-jsr310</artifactId>
			<version>2.14.0</version>
		</dependency>
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
			<version>2.3.0</version>
		</dependency>

		<dependency>
			<groupId>org.apache.camel.springboot</groupId>
			<artifactId>camel-spring-boot-starter</artifactId>
			<version>4.8.1</version>
		</dependency>

		<dependency>
			<groupId>com.azure</groupId>
			<artifactId>azure-storage-blob</artifactId>
			<version>12.28.1</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-csv</artifactId>
			<version>1.10.0</version>
		</dependency>

		<!-- yGuard dependency for obfuscation -->
		<dependency>
			<groupId>com.yworks</groupId>
			<artifactId>yguard</artifactId>
			<version>4.1.1</version>
			<scope>compile</scope>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>

			<!-- yGuard obfuscation plugin -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-antrun-plugin</artifactId>
				<version>3.1.0</version>
				<executions>
					<execution>
						<id>obfuscate</id>
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<target>
								<property name="runtime_classpath" refid="maven.runtime.classpath"/>
								<taskdef name="yguard" classname="com.yworks.yguard.YGuardTask" classpath="${runtime_classpath}"/>

								<!-- Create obfuscated version using yGuard -->
								<yguard>
									<inoutpair in="${project.build.directory}/${project.build.finalName}.jar"
											   out="${project.build.directory}/${project.build.finalName}-obfuscated.jar"/>

									<rename logfile="${project.build.directory}/yguard-log.xml"
											replaceClassNameStrings="true"
											conserveManifest="true">

										<property name="error-checking" value="pedantic"/>

										<keep>
											<class classes="public" methods="public" fields="public">
												<patternset>
													<include name="com.itcinfotech.windchill.complaints.WindchillComplaintsApplication"/>
												</patternset>
											</class>
										</keep>

										<keep>
											<class classes="public" methods="public" fields="public">
												<patternset>
													<include name="com.itcinfotech.windchill.complaints.controller.**"/>
													<include name="com.itcinfotech.windchill.complaints.service.**"/>
													<include name="com.itcinfotech.windchill.complaints.config.**"/>
													<include name="com.itcinfotech.windchill.complaints.dto.**"/>
													<include name="com.itcinfotech.windchill.complaints.request.**"/>
													<include name="com.itcinfotech.windchill.complaints.response.**"/>
													<include name="com.itcinfotech.windchill.complaints.exception.**"/>
												</patternset>
											</class>
										</keep>

									</rename>
								</yguard>

								<echo message="yGuard obfuscation completed successfully!"/>
								<echo message="Original JAR: ${project.build.directory}/${project.build.finalName}.jar"/>
								<echo message="Obfuscated JAR: ${project.build.directory}/${project.build.finalName}-obfuscated.jar"/>
								<echo message="Log file: ${project.build.directory}/yguard-log.xml"/>
							</target>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
