# ProGuard Configuration for Application Classes Only
# This configuration obfuscates only your business logic classes

# Basic obfuscation settings
-dontskipnonpubliclibraryclasses
-dontskipnonpubliclibraryclassmembers
-dontpreverify
-verbose

# Enable obfuscation for application classes only
-keep public class com.itcinfotech.windchill.complaints.WindchillComplaintsApplication {
    public static void main(java.lang.String[]);
}

# Keep Spring annotations and framework integration points
-keep @org.springframework.stereotype.Component class * {
    *;
}

-keep @org.springframework.stereotype.Service class * {
    *;
}

-keep @org.springframework.stereotype.Repository class * {
    *;
}

-keep @org.springframework.web.bind.annotation.RestController class * {
    *;
}

-keep @org.springframework.web.bind.annotation.Controller class * {
    *;
}

-keep @org.springframework.context.annotation.Configuration class * {
    *;
}

# Keep REST API endpoints
-keep class * {
    @org.springframework.web.bind.annotation.RequestMapping *;
    @org.springframework.web.bind.annotation.GetMapping *;
    @org.springframework.web.bind.annotation.PostMapping *;
    @org.springframework.web.bind.annotation.PutMapping *;
    @org.springframework.web.bind.annotation.DeleteMapping *;
    @org.springframework.web.bind.annotation.PatchMapping *;
}

# Keep Spring injection points
-keepclassmembers class * {
    @org.springframework.beans.factory.annotation.Autowired *;
    @org.springframework.beans.factory.annotation.Value *;
}

# Keep Jackson serialization
-keep class com.itcinfotech.windchill.complaints.dto.** {
    *;
}

-keep class com.itcinfotech.windchill.complaints.request.** {
    *;
}

-keep class com.itcinfotech.windchill.complaints.response.** {
    *;
}

# Keep exception classes
-keep class com.itcinfotech.windchill.complaints.exception.** {
    *;
}

# Keep Lombok annotations
-keep class * {
    @lombok.Data *;
    @lombok.Getter *;
    @lombok.Setter *;
    @lombok.Builder *;
    @lombok.AllArgsConstructor *;
    @lombok.NoArgsConstructor *;
    @lombok.RequiredArgsConstructor *;
}

# Keep Jackson annotations
-keep class * {
    @com.fasterxml.jackson.annotation.JsonProperty *;
    @com.fasterxml.jackson.annotation.JsonIgnore *;
    @com.fasterxml.jackson.annotation.JsonInclude *;
    @com.fasterxml.jackson.annotation.JsonCreator *;
}

# Keep Swagger annotations
-keep class * {
    @io.swagger.v3.oas.annotations.** *;
}

# Keep configuration properties
-keep @org.springframework.boot.context.properties.ConfigurationProperties class * {
    *;
}

# Keep attributes for proper Spring functionality
-keepattributes Signature,RuntimeVisibleAnnotations,AnnotationDefault,RuntimeVisibleParameterAnnotations
-keepattributes SourceFile,LineNumberTable
-keepattributes InnerClasses,EnclosingMethod

# Preserve enum classes
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep serialization methods
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Obfuscate private methods and fields in service classes
-keepclassmembers class com.itcinfotech.windchill.complaints.service.** {
    public *;
    protected *;
}

# Obfuscate private methods and fields in utility classes  
-keepclassmembers class com.itcinfotech.windchill.complaints.utils.** {
    public *;
    protected *;
}

# Allow access modification for better obfuscation
-allowaccessmodification

# Don't warn about missing classes (they're in the library JAR)
-dontwarn **

# Print mapping for debugging
-printmapping mapping-app.txt
-printseeds seeds-app.txt
-printusage usage-app.txt
