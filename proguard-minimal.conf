# Minimal ProGuard Configuration for Spring Boot
# This approach focuses on minimal obfuscation to avoid Spring Boot loader issues

# Basic settings - minimal intervention
-dontskipnonpubliclibraryclasses
-dontpreverify
-dontshrink
-dontoptimize
-verbose
-ignorewarnings
-dontwarn **

# Keep ALL Spring Boot infrastructure completely untouched
-keep class org.springframework.boot.loader.** { *; }
-keep class org.springframework.boot.** { *; }
-keep class org.springframework.** { *; }
-keep class org.apache.** { *; }
-keep class com.fasterxml.** { *; }
-keep class jakarta.** { *; }
-keep class javax.** { *; }
-keep class lombok.** { *; }
-keep class io.swagger.** { *; }
-keep class com.azure.** { *; }
-keep class ch.qos.** { *; }
-keep class org.slf4j.** { *; }
-keep class org.yaml.** { *; }
-keep class org.jboss.** { *; }
-keep class com.sun.** { *; }
-keep class sun.** { *; }
-keep class java.** { *; }
-keep class jdk.** { *; }

# Keep ALL META-INF and BOOT-INF structure
-keep class META-INF.** { *; }
-keep class BOOT-INF.** { *; }

# Keep main application class
-keep public class com.itcinfotech.windchill.complaints.WindchillComplaintsApplication {
    public static void main(java.lang.String[]);
}

# Keep ALL your application classes but allow field/method obfuscation
-keep class com.itcinfotech.windchill.complaints.** {
    public *;
    protected *;
}

# Keep all annotations
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes SourceFile,LineNumberTable
-keepattributes InnerClasses,EnclosingMethod

# Keep all enums
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep serialization
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Minimal obfuscation - only private fields and methods in specific packages
-keepclassmembers class com.itcinfotech.windchill.complaints.service.** {
    public *;
    protected *;
}

-keepclassmembers class com.itcinfotech.windchill.complaints.utils.** {
    public *;
    protected *;
}

# Print mapping
-printmapping mapping-minimal.txt
-printseeds seeds-minimal.txt
-printusage usage-minimal.txt
