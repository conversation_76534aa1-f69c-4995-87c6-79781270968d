# ProGuard Configuration for Spring Boot Windchill Complaints Microservice
# This configuration preserves Spring Boot functionality while obfuscating business logic

# Basic obfuscation settings - Conservative approach for Spring Boot
-dontskipnonpubliclibraryclasses
-dontskipnonpubliclibraryclassmembers
-dontpreverify
-dontoptimize
-dontshrink
-verbose

# Keep ALL Spring Boot and framework classes untouched
-keep class org.springframework.** { *; }
-keep class org.apache.** { *; }
-keep class com.fasterxml.** { *; }
-keep class jakarta.** { *; }
-keep class javax.** { *; }
-keep class lombok.** { *; }
-keep class io.swagger.** { *; }
-keep class com.azure.** { *; }
-keep class ch.qos.** { *; }
-keep class org.slf4j.** { *; }
-keep class org.yaml.** { *; }
-keep class org.jboss.** { *; }
-keep class com.sun.** { *; }
-keep class sun.** { *; }

# Handle Spring Boot fat JAR structure
-adaptclassstrings
-adaptresourcefilenames    **.properties,**.xml,**.txt,**.html,**.css,**.js
-adaptresourcefilecontents **.properties,**.xml,**.txt,**.html,**.css,**.js

# Ignore warnings about Spring Boot JAR structure
-ignorewarnings

# Keep main application class
-keep public class com.itcinfotech.windchill.complaints.WindchillComplaintsApplication {
    public static void main(java.lang.String[]);
}

# Keep ALL Spring Boot loader classes (critical for fat JAR execution)
-keep class org.springframework.boot.loader.** { *; }
-keep class org.springframework.boot.loader.launch.** { *; }
-keep class org.springframework.boot.loader.jar.** { *; }
-keep class org.springframework.boot.loader.zip.** { *; }
-keep class org.springframework.boot.loader.log.** { *; }

# Keep all META-INF and BOOT-INF structure
-keep class BOOT-INF.** { *; }
-keep class META-INF.** { *; }

# Keep Spring Boot auto-configuration classes
-keep class org.springframework.boot.autoconfigure.** { *; }

# Keep classes that are loaded via reflection by Spring Boot
-keepclassmembers class * {
    @org.springframework.boot.context.properties.ConfigurationProperties *;
}

# Keep all Spring Boot auto-configuration
-keep @org.springframework.boot.autoconfigure.SpringBootApplication class * {
    *;
}

# Keep Spring configuration classes
-keep @org.springframework.context.annotation.Configuration class * {
    *;
}

# Keep Spring components, services, repositories, controllers
-keep @org.springframework.stereotype.Component class * {
    *;
}

-keep @org.springframework.stereotype.Service class * {
    *;
}

-keep @org.springframework.stereotype.Repository class * {
    *;
}

-keep @org.springframework.web.bind.annotation.RestController class * {
    *;
}

-keep @org.springframework.web.bind.annotation.Controller class * {
    *;
}

# Keep REST API endpoints and their methods
-keep class * {
    @org.springframework.web.bind.annotation.RequestMapping *;
    @org.springframework.web.bind.annotation.GetMapping *;
    @org.springframework.web.bind.annotation.PostMapping *;
    @org.springframework.web.bind.annotation.PutMapping *;
    @org.springframework.web.bind.annotation.DeleteMapping *;
    @org.springframework.web.bind.annotation.PatchMapping *;
}

# Keep Spring Security configuration
-keep @org.springframework.security.config.annotation.web.configuration.EnableWebSecurity class * {
    *;
}

# Keep Spring Boot configuration properties
-keep @org.springframework.boot.context.properties.ConfigurationProperties class * {
    *;
}

# Keep classes with @Value annotations
-keepclassmembers class * {
    @org.springframework.beans.factory.annotation.Value *;
}

# Keep classes with @Autowired annotations
-keepclassmembers class * {
    @org.springframework.beans.factory.annotation.Autowired *;
}

# Keep Lombok generated methods and constructors
-keep class * {
    @lombok.Data *;
    @lombok.Getter *;
    @lombok.Setter *;
    @lombok.Builder *;
    @lombok.AllArgsConstructor *;
    @lombok.NoArgsConstructor *;
    @lombok.RequiredArgsConstructor *;
}

# Keep Jackson serialization/deserialization
-keep @com.fasterxml.jackson.annotation.JsonProperty class * {
    *;
}

-keep class * {
    @com.fasterxml.jackson.annotation.JsonProperty *;
    @com.fasterxml.jackson.annotation.JsonIgnore *;
    @com.fasterxml.jackson.annotation.JsonInclude *;
    @com.fasterxml.jackson.annotation.JsonCreator *;
}

# Keep record classes (Java 14+) - for DTOs and requests/responses
-keep class com.itcinfotech.windchill.complaints.request.** {
    *;
}

-keep class com.itcinfotech.windchill.complaints.response.** {
    *;
}

-keep class com.itcinfotech.windchill.complaints.dto.** {
    *;
}

# Keep exception classes
-keep class com.itcinfotech.windchill.complaints.exception.** {
    *;
}

# Keep Apache Camel routes and configurations
-keep @org.apache.camel.Component class * {
    *;
}

-keep class * extends org.apache.camel.builder.RouteBuilder {
    *;
}

# Keep Swagger/OpenAPI annotations
-keep @io.swagger.v3.oas.annotations.** class * {
    *;
}

-keep class * {
    @io.swagger.v3.oas.annotations.** *;
}

# Keep Spring Boot actuator endpoints
-keep class * extends org.springframework.boot.actuate.endpoint.annotation.** {
    *;
}

# Keep classes that use reflection (common in Spring)
-keepattributes Signature,RuntimeVisibleAnnotations,AnnotationDefault,RuntimeVisibleParameterAnnotations

# Keep generic signatures for proper Spring injection
-keepattributes Signature

# Keep source file names and line numbers for debugging
-keepattributes SourceFile,LineNumberTable

# Keep inner classes
-keepattributes InnerClasses,EnclosingMethod

# Preserve enum classes
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep serialization methods
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Azure SDK specific keeps
-keep class com.azure.** {
    *;
}

# Keep utility classes that might be used via reflection
-keep class com.itcinfotech.windchill.complaints.utils.** {
    public *;
}

# Keep test classes from obfuscation (if included in build)
-keep class com.itcinfotech.windchill.complaints.test.** {
    *;
}

# Don't obfuscate classes that implement interfaces (Spring often uses proxies)
-keep class * implements java.lang.reflect.InvocationHandler {
    *;
}

# Keep Spring Boot CommandLineRunner implementations
-keep class * implements org.springframework.boot.CommandLineRunner {
    *;
}

# Keep classes with @Bean methods
-keepclassmembers class * {
    @org.springframework.context.annotation.Bean *;
}

# Additional Spring annotations to preserve
-keep class * {
    @org.springframework.scheduling.annotation.Scheduled *;
    @org.springframework.transaction.annotation.Transactional *;
    @org.springframework.cache.annotation.Cacheable *;
    @org.springframework.security.access.prepost.PreAuthorize *;
}

# Keep method parameter names for Spring MVC
-keepparameternames

# Don't warn about missing classes (common with optional dependencies)
-dontwarn javax.**
-dontwarn org.slf4j.**
-dontwarn org.apache.**
-dontwarn com.fasterxml.**
-dontwarn lombok.**
-dontwarn org.springframework.**
-dontwarn com.azure.**

# Keep Spring Boot META-INF files
-keepdirectories META-INF/**
-keep class META-INF.** { *; }

# Preserve Spring Boot configuration files
-adaptresourcefilenames **.properties,**.xml,**.yml,**.yaml,**.json
-adaptresourcefilecontents **.properties,**.xml,**.yml,**.yaml,**.json

# Keep Spring factories and auto-configuration
-keep class * implements org.springframework.boot.autoconfigure.AutoConfiguration { *; }
-keep class * implements org.springframework.context.ApplicationContextInitializer { *; }
-keep class * implements org.springframework.context.ApplicationListener { *; }

# Selective obfuscation - Only obfuscate specific business logic classes
# Obfuscate private methods and fields in service classes
-keepclassmembers class com.itcinfotech.windchill.complaints.service.** {
    public *;
    protected *;
}

# Obfuscate private methods and fields in utility classes
-keepclassmembers class com.itcinfotech.windchill.complaints.utils.** {
    public *;
    protected *;
}

# Print mapping for debugging
-printmapping mapping.txt
-printseeds seeds.txt
-printusage usage.txt
