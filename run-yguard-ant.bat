@echo off
REM yGuard obfuscation script using Ant

echo ========================================
echo yGuard Obfuscation with Ant
echo ========================================
echo.

REM Check if input JAR exists
if not exist "target\windchill.complaints-0.0.1-SNAPSHOT.jar" (
    echo ERROR: Input JAR not found
    echo Please run: mvn clean package -DskipTests
    pause
    exit /b 1
)

REM Set up paths
set YGUARD_JAR=%USERPROFILE%\.m2\repository\com\yworks\yguard\4.1.1\yguard-4.1.1.jar
set ANT_JAR=%USERPROFILE%\.m2\repository\org\apache\ant\ant\1.10.14\ant-1.10.14.jar
set ANT_LAUNCHER_JAR=%USERPROFILE%\.m2\repository\org\apache\ant\ant-launcher\1.10.14\ant-launcher-1.10.14.jar

REM Check if yGuard JAR exists
if not exist "%YGUARD_JAR%" (
    echo Downloading yGuard...
    mvn dependency:get -Dartifact=com.yworks:yguard:4.1.1 -q
)

REM Check if Ant JARs exist
if not exist "%ANT_JAR%" (
    echo Downloading Ant...
    mvn dependency:get -Dartifact=org.apache.ant:ant:1.10.14 -q
)

if not exist "%ANT_LAUNCHER_JAR%" (
    echo Downloading Ant Launcher...
    mvn dependency:get -Dartifact=org.apache.ant:ant-launcher:1.10.14 -q
)

REM Check if configuration file exists
if not exist "yguard-simple.xml" (
    echo ERROR: yGuard configuration file not found
    echo Please ensure yguard-simple.xml exists
    pause
    exit /b 1
)

echo Step 1: Verifying files...
echo ✅ Input JAR: target\windchill.complaints-0.0.1-SNAPSHOT.jar
echo ✅ yGuard JAR: %YGUARD_JAR%
echo ✅ Ant JAR: %ANT_JAR%
echo ✅ Configuration: yguard-simple.xml
echo.

echo Step 2: Running yGuard obfuscation...
java -cp "%ANT_JAR%;%ANT_LAUNCHER_JAR%" org.apache.tools.ant.Main -f yguard-simple.xml obfuscate

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERROR: yGuard obfuscation failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Success! yGuard Obfuscation Completed
echo ========================================

REM Verify results
if exist "target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar" (
    echo ✅ Obfuscated JAR created successfully
    echo    Location: target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar
    
    REM Show file sizes
    for %%I in ("target\windchill.complaints-0.0.1-SNAPSHOT.jar") do echo    Original size:    %%~zI bytes
    for %%I in ("target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar") do echo    Obfuscated size:  %%~zI bytes
) else (
    echo ❌ Obfuscated JAR not found
)

if exist "target\yguard-simple.xml" (
    echo ✅ Log file: target\yguard-simple.xml
) else (
    echo ⚠️  Log file not found
)

echo.
echo ========================================
echo Testing and Verification
echo ========================================
echo.
echo 1. Test the obfuscated JAR:
echo    java -jar target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar
echo.
echo 2. Compare class names (should show obfuscated names):
echo    jar -tf target\windchill.complaints-0.0.1-SNAPSHOT.jar ^| findstr "com/itcinfotech" ^| head -10
echo    jar -tf target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar ^| findstr "com/itcinfotech" ^| head -10
echo.
echo 3. Check obfuscation log:
echo    type target\yguard-simple.xml
echo.

pause
