@echo off
REM Simple yGuard obfuscation script using Ant

echo ========================================
echo yGuard Obfuscation Script
echo ========================================
echo.

REM Check if input JAR exists
if not exist "target\windchill.complaints-0.0.1-SNAPSHOT.jar" (
    echo ERROR: Input JAR not found
    echo Please run: mvn clean package -DskipTests
    pause
    exit /b 1
)

REM Check if yGuard JAR exists
set YGUARD_JAR=%USERPROFILE%\.m2\repository\com\yworks\yguard\4.1.1\yguard-4.1.1.jar
if not exist "%YGUARD_JAR%" (
    echo ERROR: yGuard JAR not found in Maven repository
    echo Please run: mvn dependency:resolve
    pause
    exit /b 1
)

echo Step 1: Checking yGuard configuration...
if not exist "yguard-config.xml" (
    echo ERROR: yGuard configuration file not found
    echo Please ensure yguard-config.xml exists
    pause
    exit /b 1
)

echo Step 2: Running yGuard obfuscation...
echo Input JAR: target\windchill.complaints-0.0.1-SNAPSHOT.jar
echo yGuard JAR: %YGUARD_JAR%
echo Configuration: yguard-config.xml
echo.

REM Download Ant if not available
set ANT_JAR=%USERPROFILE%\.m2\repository\org\apache\ant\ant\1.10.14\ant-1.10.14.jar
if not exist "%ANT_JAR%" (
    echo Downloading Ant...
    mvn dependency:get -Dartifact=org.apache.ant:ant:1.10.14 -q
)

REM Run yGuard using Ant
java -cp "%YGUARD_JAR%;%ANT_JAR%" org.apache.tools.ant.Main -f yguard-config.xml obfuscate

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERROR: yGuard obfuscation failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo yGuard Obfuscation Completed!
echo ========================================

REM Check results
if exist "target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar" (
    echo ✅ Obfuscated JAR created successfully
    echo    Location: target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar
    
    REM Show file sizes
    for %%I in ("target\windchill.complaints-0.0.1-SNAPSHOT.jar") do echo    Original size:    %%~zI bytes
    for %%I in ("target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar") do echo    Obfuscated size:  %%~zI bytes
) else (
    echo ❌ Obfuscated JAR not found
)

if exist "target\yguard-obfuscation.xml" (
    echo ✅ Log file: target\yguard-obfuscation.xml
) else (
    echo ⚠️  Log file not found
)

echo.
echo Next steps:
echo 1. Test the obfuscated JAR:
echo    java -jar target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar
echo.
echo 2. Compare class names:
echo    jar -tf target\windchill.complaints-0.0.1-SNAPSHOT.jar ^| findstr "com/itcinfotech"
echo    jar -tf target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar ^| findstr "com/itcinfotech"
echo.

pause
