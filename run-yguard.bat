@echo off
REM Simple script to run yGuard obfuscation directly

echo ========================================
echo Running yGuard Obfuscation
echo ========================================
echo.

REM Check if JAR exists
if not exist "target\windchill.complaints-0.0.1-SNAPSHOT.jar" (
    echo ERROR: JAR file not found. Please run 'mvn clean package -DskipTests' first
    pause
    exit /b 1
)

REM Check if yGuard JAR exists in Maven repository
set YGUARD_JAR=%USERPROFILE%\.m2\repository\com\yworks\yguard\4.1.1\yguard-4.1.1.jar
if not exist "%YGUARD_JAR%" (
    echo ERROR: yGuard JAR not found in Maven repository
    echo Please run 'mvn dependency:resolve' first
    pause
    exit /b 1
)

echo Step 1: Running yGuard obfuscation...
java -cp "%YGUARD_JAR%" com.yworks.yguard.YGuardTask ^
    -injar target\windchill.complaints-0.0.1-SNAPSHOT.jar ^
    -outjar target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar ^
    -logfile target\yguard-simple.xml ^
    -keep "com.itcinfotech.windchill.complaints.WindchillComplaintsApplication" ^
    -keep "com.itcinfotech.windchill.complaints.controller.**" ^
    -keep "com.itcinfotech.windchill.complaints.service.**" ^
    -keep "com.itcinfotech.windchill.complaints.config.**" ^
    -keep "com.itcinfotech.windchill.complaints.dto.**" ^
    -keep "com.itcinfotech.windchill.complaints.request.**" ^
    -keep "com.itcinfotech.windchill.complaints.response.**" ^
    -keep "com.itcinfotech.windchill.complaints.exception.**"

if %ERRORLEVEL% neq 0 (
    echo ERROR: yGuard obfuscation failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo yGuard Obfuscation Completed!
echo ========================================
echo Original JAR: target\windchill.complaints-0.0.1-SNAPSHOT.jar
echo Obfuscated JAR: target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar
echo Log file: target\yguard-simple.xml
echo.

REM Show file sizes
for %%I in ("target\windchill.complaints-0.0.1-SNAPSHOT.jar") do echo Original size:    %%~zI bytes
for %%I in ("target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar") do echo Obfuscated size:  %%~zI bytes

echo.
pause
