package com.itcinfotech.windchill.complaints.camel;

import com.itcinfotech.windchill.complaints.request.ComplaintRequest;
import com.itcinfotech.windchill.complaints.utils.ZipUtil;
import org.apache.camel.builder.RouteBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;


@Component
public class FileWatcherRoute extends RouteBuilder {

    @Value("${camel.files.root}")
    private String rootDir;

    @Autowired
    private ZipUtil zipUtil;

    @Override
    public void configure() throws Exception {

        // Route 1 - Move ZIP from Incoming to Processing
        from("file:" + rootDir + "/IntegrationRoot/Incoming"
                + "?include=.*\\.zip"
                + "&move=../Processing/${file:name}"
                + "&readLock=none"
                + "&idempotent=false")
                .routeId("moveZipRoute")
                .log("📦 ZIP ${file:name} moved to /Processing");


        // Route 2 - Unzip file in /Processing folder with proper error handling
        from("file:" + rootDir + "/IntegrationRoot/Processing"
                + "?include=.*\\.zip"
                + "&noop=false"                    // File will be consumed/moved after processing
                + "&move=../Success/${file:name}"  // Move to Success on successful processing
                + "&moveFailed=../Error/${file:name}" // Move to Error on failure
                + "&readLock=changed"              // Wait for file to stop changing
                + "&readLockTimeout=10000"         // 10 second timeout for file lock
                + "&delay=5000"                    // 5 second delay between polls
                + "&maxMessagesPerPoll=1")         // Process one file at a time
                .routeId("unzipRoute")
                .onException(Exception.class)
                    .handled(true)
                    .log("ERROR processing ZIP file: ${exception.message}")
                    .to("log:error?level=ERROR")
                    .end()
                .process(exchange -> {
                    String fileName = exchange.getIn().getHeader("CamelFileName", String.class);
                    File file = exchange.getIn().getBody(File.class);

                    log.info("Starting unzip of: {}", fileName);

                    try {
                        if (file != null && file.exists()) {
                            zipUtil.unzip(file.getAbsolutePath(), rootDir + "/IntegrationRoot/Processing");
                            log.info("Unzip completed successfully for: {}", fileName);
                        } else {
                            throw new RuntimeException("ZIP file not found in /Processing: " + fileName);
                        }
                    } catch (Exception e) {
                        log.error("Error processing ZIP file {}: {}", fileName, e.getMessage(), e);
                        throw e; // Re-throw to trigger error handling
                    }
                });
    }

}
