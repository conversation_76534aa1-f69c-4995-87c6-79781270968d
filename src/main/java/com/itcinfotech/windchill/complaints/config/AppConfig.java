package com.itcinfotech.windchill.complaints.config;

import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
public class AppConfig {

    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        RestTemplate restTemplate = builder.build();

        // Adicionando o interceptador
        restTemplate.getInterceptors().add((request, body, execution) -> {
            // Log do cabeçalho
//            System.out.println("Request URI: " + request.getURI());
//            System.out.println("Request Method: " + request.getMethod());
//            System.out.println("Request Headers: " + request.getHeaders());
//
//            // Log do corpo da requisição (apenas em caso de POST/PUT)
//            if (body != null && body.length > 0) {
//                System.out.println("Request Body: " + new String(body));
//            }

            // Prossegue a execução da requisição
            return execution.execute(request, body);
        });

        return restTemplate;
    }
}
