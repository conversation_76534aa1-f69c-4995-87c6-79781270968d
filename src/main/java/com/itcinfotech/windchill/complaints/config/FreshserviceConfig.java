package com.itcinfotech.windchill.complaints.config;

import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Configuration class for Freshservice integration
 */
@Configuration
public class FreshserviceConfig {

    @Value("${freshservice.api.url}")
    private String apiUrl;
    
    @Value("${freshservice.api.key}")
    private String apiKey;
    
    @Value("${freshservice.attachments.max.size}")
    private long maxAttachmentSize;
    
    @Value("${freshservice.attachments.allowed.extensions}")
    private String allowedExtensions;
    
    /**
     * Creates a temporary directory for storing file attachments
     * 
     * @return Path to the temporary directory
     * @throws IOException If there's an error creating the directory
     */
    @Bean
    public Path tempAttachmentDirectory() throws IOException {
        Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"), "freshservice-attachments");
        if (!Files.exists(tempDir)) {
            Files.createDirectories(tempDir);
        }
        return tempDir;
    }
    
    /**
     * Validates the Freshservice configuration
     * 
     * @throws IllegalStateException If the configuration is invalid
     */
    @PostConstruct
    public void validateFreshserviceConfig() {
        if (apiUrl == null || apiUrl.isEmpty()) {
            throw new IllegalStateException("Freshservice API URL is not configured");
        }
        
        if (apiKey == null || apiKey.isEmpty()) {
            throw new IllegalStateException("Freshservice API key is not configured");
        }
        
        if (maxAttachmentSize <= 0) {
            throw new IllegalStateException("Invalid maximum attachment size configured");
        }
        
        if (allowedExtensions == null || allowedExtensions.isEmpty()) {
            throw new IllegalStateException("No allowed file extensions configured for Freshservice attachments");
        }
    }
}
