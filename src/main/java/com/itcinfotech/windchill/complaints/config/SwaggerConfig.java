package com.itcinfotech.windchill.complaints.config;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Contact;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.tags.Tag;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@OpenAPIDefinition(
        info = @Info(
                title = "Windchill Complaints Microservice",
                version = "v1",
                description = "API for managing Windchill complaints and Freshservice tickets",
                contact = @Contact(name = "DxP Services, an ITC Infotech brand.", url = "https://www.itcinfotech.com")
        ),
        security = @SecurityRequirement(name = "basicScheme")
)
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .components(new Components()
                        .addSecuritySchemes("basicScheme",
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("basic")))
                .addTagsItem(new Tag().name("Freshservice Tickets").description("Operations related to Freshservice tickets"))
                .addTagsItem(new Tag().name("Complaints").description("Operations related to Windchill complaints"));
    }
}
