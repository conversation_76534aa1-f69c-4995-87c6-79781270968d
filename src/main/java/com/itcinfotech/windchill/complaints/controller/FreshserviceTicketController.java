package com.itcinfotech.windchill.complaints.controller;

import com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest;
import com.itcinfotech.windchill.complaints.request.FreshserviceTicketCreateRequest;
import com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/api/v1/freshservice/tickets")
@Tag(name = "Freshservice Tickets", description = "API endpoints for managing Freshservice tickets and attachments")
public class FreshserviceTicketController {

    private static final Logger logger = LoggerFactory.getLogger(FreshserviceTicketController.class);
    private final FreshserviceTicketService freshserviceTicketService;

    @Autowired
    public FreshserviceTicketController(FreshserviceTicketService freshserviceTicketService) {
        this.freshserviceTicketService = freshserviceTicketService;
    }

    @Operation(summary = "Create a Freshservice Ticket")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK -> Ticket created in Freshservice successfully."),
            @ApiResponse(responseCode = "500", description = "ERROR -> Internal error while creating the ticket"),
            @ApiResponse(responseCode = "503", description = "ERROR -> Freshservice API is currently unavailable")
    })
    @PostMapping
    public ResponseEntity<Object> createTicket(@RequestBody FreshserviceTicketRequest request) {
        // Let exceptions propagate to the GlobalExceptionHandler
        return freshserviceTicketService.createTicket(request);
    }

    @Operation(summary = "Create a Freshservice Ticket with Attachments")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK -> Ticket created in Freshservice with attachments successfully."),
            @ApiResponse(responseCode = "500", description = "ERROR -> Internal error while creating the ticket with attachments"),
            @ApiResponse(responseCode = "503", description = "ERROR -> Freshservice API is currently unavailable")
    })
    @PostMapping(value = "/with-attachments", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<Object> createTicketWithAttachments(
            @RequestPart("ticket") FreshserviceTicketRequest ticketRequest,
            @RequestPart(value = "attachments", required = false) List<MultipartFile> attachments) {
        // Let exceptions propagate to the GlobalExceptionHandler
        logger.info("Creating Freshservice ticket with attachments. Ticket data: {}, Attachments count: {}", 
                    ticketRequest, attachments != null ? attachments.size() : 0);
        FreshserviceTicketCreateRequest request = new FreshserviceTicketCreateRequest(ticketRequest, attachments);
        return freshserviceTicketService.createTicketWithAttachments(request);
    }

    @Operation(summary = "Add Attachments to an Existing Freshservice Ticket")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK -> Attachments added to the ticket successfully."),
            @ApiResponse(responseCode = "500", description = "ERROR -> Internal error while adding attachments to the ticket"),
            @ApiResponse(responseCode = "503", description = "ERROR -> Freshservice API is currently unavailable")
    })
    @PostMapping(value = "/{ticketId}/attachments", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<Object> addAttachmentsToTicket(
            @PathVariable Long ticketId,
            @RequestPart("attachments") List<MultipartFile> attachments) {
        // Let exceptions propagate to the GlobalExceptionHandler
        logger.info("Adding attachments to Freshservice ticket ID: {}. Attachments count: {}", 
                    ticketId, attachments != null ? attachments.size() : 0);
        return freshserviceTicketService.addAttachmentsToTicket(ticketId, attachments);
    }

    @Operation(summary = "Get a Freshservice Ticket by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK -> Ticket retrieved successfully."),
            @ApiResponse(responseCode = "404", description = "ERROR -> Ticket not found"),
            @ApiResponse(responseCode = "500", description = "ERROR -> Internal error while retrieving the ticket"),
            @ApiResponse(responseCode = "503", description = "ERROR -> Freshservice API is currently unavailable")
    })
    @GetMapping("/{ticketId}")
    public ResponseEntity<Object> getTicketById(@PathVariable Long ticketId) {
        logger.info("Retrieving Freshservice ticket with ID: {}", ticketId);
        // Let exceptions propagate to the GlobalExceptionHandler
        return freshserviceTicketService.getTicketById(ticketId);
    }

    @Operation(summary = "Get Multiple Freshservice Tickets by IDs")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK -> Tickets retrieved successfully."),
            @ApiResponse(responseCode = "400", description = "ERROR -> No ticket IDs provided"),
            @ApiResponse(responseCode = "500", description = "ERROR -> Internal error while retrieving tickets"),
            @ApiResponse(responseCode = "503", description = "ERROR -> Freshservice API is currently unavailable")
    })
    @GetMapping("/batch")
    public ResponseEntity<Object> getTicketsByIds(@RequestParam("ids") List<Long> ticketIds) {
        logger.info("Retrieving multiple Freshservice tickets: {}", ticketIds);
        // Let exceptions propagate to the GlobalExceptionHandler
        return freshserviceTicketService.getTicketsByIds(ticketIds);
    }

    @Operation(summary = "Get Attachments for a Freshservice Ticket")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK -> Attachments retrieved successfully."),
            @ApiResponse(responseCode = "404", description = "ERROR -> Ticket not found"),
            @ApiResponse(responseCode = "500", description = "ERROR -> Internal error while retrieving attachments"),
            @ApiResponse(responseCode = "503", description = "ERROR -> Freshservice API is currently unavailable")
    })
    @GetMapping("/{ticketId}/attachments")
    public ResponseEntity<Object> getTicketAttachments(@PathVariable Long ticketId) {
        logger.info("Retrieving attachments for Freshservice ticket with ID: {}", ticketId);
        // Let exceptions propagate to the GlobalExceptionHandler
        return freshserviceTicketService.getTicketAttachments(ticketId);
    }

    @Operation(summary = "Download an Attachment from Freshservice")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK -> Attachment downloaded successfully."),
            @ApiResponse(responseCode = "404", description = "ERROR -> Attachment not found"),
            @ApiResponse(responseCode = "500", description = "ERROR -> Internal error while downloading the attachment"),
            @ApiResponse(responseCode = "503", description = "ERROR -> Freshservice API is currently unavailable")
    })
    @GetMapping("/attachments/{attachmentId}")
    public ResponseEntity<Object> downloadAttachment(@PathVariable Long attachmentId) {
        logger.info("Downloading Freshservice attachment with ID: {}", attachmentId);
        // Let exceptions propagate to the GlobalExceptionHandler
        return freshserviceTicketService.downloadAttachment(attachmentId);
    }
}
