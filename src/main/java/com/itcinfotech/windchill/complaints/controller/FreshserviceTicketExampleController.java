package com.itcinfotech.windchill.complaints.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceAttachmentResponse;
import com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceAttachmentResponseWrapper;
import com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest;
import com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketResponse;
import com.itcinfotech.windchill.complaints.request.FreshserviceTicketCreateRequest;
import com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketExample;
import com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Controller for testing the FreshserviceTicketExample
 */
@RestController
@RequestMapping("/api/v1/freshservice/test")
public class FreshserviceTicketExampleController {

    private static final Logger logger = LoggerFactory.getLogger(FreshserviceTicketExampleController.class);
    
    private final FreshserviceTicketExample freshserviceTicketExample;
    private final FreshserviceTicketService freshserviceTicketService;
    private final RestTemplate restTemplate;
    
    @Value("${freshservice.api.url}")
    private String freshserviceBaseUrl;
    
    @Value("${freshservice.api.key}")
    private String freshserviceApiKey;
    
    @Autowired
    public FreshserviceTicketExampleController(FreshserviceTicketExample freshserviceTicketExample,
                                              FreshserviceTicketService freshserviceTicketService,
                                              RestTemplate restTemplate) {
        this.freshserviceTicketExample = freshserviceTicketExample;
        this.freshserviceTicketService = freshserviceTicketService;
        this.restTemplate = restTemplate;
    }
    
    /**
     * Test endpoint to create a ticket without attachments
     * 
     * @return ResponseEntity with the created ticket or error message
     */
    @PostMapping("/create-ticket")
    @Operation(
        summary = "Create a ticket without attachments",
        description = "Creates a new ticket in Freshservice without any attachments",
        responses = {
            @ApiResponse(responseCode = "200", description = "Ticket created successfully", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = FreshserviceTicketResponse.class))),
            @ApiResponse(responseCode = "400", description = "Bad request"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
        }
    )
    public ResponseEntity<Object> testCreateTicket() {
        logger.info("Testing ticket creation without attachments");
        try {
            // Create a direct JSON string with required fields including custom fields
            String ticketJson = "{\n" +
                "  \"name\": \"Test User\",\n" +
                "  \"email\": \"<EMAIL>\",\n" +
                "  \"subject\": \"Test Ticket Request\",\n" +
                "  \"description\": \"This is a test ticket created for testing purposes.\",\n" +
                "  \"status\": 2,\n" +
                "  \"priority\": 1,\n" +
                "  \"source\": 2,\n" +
                "  \"category\": \"Applications\",\n" +
                "  \"sub_category\": \"Troubleshooting\",\n" +
                "  \"type\": \"Incident\",\n" +
                "  \"impact\": 1,\n" +
                "  \"urgency\": 1,\n" +
                "  \"custom_fields\": {\n" +
                "    \"lf_what_solution_is_having_an_issue\": ***********\n" +
                "  }\n" +
                "}";
            
            // Make a direct REST call to create the ticket
            HttpHeaders headers = new HttpHeaders();
            String auth = freshserviceApiKey + ":X";
            byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes());
            String authHeader = "Basic " + new String(encodedAuth);
            headers.set("Authorization", authHeader);
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<String> entity = new HttpEntity<>(ticketJson, headers);
            
            ResponseEntity<String> response = restTemplate.exchange(
                freshserviceBaseUrl + "/tickets",
                HttpMethod.POST,
                entity,
                String.class
            );
            
            if (response.getStatusCode().is2xxSuccessful()) {
                // Parse the response
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode rootNode = objectMapper.readTree(response.getBody());
                JsonNode ticketNode = rootNode.get("ticket");
                
                if (ticketNode != null && ticketNode.has("id")) {
                    Long ticketId = ticketNode.get("id").asLong();
                    logger.info("Successfully created test ticket with ID: {}", ticketId);
                    
                    // Create a minimal ticket response object
                    FreshserviceTicketResponse ticketResponse = new FreshserviceTicketResponse();
                    ticketResponse.setId(ticketId);
                    ticketResponse.setSubject("Test Ticket Request");
                    
                    return ResponseEntity.ok(ticketResponse);
                } else {
                    logger.error("Failed to parse ticket response: {}", response.getBody());
                    return ResponseEntity.badRequest().body("Failed to parse ticket response");
                }
            } else {
                logger.error("Failed to create test ticket: {}", response.getBody());
                return ResponseEntity.badRequest().body("Failed to create test ticket: " + response.getBody());
            }
        } catch (Exception e) {
            logger.error("Error creating test ticket: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body("Error creating test ticket: " + e.getMessage());
        }
    }
    
    /**
     * Unified endpoint to create a ticket with attachments
     * Supports different methods of creating tickets with attachments
     * 
     * @param attachments List of file attachments
     * @param method The method to use for creating the ticket with attachments:
     *               "two-step" - Create ticket first, then add attachments
     *               "single-call" - Create ticket with attachments in a single API call
     *               "example" - Use the example service to create ticket with attachments
     * @return ResponseEntity with the created ticket or error message
     */
    @PostMapping(value = "/create-ticket-with-attachments", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(
        summary = "Create a ticket with attachments (unified endpoint)",
        description = "Creates a new ticket in Freshservice with attachments using different methods",
        responses = {
            @ApiResponse(responseCode = "200", description = "Ticket created successfully", 
                content = @Content(mediaType = "application/json")),
            @ApiResponse(responseCode = "400", description = "Bad request"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
        }
    )
    public ResponseEntity<Object> createTicketWithAttachments(
            @Parameter(
                description = "Files to upload as attachments",
                content = @Content(mediaType = "multipart/form-data")
            )
            @RequestPart(value = "attachments", required = false) List<MultipartFile> attachments,
            @Parameter(
                description = "Method to use for creating ticket with attachments (two-step, single-call, example)",
                example = "single-call",
                required = false
            )
            @RequestParam(value = "method", defaultValue = "single-call") String method) {
        
        logger.info("Creating ticket with {} attachments using method: {}", 
                    attachments != null ? attachments.size() : 0, method);
        
        // Check if attachments list is empty
        if (attachments == null || attachments.isEmpty()) {
            logger.warn("No attachments provided");
            return ResponseEntity.badRequest().body("No attachments provided");
        }
        
        try {
            ResponseEntity<Object> response = null;
            
            switch (method.toLowerCase()) {
                case "two-step":
                    // Step 1: Create a ticket without attachments
                    FreshserviceTicketResponse ticketResponse = freshserviceTicketExample.createExampleTicket();
                    
                    if (ticketResponse == null || ticketResponse.getId() == null) {
                        logger.error("Failed to create initial ticket");
                        return ResponseEntity.badRequest().body("Failed to create initial ticket");
                    }
                    
                    logger.info("Successfully created ticket with ID: {}", ticketResponse.getId());
                    
                    // Step 2: Add attachments to the created ticket
                    ResponseEntity<Object> twoStepAttachmentResponse = 
                        freshserviceTicketService.addAttachmentsToTicket(ticketResponse.getId(), attachments);
                    
                    if (twoStepAttachmentResponse.getStatusCode().is2xxSuccessful()) {
                        logger.info("Successfully added attachments to ticket ID: {}", ticketResponse.getId());
                        
                        // Return a more detailed response with the ticket URL for easy access
                        String domain = freshserviceBaseUrl.replaceAll("(?i)/api/v2/?$", "");
                        String ticketUrl = domain + "/helpdesk/tickets/" + ticketResponse.getId();
                        response = ResponseEntity.ok()
                            .body("Successfully created ticket with ID: " + ticketResponse.getId() + 
                                  " and added " + attachments.size() + " attachments using two-step method. " +
                                  "View ticket at: " + ticketUrl);
                    } else {
                        logger.error("Failed to add attachments to ticket: {}", twoStepAttachmentResponse.getBody());
                        response = ResponseEntity.ok()
                            .body("Created ticket with ID: " + ticketResponse.getId() + 
                                  " but failed to add attachments: " + twoStepAttachmentResponse.getBody());
                    }
                    break;
                    
                case "example":
                    // Call the example service to create a ticket with attachments in a single call
                    FreshserviceTicketResponse exampleResponse = 
                        freshserviceTicketExample.createExampleTicketWithAttachmentsInSingleCall(attachments);
                    
                    if (exampleResponse != null && exampleResponse.getId() != null) {
                        logger.info("Successfully created ticket with ID: {} and attachments using example service", 
                                   exampleResponse.getId());
                        
                        // Return a more detailed response with the ticket URL for easy access
                        String domain = freshserviceBaseUrl.replaceAll("(?i)/api/v2/?$", "");
                        String ticketUrl = domain + "/helpdesk/tickets/" + exampleResponse.getId();
                        response = ResponseEntity.ok()
                            .body("Successfully created ticket with ID: " + exampleResponse.getId() + 
                                  " and " + attachments.size() + " attachments using example service. " +
                                  "View ticket at: " + ticketUrl);
                    } else {
                        logger.error("Failed to create ticket with attachments using example service");
                        response = ResponseEntity.badRequest()
                            .body("Failed to create ticket with attachments using example service");
                    }
                    break;
                    
                case "single-call":
                default:
                    // Create a completely clean ticket request with no custom fields
                    // Use a direct JSON string to avoid any serialization issues
                    String ticketJson = "{\n" +
                        "  \"name\": \"Test User\",\n" +
                        "  \"email\": \"<EMAIL>\",\n" +
                        "  \"subject\": \"Test Ticket Request\",\n" +
                        "  \"description\": \"This is a test ticket created for testing purposes.\",\n" +
                        "  \"status\": 2,\n" +
                        "  \"priority\": 1,\n" +
                        "  \"source\": 2,\n" +
                        "  \"category\": \"Applications\",\n" +
                        "  \"sub_category\": \"Troubleshooting\",\n" +
                        "  \"type\": \"Incident\",\n" +
                        "  \"impact\": 1,\n" +
                        "  \"urgency\": 1,\n" +
                        "  \"custom_fields\": {\n" +
                        "    \"lf_what_solution_is_having_an_issue\": ***********\n" +
                        "  }\n" +
                        "}";
                    
                    // Create the ticket request object from the JSON
                    FreshserviceTicketRequest ticketRequest = null;
                    try {
                        ObjectMapper objectMapper = new ObjectMapper();
                        ticketRequest = objectMapper.readValue(ticketJson, FreshserviceTicketRequest.class);
                    } catch (Exception e) {
                        logger.error("Error parsing ticket JSON: {}", e.getMessage(), e);
                        return ResponseEntity.internalServerError().body(
                            Collections.singletonMap("error", "Error parsing ticket JSON: " + e.getMessage()));
                    }
                    
                    // Step 1: Create a ticket without attachments
                    FreshserviceTicketResponse createdTicket = null;
                    try {
                        // Make a direct REST call to create the ticket
                        HttpHeaders headers = new HttpHeaders();
                        String auth = freshserviceApiKey + ":X";
                        byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes());
                        String authHeader = "Basic " + new String(encodedAuth);
                        headers.set("Authorization", authHeader);
                        headers.setContentType(MediaType.APPLICATION_JSON);
                        
                        HttpEntity<String> entity = new HttpEntity<>(ticketJson, headers);
                        
                        ResponseEntity<String> directApiResponse = restTemplate.exchange(
                            freshserviceBaseUrl + "/tickets",
                            HttpMethod.POST,
                            entity,
                            String.class
                        );
                        
                        if (directApiResponse.getStatusCode().is2xxSuccessful()) {
                            // Parse the response
                            ObjectMapper objectMapper = new ObjectMapper();
                            JsonNode rootNode = objectMapper.readTree(directApiResponse.getBody());
                            JsonNode ticketNode = rootNode.get("ticket");
                            
                            if (ticketNode != null && ticketNode.has("id")) {
                                Long ticketId = ticketNode.get("id").asLong();
                                logger.info("Successfully created ticket with ID: {}", ticketId);
                                
                                // Create a minimal ticket response object
                                createdTicket = new FreshserviceTicketResponse();
                                createdTicket.setId(ticketId);
                                createdTicket.setSubject(ticketRequest.subject());
                            } else {
                                logger.error("Failed to parse ticket response: {}", directApiResponse.getBody());
                                return ResponseEntity.badRequest().body(
                                    Collections.singletonMap("error", "Failed to parse ticket response"));
                            }
                        } else {
                            logger.error("Failed to create initial ticket: {}", directApiResponse.getBody());
                            return ResponseEntity.badRequest().body(
                                Collections.singletonMap("error", "Failed to create initial ticket: " + directApiResponse.getBody()));
                        }
                    } catch (Exception e) {
                        logger.error("Error creating ticket: {}", e.getMessage(), e);
                        return ResponseEntity.internalServerError().body(
                            Collections.singletonMap("error", "Error creating ticket: " + e.getMessage()));
                    }
                    
                    // Step 2: Add attachments to the created ticket
                    if (createdTicket != null && attachments != null && !attachments.isEmpty()) {
                        try {
                            ResponseEntity<Object> attachmentResponse = 
                                freshserviceTicketService.addAttachmentsToTicket(createdTicket.getId(), attachments);
                            
                            if (attachmentResponse.getStatusCode().is2xxSuccessful()) {
                                logger.info("Successfully added attachments to ticket ID: {}", createdTicket.getId());
                                
                                // Return a more detailed response with the ticket URL for easy access
                                String domain = freshserviceBaseUrl.replaceAll("(?i)/api/v2/?$", "");
                                String ticketUrl = domain + "/helpdesk/tickets/" + createdTicket.getId();
                                
                                Map<String, Object> result = new HashMap<>();
                                result.put("status", "success");
                                result.put("message", "Successfully created ticket with ID: " + createdTicket.getId() + 
                                          " and added " + attachments.size() + " attachments");
                                result.put("ticketId", createdTicket.getId());
                                result.put("ticketUrl", ticketUrl);
                                
                                // Include attachment information if available
                                if (attachmentResponse.getBody() instanceof FreshserviceAttachmentResponseWrapper) {
                                    result.put("attachments", attachmentResponse.getBody());
                                } else {
                                    // Include attachment names in the response
                                    List<String> attachmentNames = attachments.stream()
                                        .map(MultipartFile::getOriginalFilename)
                                        .collect(Collectors.toList());
                                    result.put("attachmentNames", attachmentNames);
                                }
                                
                                response = ResponseEntity.ok(result);
                            } else {
                                logger.error("Created ticket but failed to add attachments: {}", attachmentResponse.getBody());
                                
                                Map<String, Object> result = new HashMap<>();
                                result.put("status", "partial_success");
                                result.put("message", "Created ticket with ID: " + createdTicket.getId() + 
                                          " but failed to add attachments");
                                result.put("ticketId", createdTicket.getId());
                                result.put("error", attachmentResponse.getBody());
                                
                                response = ResponseEntity.ok(result);
                            }
                        } catch (Exception e) {
                            logger.error("Created ticket but error adding attachments: {}", e.getMessage(), e);
                            
                            Map<String, Object> result = new HashMap<>();
                            result.put("status", "partial_success");
                            result.put("message", "Created ticket with ID: " + createdTicket.getId() + 
                                      " but error adding attachments: " + e.getMessage());
                            result.put("ticketId", createdTicket.getId());
                            
                            response = ResponseEntity.ok(result);
                        }
                    } else if (createdTicket != null) {
                        // No attachments to add, just return the ticket info
                        String domain = freshserviceBaseUrl.replaceAll("(?i)/api/v2/?$", "");
                        String ticketUrl = domain + "/helpdesk/tickets/" + createdTicket.getId();
                        
                        Map<String, Object> result = new HashMap<>();
                        result.put("status", "success");
                        result.put("message", "Successfully created ticket with ID: " + createdTicket.getId());
                        result.put("ticketId", createdTicket.getId());
                        result.put("ticketUrl", ticketUrl);
                        result.put("ticket", createdTicket);
                        
                        response = ResponseEntity.ok(result);
                    }
                    break;
            }
            
            return response;
        } catch (Exception e) {
            logger.error("Error creating ticket with attachments: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body("Error creating ticket with attachments: " + e.getMessage());
        }
    }    
    /**
     * Test endpoint to add attachments to an existing ticket
     * 
     * @param ticketId The ID of the ticket to add attachments to
     * @param attachments List of file attachments
     * @return ResponseEntity with the attachment response or error message
     */
    @PostMapping(value = "/add-attachments-to-ticket", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(
        summary = "Add attachments to an existing ticket",
        description = "Adds attachments to an existing ticket in Freshservice",
        responses = {
            @ApiResponse(responseCode = "200", description = "Attachments added successfully", 
                content = @Content(mediaType = "application/json")),
            @ApiResponse(responseCode = "400", description = "Bad request"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
        }
    )
    public ResponseEntity<Object> testAddAttachmentsToTicket(
            @Parameter(
                description = "ID of the ticket to add attachments to",
                example = "123456",
                required = true
            )
            @RequestParam("ticketId") Long ticketId,
            @Parameter(
                description = "Files to upload as attachments",
                content = @Content(mediaType = "multipart/form-data")
            )
            @RequestPart("attachments") List<MultipartFile> attachments) {
        logger.info("Testing adding {} attachments to ticket ID: {}", attachments.size(), ticketId);
        try {
            // Check if attachments list is empty
            if (attachments == null || attachments.isEmpty()) {
                logger.warn("No attachments provided for ticket ID: {}", ticketId);
                return ResponseEntity.badRequest().body(
                    Collections.singletonMap("error", "No attachments provided"));
            }
            
            // Log attachment details for debugging
            for (MultipartFile file : attachments) {
                logger.debug("Attachment: name={}, size={}, contentType={}", 
                    file.getOriginalFilename(), file.getSize(), file.getContentType());
            }
            
            ResponseEntity<Object> response = freshserviceTicketService.addAttachmentsToTicket(ticketId, attachments);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                logger.info("Successfully added attachments to ticket ID: {}", ticketId);
                
                // Create a proper JSON response
                Map<String, Object> result = new HashMap<>();
                result.put("status", "success");
                result.put("message", "Successfully added " + attachments.size() + " attachments to ticket ID: " + ticketId);
                
                // If the original response has useful data, include it
                if (response.getBody() instanceof FreshserviceAttachmentResponseWrapper) {
                    result.put("attachments", response.getBody());
                } else {
                    // Include attachment names in the response
                    List<String> attachmentNames = attachments.stream()
                        .map(MultipartFile::getOriginalFilename)
                        .collect(Collectors.toList());
                    result.put("attachmentNames", attachmentNames);
                }
                
                return ResponseEntity.ok(result);
            } else {
                logger.error("Failed to add attachments to ticket: {}", response.getBody());
                
                // Create a proper error response
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("status", "error");
                errorResponse.put("message", "Failed to add attachments to ticket");
                errorResponse.put("details", response.getBody());
                
                return ResponseEntity.status(response.getStatusCode()).body(errorResponse);
            }
        } catch (Exception e) {
            logger.error("Error adding attachments to ticket: {}", e.getMessage(), e);
            
            // Create a proper error response
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", "error");
            errorResponse.put("message", "Error adding attachments to ticket: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * Create a ticket with a custom JSON request
     * 
     * @param request The ticket request object
     * @return ResponseEntity with the created ticket or error message
     */
    @PostMapping("/tickets")
    @Operation(
        summary = "Create a ticket with a custom JSON request",
        description = "Creates a new ticket in Freshservice with a custom JSON request",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = FreshserviceTicketRequest.class),
                examples = {
                    @ExampleObject(
                        name = "Sample Ticket Request",
                        summary = "Sample ticket request with all fields",
                        value = "{\n" +
                               "  \"name\": \"Test User\",\n" +
                               "  \"email\": \"<EMAIL>\",\n" +
                               "  \"subject\": \"Test Ticket Request\",\n" +
                               "  \"description\": \"This is a test ticket created for testing purposes.\",\n" +
                               "  \"status\": 2,\n" +
                               "  \"priority\": 1,\n" +
                               "  \"source\": 2,\n" +
                               "  \"department_id\": null,\n" +
                               "  \"category\": \"Applications\",\n" +
                               "  \"sub_category\": \"Troubleshooting\",\n" +
                               "  \"type\": \"Incident\",\n" +
                               "  \"impact\": 1,\n" +
                               "  \"urgency\": 1,\n" +
                               "  \"custom_fields\": {\n" +
                               "    \"major_incident_type\": null,\n" +
                               "    \"business_impact\": null,\n" +
                               "    \"impacted_locations\": null,\n" +
                               "    \"no_of_customers_impacted\": null,\n" +
                               "    \"msf_closing_code\": [],\n" +
                               "    \"lf_what_solution_is_having_an_issue\": ***********\n" +
                               "  }\n" +
                               "}"
                    )
                }
            )
        ),
        responses = {
            @ApiResponse(responseCode = "200", description = "Ticket created successfully", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = FreshserviceTicketResponse.class))),
            @ApiResponse(responseCode = "400", description = "Bad request"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
        }
    )
    public ResponseEntity<Object> createTicketWithCustomRequest(@RequestBody FreshserviceTicketRequest request) {
        logger.info("Creating ticket with custom request: {}", request);
        try {
            ResponseEntity<Object> response = freshserviceTicketService.createTicket(request);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                logger.info("Successfully created ticket with custom request");
                return response;
            } else {
                logger.error("Failed to create ticket with custom request: {}", response.getBody());
                return response;
            }
        } catch (Exception e) {
            logger.error("Error creating ticket with custom request: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body("Error creating ticket with custom request: " + e.getMessage());
        }
    }
    
    /**
     * Get ticket details by ID
     * 
     * @param ticketId The ID of the ticket to retrieve
     * @return ResponseEntity with the ticket details or error message
     */
    @GetMapping("/tickets/{ticketId}")
    public ResponseEntity<Object> getTicketById(@PathVariable("ticketId") Long ticketId) {
        logger.info("Retrieving ticket details for ID: {}", ticketId);
        try {
            ResponseEntity<Object> response = freshserviceTicketService.getTicketById(ticketId);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                logger.info("Successfully retrieved ticket details for ID: {}", ticketId);
                
                // Add ticket URL to the response for easy access
                String domain = freshserviceBaseUrl.replaceAll("(?i)/api/v2/?$", "");
                String ticketUrl = domain + "/helpdesk/tickets/" + ticketId;
                
                // If the response body is a FreshserviceTicketResponse, we can add more context
                if (response.getBody() instanceof FreshserviceTicketResponse) {
                    FreshserviceTicketResponse ticketResponse = (FreshserviceTicketResponse) response.getBody();
                    logger.info("Ticket subject: {}", ticketResponse.getSubject());
                    
                    // Return the ticket details
                    return ResponseEntity.ok()
                        .body(ticketResponse);
                } else {
                    // Just return whatever the service returned
                    return response;
                }
            } else {
                logger.error("Failed to retrieve ticket details: {}", response.getBody());
                return response;
            }
        } catch (Exception e) {
            logger.error("Error retrieving ticket details: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body("Error retrieving ticket details: " + e.getMessage());
        }
    }
    
    /**
     * Get raw ticket details by ID
     * 
     * @param id The ID of the ticket to retrieve
     * @return ResponseEntity with the raw ticket details or error message
     */
    @GetMapping("/tickets/{id}/raw")
    @Operation(
        summary = "Get raw ticket details by ID",
        description = "Retrieves the raw ticket details from Freshservice API including attachment information",
        responses = {
            @ApiResponse(responseCode = "200", description = "Ticket details retrieved successfully", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = String.class))),
            @ApiResponse(responseCode = "404", description = "Ticket not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
        }
    )
    public ResponseEntity<String> getTicketRaw(@PathVariable Long id) {
        logger.info("Getting raw ticket details for ID: {}", id);
        
        try {
            // Get the ticket details
            String apiUrl = freshserviceBaseUrl + "/tickets/" + id;
            
            HttpHeaders headers = new HttpHeaders();
            String auth = freshserviceApiKey + ":X";
            byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes());
            String authHeader = "Basic " + new String(encodedAuth);
            headers.set("Authorization", authHeader);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            ResponseEntity<String> response = restTemplate.exchange(
                apiUrl,
                HttpMethod.GET,
                entity,
                String.class
            );
            
            // Check if the ticket exists
            if (!response.getStatusCode().is2xxSuccessful() || response.getBody() == null) {
                logger.error("Failed to retrieve ticket. Status: {}, Response: {}", 
                    response.getStatusCode(), response.getBody());
                return ResponseEntity.status(response.getStatusCode())
                    .body("Failed to retrieve ticket: " + response.getBody());
            }
            
            // Get the attachments
            ResponseEntity<Object> attachmentsResponse = freshserviceTicketService.getTicketAttachments(id);
            
            // Combine ticket details and attachments
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode ticketNode = objectMapper.readTree(response.getBody());
                
                if (ticketNode == null || !ticketNode.has("ticket")) {
                    logger.error("Invalid ticket response format: {}", response.getBody());
                    return ResponseEntity.ok(response.getBody());
                }
                
                // Create a combined response with ticket details and attachments
                ObjectNode combinedNode = objectMapper.createObjectNode();
                combinedNode.set("ticket", ticketNode.get("ticket"));
                
                if (attachmentsResponse.getStatusCode().is2xxSuccessful() && 
                    attachmentsResponse.getBody() instanceof FreshserviceAttachmentResponseWrapper) {
                    
                    FreshserviceAttachmentResponseWrapper wrapper = 
                        (FreshserviceAttachmentResponseWrapper) attachmentsResponse.getBody();
                    
                    if (wrapper != null && wrapper.getAttachments() != null && !wrapper.getAttachments().isEmpty()) {
                        logger.info("Found {} attachments for ticket ID: {}", 
                            wrapper.getAttachments().size(), id);
                        
                        // Add attachments to the response
                        ArrayNode attachmentsArray = objectMapper.createArrayNode();
                        for (FreshserviceAttachmentResponse attachment : wrapper.getAttachments()) {
                            ObjectNode attachmentNode = objectMapper.createObjectNode();
                            attachmentNode.put("id", attachment.getId());
                            attachmentNode.put("name", attachment.getName());
                            attachmentNode.put("content_type", attachment.getContentType());
                            attachmentNode.put("size", attachment.getSize());
                            attachmentNode.put("attachment_url", attachment.getAttachmentUrl());
                            attachmentNode.put("created_at", attachment.getCreatedAt());
                            attachmentNode.put("updated_at", attachment.getUpdatedAt());
                            attachmentsArray.add(attachmentNode);
                        }
                        
                        // Add attachments array to the ticket node
                        ((ObjectNode) combinedNode.get("ticket")).set("conversation_attachments", attachmentsArray);
                    } else {
                        logger.info("No attachments found for ticket ID: {}", id);
                        ((ObjectNode) combinedNode.get("ticket")).set("conversation_attachments", objectMapper.createArrayNode());
                    }
                } else {
                    logger.warn("Failed to retrieve attachments for ticket ID: {}", id);
                    ((ObjectNode) combinedNode.get("ticket")).set("conversation_attachments", objectMapper.createArrayNode());
                }
                
                return ResponseEntity.ok(objectMapper.writeValueAsString(combinedNode));
            } catch (Exception e) {
                logger.error("Error combining ticket details and attachments: {}", e.getMessage(), e);
                // If there's an error, just return the original response
                return ResponseEntity.ok(response.getBody());
            }
        } catch (Exception e) {
            logger.error("Error getting raw ticket details: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error getting raw ticket details: " + e.getMessage());
        }
    }
    
    /**
     * Get attachments for a ticket by ID
     * 
     * @param id The ID of the ticket to retrieve attachments for
     * @return ResponseEntity with the attachments or error message
     */
    @GetMapping("/tickets/{id}/attachments")
    @Operation(
        summary = "Get attachments for a ticket",
        description = "Retrieves all attachments associated with a ticket from Freshservice API",
        responses = {
            @ApiResponse(responseCode = "200", description = "Attachments retrieved successfully", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = FreshserviceAttachmentResponseWrapper.class))),
            @ApiResponse(responseCode = "404", description = "Ticket not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
        }
    )
    public ResponseEntity<Object> getTicketAttachments(@PathVariable Long id) {
        logger.info("Getting attachments for ticket ID: {}", id);
        
        try {
            ResponseEntity<Object> response = freshserviceTicketService.getTicketAttachments(id);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                logger.info("Successfully retrieved attachments for ticket ID: {}", id);
                return response;
            } else {
                logger.error("Failed to retrieve attachments: {}", response.getBody());
                return response;
            }
        } catch (Exception e) {
            logger.error("Error getting ticket attachments: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error getting ticket attachments: " + e.getMessage());
        }
    }
    
    /**
     * Download an attachment by its ID
     * 
     * @param attachmentId The ID of the attachment to download
     * @return ResponseEntity with the attachment content or error message
     */
    @GetMapping("/attachments/{attachmentId}")
    @Operation(
        summary = "Download an attachment",
        description = "Downloads an attachment from Freshservice by its ID",
        responses = {
            @ApiResponse(responseCode = "200", description = "Attachment downloaded successfully", 
                content = @Content(mediaType = "application/octet-stream")),
            @ApiResponse(responseCode = "404", description = "Attachment not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
        }
    )
    public ResponseEntity<Object> downloadAttachment(@PathVariable Long attachmentId) {
        logger.info("Downloading attachment with ID: {}", attachmentId);
        
        try {
            ResponseEntity<Object> response = freshserviceTicketService.downloadAttachment(attachmentId);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                logger.info("Successfully downloaded attachment with ID: {}", attachmentId);
                return response;
            } else {
                logger.error("Failed to download attachment: {}", response.getBody());
                return response;
            }
        } catch (Exception e) {
            logger.error("Error downloading attachment: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error downloading attachment: " + e.getMessage());
        }
    }
    
    /**
     * Get conversations for a ticket by ID
     * 
     * @param id The ID of the ticket to retrieve conversations for
     * @return ResponseEntity with the conversations or error message
     */
    @GetMapping("/tickets/{id}/conversations")
    @Operation(
        summary = "Get conversations for a ticket",
        description = "Retrieves all conversations for a ticket from Freshservice API",
        responses = {
            @ApiResponse(responseCode = "200", description = "Conversations retrieved successfully", 
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = String.class))),
            @ApiResponse(responseCode = "404", description = "Ticket not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
        }
    )
    public ResponseEntity<String> getTicketConversations(@PathVariable Long id) {
        logger.info("Getting conversations for ticket ID: {}", id);
        
        try {
            return freshserviceTicketService.getTicketConversations(id);
        } catch (Exception e) {
            logger.error("Error getting ticket conversations: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error getting ticket conversations: " + e.getMessage());
        }
    }
    
    /**
     * Test endpoint to create a ticket with direct JSON
     * This bypasses all DTO serialization to ensure no unwanted fields are included
     * 
     * @return ResponseEntity with the created ticket or error message
     */
    @PostMapping("/create-ticket-direct-json")
    @Operation(
        summary = "Create a ticket with direct JSON",
        description = "Creates a new ticket in Freshservice using direct JSON to avoid serialization issues",
        responses = {
            @ApiResponse(responseCode = "200", description = "Ticket created successfully", 
                content = @Content(mediaType = "application/json")),
            @ApiResponse(responseCode = "400", description = "Bad request"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
        }
    )
    public ResponseEntity<Object> createTicketDirectJson() {
        logger.info("Creating ticket with direct JSON");
        
        // Create a completely clean ticket request with no custom fields
        String ticketJson = "{\n" +
            "  \"name\": \"Test User\",\n" +
            "  \"email\": \"<EMAIL>\",\n" +
            "  \"subject\": \"Test Ticket Request\",\n" +
            "  \"description\": \"This is a test ticket created for testing purposes.\",\n" +
            "  \"status\": 2,\n" +
            "  \"priority\": 1,\n" +
            "  \"source\": 2,\n" +
            "  \"category\": \"Applications\",\n" +
            "  \"sub_category\": \"Troubleshooting\",\n" +
            "  \"type\": \"Incident\",\n" +
            "  \"impact\": 1,\n" +
            "  \"urgency\": 1\n" +
            "}";
        
        try {
            // Make a direct REST call to create the ticket
            HttpHeaders headers = new HttpHeaders();
            String auth = freshserviceApiKey + ":X";
            byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes());
            String authHeader = "Basic " + new String(encodedAuth);
            headers.set("Authorization", authHeader);
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<String> entity = new HttpEntity<>(ticketJson, headers);
            
            ResponseEntity<String> apiResponse = restTemplate.exchange(
                freshserviceBaseUrl + "/tickets",
                HttpMethod.POST,
                entity,
                String.class
            );
            
            if (apiResponse.getStatusCode().is2xxSuccessful()) {
                logger.info("Successfully created ticket with direct JSON");
                return ResponseEntity.ok(apiResponse.getBody());
            } else {
                logger.error("Failed to create ticket with direct JSON: {}", apiResponse.getBody());
                return ResponseEntity.badRequest().body(
                    Collections.singletonMap("error", "Failed to create ticket: " + apiResponse.getBody()));
            }
        } catch (Exception e) {
            logger.error("Error creating ticket with direct JSON: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(
                Collections.singletonMap("error", "Error creating ticket: " + e.getMessage()));
        }
    }
}
