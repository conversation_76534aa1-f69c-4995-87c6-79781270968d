package com.itcinfotech.windchill.complaints.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public record ContainerValue(

        @JsonProperty("@odata.type")
        String oDataType,

        @JsonProperty("ID")
        String id,

        @JsonProperty("Name")
        String name

) {}

