package com.itcinfotech.windchill.complaints.dto;

import com.fasterxml.jackson.annotation.JsonProperty;


public record ContentInfoStage1(
        @JsonProperty("StreamId") String streamId,
        @JsonProperty("FileSize") String fileSize,
        @JsonProperty("EncodedInfo") String encodedInfo,
        @JsonProperty("FileName") String fileName,
        @JsonProperty("MimeType") String mimeType,
        @JsonProperty("PrimaryContent") Boolean primaryContent
) {}

