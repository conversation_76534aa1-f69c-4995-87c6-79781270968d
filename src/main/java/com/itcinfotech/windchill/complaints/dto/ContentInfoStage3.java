package com.itcinfotech.windchill.complaints.dto;

import com.fasterxml.jackson.annotation.JsonProperty;


public record ContentInfoStage3(
        @JsonProperty("StreamId") Integer streamId,
        @JsonProperty("FileSize") Integer fileSize,
        @JsonProperty("EncodedInfo") String encodedInfo,
        @JsonProperty("FileName") String fileName,
        @JsonProperty("MimeType") String mimeType,
        @JsonProperty("PrimaryContent") Boolean primaryContent
) {}

