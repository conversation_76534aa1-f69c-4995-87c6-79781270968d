package com.itcinfotech.windchill.complaints.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public record ContentValue(

        @JsonProperty("ID")
        String id,

        @JsonProperty("VaultId")
        int vaultId,

        @JsonProperty("MasterUrl")
        String masterUrl,

        @JsonProperty("ReplicaUrl")
        String replicaUrl,

        @JsonProperty("FileNames")
        List<Integer> fileNames,

        @JsonProperty("FolderId")
        int folderId,

        @JsonProperty("StreamIds")
        List<Integer> streamIds

) {}

