package com.itcinfotech.windchill.complaints.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public record ContentValueStage3(

        @JsonProperty("@odata.mediaContentType") String contentType,
        @JsonProperty("Category") String category,
        @JsonProperty("Comments") String comments,
        @JsonProperty("CreatedBy") String createdBy,
        @JsonProperty("CreatedOn") String createdOn,
        @JsonProperty("Description") String description,
        @JsonProperty("FormatIcon") FormatIcon formatIcon,
        @JsonProperty("ID") String id,
        @JsonProperty("LastModified") String lastModified,
        @JsonProperty("ModifiedBy") String modifiedBy,
        @JsonProperty("Content") Content content,
        @JsonProperty("FileName") String fileName,
        @JsonProperty("FileSize") Integer fileSize,
        @JsonProperty("Format") String format,
        @JsonProperty("MimeType") String mimeType

) {}

