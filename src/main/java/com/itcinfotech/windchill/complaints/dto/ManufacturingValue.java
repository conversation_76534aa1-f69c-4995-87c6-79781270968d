package com.itcinfotech.windchill.complaints.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public record ManufacturingValue(

        @JsonProperty("@odata.type")
        String oDataType,

        @JsonProperty("ID")
        String placeId,

        @JsonProperty("Name")
        String name
) {}

