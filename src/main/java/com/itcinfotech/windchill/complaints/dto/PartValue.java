package com.itcinfotech.windchill.complaints.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public record PartValue(
        @JsonProperty("@odata.type")
        String oDataType,

        @JsonProperty("ID")
        String partId,

        @JsonProperty("Number")
        String number


) {}

