package com.itcinfotech.windchill.complaints.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public record PeopleValue(

        @JsonProperty("@odata.type")
        String oDataType,

        @JsonProperty("ID")
        String id,

        @JsonProperty("Name")
        String name,

        @JsonProperty("ComPtcmscloudUniquenessEmailAddress")
        String uniquenessEmailAddress
) {}

