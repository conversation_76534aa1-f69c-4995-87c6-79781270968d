package com.itcinfotech.windchill.complaints.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public record PrimaryRelatedPersonOrLocation(

        @JsonProperty("@odata.type")
        String odataType, // Constant Value: #PTC.QMS.PatientContact

        @JsonProperty("<EMAIL>")
        String contactOdataBind, // Ex: "PeopleOrPlaces('OR:com.ptc.qualitymanagement.masterdata.entity.MDEntity:361585')"

        @JsonProperty("Age")
        Integer age,

        @JsonProperty("AgeUnits")
        AgeUnits ageUnits,

        @JsonProperty("DateOfBirth")
        String dateOfBirth,

        @JsonProperty("DateOfBirthApproximate")
        Boolean dateOfBirthApproximate,

        @JsonProperty("Gender")
        Gender gender,

        @JsonProperty("Weight")
        Integer weight,

        @JsonProperty("WeightUnits")
        WeightUnits weightUnits

) {}
