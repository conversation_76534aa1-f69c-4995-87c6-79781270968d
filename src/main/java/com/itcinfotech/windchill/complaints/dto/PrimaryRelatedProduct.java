package com.itcinfotech.windchill.complaints.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record PrimaryRelatedProduct(

        @JsonProperty("<EMAIL>")
        String oDataSubject, // Ex: Parts('OR:wt.part.WTPart:278290')

        @JsonProperty("ExpectedReturn")
        Boolean expectedReturn,

        @JsonProperty("Primary")
        Boolean primary,

        @JsonProperty("Quantity")
        Integer quantity,

        @JsonProperty("SerialLotNumber")
        String serialLotNumber,

        @JsonProperty("UnitOfMeasure")
        UnitOfMeasure unitOfMeasure,

        @JsonProperty("<EMAIL>")
        String oDataManufacturingLocation // Ex: Places('oid')

) {}
