package com.itcinfotech.windchill.complaints.dto.freshservice;

import com.fasterxml.jackson.annotation.JsonProperty;

public class FreshserviceAttachmentResponse {
    
    @JsonProperty("id")
    private Long id;
    
    @JsonProperty("content_type")
    private String contentType;
    
    @JsonProperty("size")
    private Long size;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("attachment_url")
    private String attachmentUrl;
    
    @JsonProperty("created_at")
    private String createdAt;
    
    @JsonProperty("updated_at")
    private String updatedAt;
    
    @JsonProperty("canonical_url")
    private String canonicalUrl;
    
    @JsonProperty("has_access")
    private Boolean hasAccess;
    
    // Constructors
    public FreshserviceAttachmentResponse() {
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public Long getSize() {
        return size;
    }
    
    public void setSize(Long size) {
        this.size = size;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getAttachmentUrl() {
        return attachmentUrl;
    }
    
    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }
    
    public String getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }
    
    public String getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getCanonicalUrl() {
        return canonicalUrl;
    }
    
    public void setCanonicalUrl(String canonicalUrl) {
        this.canonicalUrl = canonicalUrl;
    }
    
    public Boolean getHasAccess() {
        return hasAccess;
    }
    
    public void setHasAccess(Boolean hasAccess) {
        this.hasAccess = hasAccess;
    }
}
