package com.itcinfotech.windchill.complaints.dto.freshservice;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class FreshserviceAttachmentResponseWrapper {
    
    @JsonProperty("attachments")
    private List<FreshserviceAttachmentResponse> attachments;
    
    // Constructors
    public FreshserviceAttachmentResponseWrapper() {
    }
    
    public FreshserviceAttachmentResponseWrapper(List<FreshserviceAttachmentResponse> attachments) {
        this.attachments = attachments;
    }
    
    // Getters and Setters
    public List<FreshserviceAttachmentResponse> getAttachments() {
        return attachments;
    }
    
    public void setAttachments(List<FreshserviceAttachmentResponse> attachments) {
        this.attachments = attachments;
    }
}
