package com.itcinfotech.windchill.complaints.dto.freshservice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FreshserviceResponse<T> {
    
    @JsonProperty("ticket")
    private T ticket;
    
    // Constructors
    public FreshserviceResponse() {
    }
    
    public FreshserviceResponse(T ticket) {
        this.ticket = ticket;
    }
    
    // Getters and Setters
    public T getTicket() {
        return ticket;
    }
    
    public void setTicket(T ticket) {
        this.ticket = ticket;
    }
}
