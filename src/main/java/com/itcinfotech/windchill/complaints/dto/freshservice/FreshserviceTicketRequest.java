package com.itcinfotech.windchill.complaints.dto.freshservice;


import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;


public record FreshserviceTicketRequest (
    
    @JsonProperty("name")
    String name,
    
    @JsonProperty("email")
    String email,
    
    @JsonProperty("subject")
    String subject,
    
    @JsonProperty("description")
    String description,
    
    @JsonProperty("status")
    Integer status,
    
    @JsonProperty("priority")
    Integer priority,
    
    @<PERSON>sonProperty("source")
    Integer source,
    
    @JsonProperty("department_id")
    Long departmentId,
    
    @JsonProperty("category")
    String category,
    
    @JsonProperty("sub_category")
    String subCategory,
    
    @JsonProperty("type")
    String type,
    
    @JsonProperty("impact")
    Integer impact,
    
    @JsonProperty("urgency")
    Integer urgency,
    
    @JsonProperty("custom_fields")
    Map<String, Object> customFields

)
{}
