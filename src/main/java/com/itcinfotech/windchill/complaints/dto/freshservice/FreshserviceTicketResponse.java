package com.itcinfotech.windchill.complaints.dto.freshservice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FreshserviceTicketResponse {
    
    @JsonProperty("id")
    private Long id;
    
    @JsonProperty("subject")
    private String subject;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("description_text")
    private String descriptionText;
    
    @JsonProperty("status")
    private Integer status;
    
    @JsonProperty("priority")
    private Integer priority;
    
    @JsonProperty("source")
    private Integer source;
    
    @JsonProperty("requester_id")
    private Long requesterId;
    
    @JsonProperty("responder_id")
    private Long responderId;
    
    @JsonProperty("department_id")
    private Long departmentId;
    
    @JsonProperty("category")
    private String category;
    
    @JsonProperty("sub_category")
    private String subCategory;
    
    @JsonProperty("item_category")
    private String itemCategory;
    
    @JsonProperty("created_at")
    private String createdAt;
    
    @JsonProperty("updated_at")
    private String updatedAt;
    
    @JsonProperty("due_by")
    private String dueBy;
    
    @JsonProperty("fr_due_by")
    private String frDueBy;
    
    @JsonProperty("is_escalated")
    private Boolean isEscalated;
    
    @JsonProperty("fr_escalated")
    private Boolean frEscalated;
    
    @JsonProperty("deleted")
    private Boolean deleted;
    
    @JsonProperty("spam")
    private Boolean spam;
    
    @JsonProperty("email_config_id")
    private Long emailConfigId;
    
    @JsonProperty("group_id")
    private Long groupId;
    
    @JsonProperty("workspace_id")
    private Long workspaceId;
    
    @JsonProperty("requested_for_id")
    private Long requestedForId;
    
    @JsonProperty("to_emails")
    private String toEmails;
    
    @JsonProperty("cc_emails")
    private List<String> ccEmails;
    
    @JsonProperty("bcc_emails")
    private List<String> bccEmails;
    
    @JsonProperty("fwd_emails")
    private List<String> fwdEmails;
    
    @JsonProperty("reply_cc_emails")
    private List<String> replyCcEmails;
    
    @JsonProperty("type")
    private String type;
    
    @JsonProperty("impact")
    private Integer impact;
    
    @JsonProperty("urgency")
    private Integer urgency;
    
    @JsonProperty("tasks_dependency_type")
    private Integer tasksDependencyType;
    
    @JsonProperty("sla_policy_id")
    private Long slaPolicyId;
    
    @JsonProperty("applied_business_hours")
    private Long appliedBusinessHours;
    
    @JsonProperty("created_within_business_hours")
    private Boolean createdWithinBusinessHours;
    
    @JsonProperty("resolution_notes")
    private String resolutionNotes;
    
    @JsonProperty("resolution_notes_html")
    private String resolutionNotesHtml;
    
    @JsonProperty("custom_fields")
    private Map<String, Object> customFields;
    
    @JsonProperty("attachments")
    private List<Object> attachments;
    
    // Constructors
    public FreshserviceTicketResponse() {
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getSubject() {
        return subject;
    }
    
    public void setSubject(String subject) {
        this.subject = subject;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getDescriptionText() {
        return descriptionText;
    }
    
    public void setDescriptionText(String descriptionText) {
        this.descriptionText = descriptionText;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Integer getPriority() {
        return priority;
    }
    
    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    
    public Integer getSource() {
        return source;
    }
    
    public void setSource(Integer source) {
        this.source = source;
    }
    
    public Long getRequesterId() {
        return requesterId;
    }
    
    public void setRequesterId(Long requesterId) {
        this.requesterId = requesterId;
    }
    
    public Long getResponderId() {
        return responderId;
    }
    
    public void setResponderId(Long responderId) {
        this.responderId = responderId;
    }
    
    public Long getDepartmentId() {
        return departmentId;
    }
    
    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public String getSubCategory() {
        return subCategory;
    }
    
    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }
    
    public String getItemCategory() {
        return itemCategory;
    }
    
    public void setItemCategory(String itemCategory) {
        this.itemCategory = itemCategory;
    }
    
    public String getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }
    
    public String getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getDueBy() {
        return dueBy;
    }
    
    public void setDueBy(String dueBy) {
        this.dueBy = dueBy;
    }
    
    public String getFrDueBy() {
        return frDueBy;
    }
    
    public void setFrDueBy(String frDueBy) {
        this.frDueBy = frDueBy;
    }
    
    public Boolean getIsEscalated() {
        return isEscalated;
    }
    
    public void setIsEscalated(Boolean isEscalated) {
        this.isEscalated = isEscalated;
    }
    
    public Boolean getFrEscalated() {
        return frEscalated;
    }
    
    public void setFrEscalated(Boolean frEscalated) {
        this.frEscalated = frEscalated;
    }
    
    public Boolean getDeleted() {
        return deleted;
    }
    
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
    
    public Boolean getSpam() {
        return spam;
    }
    
    public void setSpam(Boolean spam) {
        this.spam = spam;
    }
    
    public Long getEmailConfigId() {
        return emailConfigId;
    }
    
    public void setEmailConfigId(Long emailConfigId) {
        this.emailConfigId = emailConfigId;
    }
    
    public Long getGroupId() {
        return groupId;
    }
    
    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }
    
    public Long getWorkspaceId() {
        return workspaceId;
    }
    
    public void setWorkspaceId(Long workspaceId) {
        this.workspaceId = workspaceId;
    }
    
    public Long getRequestedForId() {
        return requestedForId;
    }
    
    public void setRequestedForId(Long requestedForId) {
        this.requestedForId = requestedForId;
    }
    
    public String getToEmails() {
        return toEmails;
    }
    
    public void setToEmails(String toEmails) {
        this.toEmails = toEmails;
    }
    
    public List<String> getCcEmails() {
        return ccEmails;
    }
    
    public void setCcEmails(List<String> ccEmails) {
        this.ccEmails = ccEmails;
    }
    
    public List<String> getBccEmails() {
        return bccEmails;
    }
    
    public void setBccEmails(List<String> bccEmails) {
        this.bccEmails = bccEmails;
    }
    
    public List<String> getFwdEmails() {
        return fwdEmails;
    }
    
    public void setFwdEmails(List<String> fwdEmails) {
        this.fwdEmails = fwdEmails;
    }
    
    public List<String> getReplyCcEmails() {
        return replyCcEmails;
    }
    
    public void setReplyCcEmails(List<String> replyCcEmails) {
        this.replyCcEmails = replyCcEmails;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public Integer getImpact() {
        return impact;
    }
    
    public void setImpact(Integer impact) {
        this.impact = impact;
    }
    
    public Integer getUrgency() {
        return urgency;
    }
    
    public void setUrgency(Integer urgency) {
        this.urgency = urgency;
    }
    
    public Integer getTasksDependencyType() {
        return tasksDependencyType;
    }
    
    public void setTasksDependencyType(Integer tasksDependencyType) {
        this.tasksDependencyType = tasksDependencyType;
    }
    
    public Long getSlaPolicyId() {
        return slaPolicyId;
    }
    
    public void setSlaPolicyId(Long slaPolicyId) {
        this.slaPolicyId = slaPolicyId;
    }
    
    public Long getAppliedBusinessHours() {
        return appliedBusinessHours;
    }
    
    public void setAppliedBusinessHours(Long appliedBusinessHours) {
        this.appliedBusinessHours = appliedBusinessHours;
    }
    
    public Boolean getCreatedWithinBusinessHours() {
        return createdWithinBusinessHours;
    }
    
    public void setCreatedWithinBusinessHours(Boolean createdWithinBusinessHours) {
        this.createdWithinBusinessHours = createdWithinBusinessHours;
    }
    
    public String getResolutionNotes() {
        return resolutionNotes;
    }
    
    public void setResolutionNotes(String resolutionNotes) {
        this.resolutionNotes = resolutionNotes;
    }
    
    public String getResolutionNotesHtml() {
        return resolutionNotesHtml;
    }
    
    public void setResolutionNotesHtml(String resolutionNotesHtml) {
        this.resolutionNotesHtml = resolutionNotesHtml;
    }
    
    public Map<String, Object> getCustomFields() {
        return customFields;
    }
    
    public void setCustomFields(Map<String, Object> customFields) {
        this.customFields = customFields;
    }
    
    public List<Object> getAttachments() {
        return attachments;
    }
    
    public void setAttachments(List<Object> attachments) {
        this.attachments = attachments;
    }
}
