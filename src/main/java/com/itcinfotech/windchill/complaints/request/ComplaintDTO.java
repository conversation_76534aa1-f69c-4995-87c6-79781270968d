package com.itcinfotech.windchill.complaints.request;

import com.fasterxml.jackson.annotation.*;
import com.itcinfotech.windchill.complaints.dto.*;

import java.time.ZonedDateTime;


public class ComplaintDTO{
        @JsonProperty("Number")
        String number;
        @JsonProperty("AdditionalInformation")
        String additionalInformation;
        @JsonProperty("Name")
        String name;
        @JsonProperty("HowReported")
        HowReported howReported;
        @JsonProperty("DateApproximate")
        boolean dateApproximate;
        @JsonProperty("EventLocation")
        EventLocation eventLocation;
        @JsonProperty("Circumstance")
        Circumstance circumstance;
        @JsonProperty("PrimaryCode")
        String primaryCode;
        @JsonProperty("CountryOfOrigin")
        CountryOfOrigin countryOfOrigin;
        @JsonProperty("CountryOfEvent")
        CountryOfEvent countryOfEvent;
        @JsonProperty("Date")
        ZonedDateTime date;
        @JsonProperty("DevicifyKey")
        String devicifyKey;
        @JsonProperty("Summary")
        String summary;
        @JsonProperty("@odata.type")
        String odataType;
        @JsonProperty("<EMAIL>")
        String[] additionalRelatedProducts;
        @JsonProperty("<EMAIL>")
        String primaryRelatedProduct;
        @JsonProperty("<EMAIL>")
        String entryLocation;
        @JsonProperty("<EMAIL>")
        String[] smallThumbnails;
        @JsonProperty("<EMAIL>")
        String primaryRelatedPersonOrLocation;
        @JsonProperty("<EMAIL>")
        String[] thumbnails;
        @JsonProperty("<EMAIL>")
        String context;
        @JsonProperty("<EMAIL>")
        String[] additionalRelatedPersonnelOrLocations;
        @JsonProperty("<EMAIL>")
        String[] attachments;
}



