package com.itcinfotech.windchill.complaints.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.itcinfotech.windchill.complaints.dto.*;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record ComplaintRequest(

        @JsonProperty("@odata.type")
        String oDataType, // Ex: #PTC.CEM.MozarcComplaint

        @JsonProperty("AdditionalInformation")
        String additionalInformation,

        @JsonProperty("HowReported")
        HowReported howReported,

        @JsonProperty("DateApproximate")
        boolean dateApproximate,

        @JsonProperty("EventLocation")
        EventLocation eventLocation,

        @JsonProperty("CountryOfOrigin")
        CountryOfOrigin countryOfOrigin,

        @JsonProperty("CountryOfEvent")
        CountryOfEvent countryOfEvent,

        @JsonProperty("Summary")
        String summary,

        @JsonProperty("SourceIntakeSystem")
        String sourceIntakeSystem,

        @JsonProperty("SourceIntakeUserName")
        String sourceIntakeUserName,

        @JsonProperty("PrimaryCode")
        String primaryCode,

        @JsonProperty("PrimaryCodePath")
        String primaryCodePath,

        @JsonProperty("Date")
        String date,

        @JsonProperty("<EMAIL>")
        String context, // Ex: Containers('OR:wt.inf.library.WTLibrary:248322')

        @JsonProperty("PrimaryRelatedPersonOrLocation")
        PrimaryRelatedPersonOrLocation primaryRelatedPersonOrLocation,

        @JsonProperty("AdditionalRelatedPersonnelOrLocation")
        List<PrimaryRelatedPersonOrLocation> additionalRelatedPersonnelOrLocation,

        @JsonProperty("PrimaryRelatedProduct")
        PrimaryRelatedProduct primaryRelatedProduct,

        Integer noOfFiles,
        List<String> attachments

) {}
