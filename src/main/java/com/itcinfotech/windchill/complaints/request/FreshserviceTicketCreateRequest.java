package com.itcinfotech.windchill.complaints.request;

import com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public class FreshserviceTicketCreateRequest {
    
    private FreshserviceTicketRequest ticketRequest;
    private List<MultipartFile> attachments;
    
    // Constructors
    public FreshserviceTicketCreateRequest() {
    }
    
    public FreshserviceTicketCreateRequest(FreshserviceTicketRequest ticketRequest, List<MultipartFile> attachments) {
        this.ticketRequest = ticketRequest;
        this.attachments = attachments;
    }
    
    // Getters and Setters
    public FreshserviceTicketRequest getTicketRequest() {
        return ticketRequest;
    }
    
    public void setTicketRequest(FreshserviceTicketRequest ticketRequest) {
        this.ticketRequest = ticketRequest;
    }
    
    public List<MultipartFile> getAttachments() {
        return attachments;
    }
    
    public void setAttachments(List<MultipartFile> attachments) {
        this.attachments = attachments;
    }
}
