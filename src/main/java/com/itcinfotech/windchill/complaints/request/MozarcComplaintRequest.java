package com.itcinfotech.windchill.complaints.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.itcinfotech.windchill.complaints.dto.*;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record MozarcComplaintRequest(

        @JsonProperty("@odata.type")
        String oDataType, // Ex: #PTC.CEM.MozarcComplaint

        @JsonProperty("ComPtcmscloudSourceIntakeSystem")
        String sourceIntakeSystem,

        @JsonProperty("ComPtcmscloudSourceComplaintReporterName")
        String reporterName,

        @JsonProperty("ComPtcmscloudSourceIntakeRecordID")
        String complaintId,

        @JsonProperty("ComPtcmscloudSourceIntakeUserName")
        String sourceIntakeUserName,

        @JsonProperty("HowReported")
        HowReported howReported,

        @JsonProperty("DateApproximate")
        Boolean dateApproximate,

        @JsonProperty("EventLocation")
        EventLocation eventLocation,

        @JsonProperty("CountryOfOrigin")
        CountryOfOrigin countryOfOrigin,

        @JsonProperty("CountryOfEvent")
        CountryOfEvent countryOfEvent,

        @JsonProperty("ComPtcmscloudSourceComplaintContactEmail")
        String sourceComplaintContactEmail,

        @JsonProperty("ComPtcmscloudSourceComplaintContactPhone")
        String sourceComplaintContactPhone,

        @JsonProperty("ComPtcmscloudSourceComplaintCreationDate")
        String sourceComplaintCreationDate,

        @JsonProperty("ComPtcmscloudSourcePatientInvolvement")
        String sourcePatientInvolvement,

        @JsonProperty("ComPtcmscloudSourcePatientImpactDescription")
        String sourcePatientImpactDescription,

        @JsonProperty("ComPtcmscloudSourceIntervention")
        String sourceIntervention,

        @JsonProperty("ComPtcmscloudSourcePatientOutcome")
        String sourcePatientOutcome,

        @JsonProperty("Date")
        String date,

        @JsonProperty("Summary")
        String summary,

        @JsonProperty("<EMAIL>")
        String context, // Ex: Containers('OR:wt.inf.library.WTLibrary:248322')

        @JsonProperty("PrimaryRelatedPersonOrLocation")
        PrimaryRelatedPersonOrLocation primaryRelatedPersonOrLocation,

        @JsonProperty("AdditionalRelatedPersonnelOrLocations")
        List<AdditionalRelatedPersonnelOrLocations> additionalRelatedPersonnelOrLocation,

        @JsonProperty("PrimaryRelatedProduct")
        PrimaryRelatedProduct primaryRelatedProduct,

        @JsonProperty("AdditionalRelatedProducts")
        List<PrimaryRelatedProduct> additionalRelatedProducts,

        @JsonIgnore
        List<String> attachments

) {}
