package com.itcinfotech.windchill.complaints.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.itcinfotech.windchill.complaints.dto.*;



public record ComplaintResponse(
        @JsonProperty("@odata.context")
        String odataContext,

        @JsonProperty("CreatedOn")
        String createdOn,

        @JsonProperty("ID")
        String id,

        @JsonProperty("LastModified")
        String lastModified,

        @JsonProperty("AdditionalInformation")
        String additionalInformation,

        @JsonProperty("Circumstance")
        Circumstance circumstance,

        @JsonProperty("CountryOfEvent")
        CountryOfEvent countryOfEvent,

        @JsonProperty("CountryOfOrigin")
        CountryOfOrigin countryOfOrigin,

        @JsonProperty("CreatedBy")
        String createdBy,

        @JsonProperty("Date")
        String date,

        @JsonProperty("DateApproximate")
        boolean dateApproximate,

        @JsonProperty("DevicifyKey")
        String devicifyKey,

        @JsonProperty("EventLocation")
        EventLocation eventLocation,

        @JsonProperty("HowReported")
        HowReported howReported,

        @JsonProperty("LifeCycleTemplateName")
        String lifeCycleTemplateName,

        @JsonProperty("ModifiedBy")
        String modifiedBy,

        @JsonProperty("Name")
        String name,

        @JsonProperty("Number")
        String number,

        @JsonProperty("ObjectType")
        String objectType,

        @JsonProperty("PrimaryCode")
        String primaryCode,

        @JsonProperty("PrimaryCodePath")
        String primaryCodePath,

        @JsonProperty("State")
        State state,

        @JsonProperty("Summary")
        String summary,

        @JsonProperty("TypeIcon")
        TypeIcon typeIcon,

        @JsonProperty("@PTC.AppliedContainerContext.LocalTimeZone")
        String localTimeZone
) { }
