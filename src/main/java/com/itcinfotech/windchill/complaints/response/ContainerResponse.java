package com.itcinfotech.windchill.complaints.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.itcinfotech.windchill.complaints.dto.*;

import java.util.List;


public record ContainerResponse(
        @JsonProperty("@odata.context")
        String odataContext,

        @JsonProperty("value")
        List<ContainerValue> value,

        @JsonProperty("@PTC.AppliedContainerContext.LocalTimeZone")
        String localTimeZone,

        @JsonProperty("@odata.nextLink")
        String oDataNextLink


) {

}
