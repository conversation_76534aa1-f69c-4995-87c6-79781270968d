package com.itcinfotech.windchill.complaints.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.itcinfotech.windchill.complaints.dto.ManufacturingValue;
import com.itcinfotech.windchill.complaints.dto.PartValue;

import java.util.List;


public record ManufacturingResponse(

        @JsonProperty("@odata.context")
        String odataContext,

        @JsonProperty("value")
        List<ManufacturingValue> value,

        @JsonProperty("@PTC.AppliedContainerContext.LocalTimeZone")
        String localTimeZone,

        @JsonProperty("@odata.nextLink")
        String oDataNextLink
)
{ }
