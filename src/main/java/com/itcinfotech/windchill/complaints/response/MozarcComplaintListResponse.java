package com.itcinfotech.windchill.complaints.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.itcinfotech.windchill.complaints.dto.MozarcComplaintValue;
import com.itcinfotech.windchill.complaints.dto.PeopleValue;

import java.util.List;


public record MozarcComplaintListResponse(

        @JsonProperty("@odata.context")
        String odataContext,

        @JsonProperty("value")
        List<MozarcComplaintValue> value,

        @JsonProperty("@PTC.AppliedContainerContext.LocalTimeZone")
        String localTimeZone,

        @JsonProperty("@odata.nextLink")
        String oDataNextLink

) {

}
