package com.itcinfotech.windchill.complaints.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.itcinfotech.windchill.complaints.dto.*;


public record MozarcComplaintResponse(

        @JsonProperty("ComPtcmscloudSourceIntakeSystem")
        String ptcSourceIntakeSystem,

        @JsonProperty("EventLocation")
        EventLocation eventLocation,

        @JsonProperty("ModifiedBy")
        String modifiedBy,

        @JsonProperty("AdditionalInformation")
        String additionalInformation,

        @JsonProperty("ComPtcmscloudCode")
        String ptcCloudCode,

        @JsonProperty("DateApproximate")
        boolean dateApproximate,

        @JsonProperty("ComPtcmscloudRrDueDate")
        String ptcCloudRrDueDate,

        @JsonProperty("ComPtcmscloudRrDecisionType")
        String ptcCloudRrDecisionType,

        @JsonProperty("ComPtcmscloudRegulatoryRptId")
        String ptcCloudRegulatoryRptId,

        @JsonProperty("LastModified")
        String lastModified,

        @JsonProperty("ComPtcmscloudProductEventStatus")
        String ptcCloudProductEventStatus,

        @JsonProperty("LifeCycleTemplateName")
        String lifeCycleTemplateName,

        @JsonProperty("ComPtcmscloudReportableDecisionId")
        String ptcCloudReportTableDecisionId,

        @JsonProperty("Date")
        String date,

        @JsonProperty("ComPtcmscloudFdaCode")
        String ptcCloudFdaCode,

        @JsonProperty("ObjectType")
        String objectType,

        @JsonProperty("Summary")
        String summary,

        @JsonProperty("ComPtcmscloudComplaint")
        String ptcCloudComplaint,

        @JsonProperty("CreatedOn")
        String createdOn,

        @JsonProperty("ID")
        String id,

        @JsonProperty("ComPtcmscloudRdReportable")
        Boolean ptcCloudReportTable,

        @JsonProperty("ComPtcmscloudProductEvent")
        String ptcCloudProductEvent,

        @JsonProperty("Number")
        String number,

        @JsonProperty("ComPtcmscloudLineItemNo")
        String ptcCloudLineItemNo,

        @JsonProperty("ComPtcmscloudInvestigationDecisionMadeBy")
        String ptcCloudInvestigationDecisionMadeBy,

        @JsonProperty("ComPtcmscloudNcitCode")
        String ptcCloudNcitCode,

        @JsonProperty("ComPtcmscloudRrTimeline")
        String ptcCloudRrTimeLine,

        @JsonProperty("HowReported")
        HowReported howReported,

        @JsonProperty("PrimaryCode")
        String primaryCode,

        @JsonProperty("ComPtcmscloudRrDateSubmitted")
        String ptcCloudRrDateSubmitted,

        @JsonProperty("ComPtcmscloudLot")
        String ptcCloudLot,

        @JsonProperty("ComPtcmscloudComplaintSourceSystemID")
        String ptcCloudComplaintSourceSystemID,

        @JsonProperty("ComPtcmscloudInvestigationId")
        String ptcCloudInvestigationId,

        @JsonProperty("ComPtcmscloudInvestigationStatus")
        String ptcCloudInvestigationStatus,

        @JsonProperty("Name")
        String name,

        @JsonProperty("ComPtcmscloudRegulatoryBody")
        String ptcCloudRegulatoryBody,

        @JsonProperty("ComPtcmscloudRrStatus")
        String ptccloudRrStatus,

        @JsonProperty("ComPtcmscloudMfgSiteId")
        String ptccloudMfgSiteId,

        @JsonProperty("ComPtcmscloudRdDecisionMadeBy")
        String ptccloudRdDecisionMadeBy,

        @JsonProperty("ComPtcmscloudImplantDate")
        String ptccloudImplantDate,

        @JsonProperty("ComPtcmscloudRdDecisionType")
        String ptccloudRdDecisionType,

        @JsonProperty("ComPtcmscloudRdDecisionDate")
        String ptccloudRdDecisionDate,

        @JsonProperty("ComPtcmscloudRegulatoryReportNo")
        String ptccloudRegulatoryReportNo,

        @JsonProperty("ComPtcmscloudInvestigationReq")
        String ptccloudInvestigationReq,

        @JsonProperty("CreatedBy")
        String createdBy,

        @JsonProperty("ComPtcmscloudReportabilityGrp")
        String ptccloudReportabilityGrp,

        @JsonProperty("Circumstance")
        Circumstance circumstance,

        @JsonProperty("ComPtcmscloudProductEventType")
        String ptccloudProductEventType,

        @JsonProperty("TestAgeComplaint")
        String testAgeComplaint,

        @JsonProperty("CountryOfOrigin")
        CountryOfOrigin countryOfOrigin,

        @JsonProperty("ComPtcmscloudCodeType")
        String ptccloudCodeType,

        @JsonProperty("ComPtcmscloudRdStatus")
        String ptccloudRdStatus,

        @JsonProperty("PrimaryCodePath")
        String primaryCodePath,

        @JsonProperty("ComPtcmscloudInvDecisionDate")
        String ptccloudInvDecisionDate,

        @JsonProperty("ComPtcmscloudExplantDate")
        String ptccloudInvExplantDate,

        @JsonProperty("ComPtcmscloudSerialNo")
        String ptccloudSerialNo,

        @JsonProperty("ComPtcmscloudNotifiedDate")
        String ptccloudNotifiedDate,

        @JsonProperty("ComPtcmscloudSourceIntakeUserName")
        String ptccloudSourceIntakeUserName,

        @JsonProperty("ComPtcmscloudInvCompletedDate")
        String ptccloudInvCompletedDate,

        @JsonProperty("ComPtcmscloudSourceIntakeRecordID")
        String ptccloudSourceIntakeRecordID,

        @JsonProperty("ComPtcmscloudSourceComplaintReporterName")
        String ptccloudSourceComplaintReporterName,

        @JsonProperty("ComPtcmscloudSourceComplaintContactEmail")
        String sourceComplaintContactEmail,

        @JsonProperty("ComPtcmscloudSourceComplaintContactPhone")
        String sourceComplaintContactPhone,

        @JsonProperty("ComPtcmscloudSourcePatientInvolvement")
        String sourcePatientInvolvement,

        @JsonProperty("ComPtcmscloudSourcePatientImpactDescription")
        String sourcePatientImpactDescription,

        @JsonProperty("ComPtcmscloudSourceIntervention")
        String sourceIntervention,

        @JsonProperty("ComPtcmscloudSourcePatientOutcome")
        String sourcePatientOutcome,

        @JsonProperty("ComPtcmscloudSourceComplaintCreationDate")
        String sourceComplaintCreationDate,

        @JsonProperty("CountryOfEvent")
        CountryOfEvent countryOfEvent,

        @JsonProperty("State")
        State state,

        @JsonProperty("TypeIcon")
        TypeIcon typeIcon,

        @JsonProperty("@odata.context")
        String odataContext,

        @JsonProperty("@PTC.AppliedContainerContext.LocalTimeZone")
        String localTimeZone
) { }
