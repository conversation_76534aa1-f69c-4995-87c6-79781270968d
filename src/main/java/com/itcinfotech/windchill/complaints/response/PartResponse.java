package com.itcinfotech.windchill.complaints.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.itcinfotech.windchill.complaints.dto.ContainerValue;
import com.itcinfotech.windchill.complaints.dto.PartValue;

import java.util.List;


public record PartResponse(

        @JsonProperty("@odata.context")
        String odataContext,

        @JsonProperty("value")
        List<PartValue> value,

        @JsonProperty("@PTC.AppliedContainerContext.LocalTimeZone")
        String localTimeZone,

        @JsonProperty("@odata.nextLink")
        String oDataNextLink

)
{ }
