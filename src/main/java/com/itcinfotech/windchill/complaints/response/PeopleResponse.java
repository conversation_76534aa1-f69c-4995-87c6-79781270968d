package com.itcinfotech.windchill.complaints.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.itcinfotech.windchill.complaints.dto.PeopleValue;

import java.util.List;


public record PeopleResponse(

        @JsonProperty("@odata.context")
        String odataContext,

        @JsonProperty("value")
        List<PeopleValue> value,

        @JsonProperty("@PTC.AppliedContainerContext.LocalTimeZone")
        String localTimeZone,

        @JsonProperty("@odata.nextLink")
        String oDataNextLink

) {

}
