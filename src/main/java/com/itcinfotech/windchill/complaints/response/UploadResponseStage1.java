package com.itcinfotech.windchill.complaints.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.itcinfotech.windchill.complaints.dto.ContentValue;

import java.util.List;


public record UploadResponseStage1(
        @JsonProperty("@odata.context")
        String context,

        @JsonProperty("value")
        List<ContentValue> value,

        @JsonProperty("@PTC.AppliedContainerContext.LocalTimeZone")
        String localTimeZone
) {}

