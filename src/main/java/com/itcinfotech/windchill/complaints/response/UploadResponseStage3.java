package com.itcinfotech.windchill.complaints.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.itcinfotech.windchill.complaints.dto.ContentInfoStage3;


import java.util.List;


public record UploadResponseStage3(

        @JsonProperty("@odata.context")
        String oDataContext,

        @JsonProperty("@PTC.AppliedContainerContext.LocalTimeZone")
        String localTimeZone,

        @JsonProperty("value")
        List<ContentInfoStage3> value

) {}

