package com.itcinfotech.windchill.complaints.service;

import com.azure.storage.blob.*;
import com.azure.storage.blob.models.*;
import com.azure.storage.blob.specialized.BlobInputStream;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.scheduling.annotation.Scheduled;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import jakarta.annotation.PostConstruct;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;

@Service
public class AzureBlobService {

    private static final Logger logger = LoggerFactory.getLogger(AzureBlobService.class);

    @Value("${azure.localDownloadDir}")
    private String localDownloadDir;

    @Value("${azure.endpoint}")
    private String endpoint;

    @Value("${azure.erpFilesDir}")
    private String erpFilesDir;

    @Value("${azure.container}")
    private String container;

    private static final String XML_SUFFIX = ".xml";
    private static final String JSON_SUFFIX = ".json";

    private BlobServiceClient blobServiceClient;
    private BlobContainerClient containerClient;
    private final Object lock = new Object();

    @PostConstruct
    public void init() {
        connect();
    }

    private void connect() {
        if (containerClient != null) return;
        synchronized (lock) {
            if (containerClient == null) {
                logger.info("AzureBlobService connecting...");
                try {
                    blobServiceClient = new BlobServiceClientBuilder()
                            .endpoint(endpoint)
                            .buildClient();
                    containerClient = blobServiceClient.getBlobContainerClient(container);
                    logger.info("ContainerURL: {}", containerClient.getBlobContainerUrl());
                } catch (Throwable t) {
                    logger.error("Issues connecting with Azure: {}", t.getMessage(), t);
                }
                logger.info("AzureBlobService connected");
            }
        }
    }

    public ArrayList<BlobItem> listESIItems() {
        logger.info("AzureBlobService listESIItems...");
        connect();
        ArrayList<BlobItem> returnList = new ArrayList<>();
        for (BlobItem blobItem : containerClient.listBlobs()) {
            if (blobItem.getName().startsWith(erpFilesDir)
                    && (blobItem.getName().endsWith(XML_SUFFIX)
                    || blobItem.getName().endsWith(JSON_SUFFIX))
                    && !blobItem.isDeleted()) {
                returnList.add(blobItem);
                logger.info("\tAdded: {}, isDeleted: {}", blobItem.getName(), blobItem.isDeleted());
            }
        }
        return returnList;
    }

    @Scheduled(cron = "0 0/1 * * * ?") // Runs every minute
    public ArrayList<BlobItem> downloadNewFiles() {
        logger.info("AzureBlobService downloadNewFiles...");
        ArrayList<BlobItem> downloadedFiles = new ArrayList<>();
        try {
            ArrayList<BlobItem> currentFiles = listESIItems();
            ArrayList<BlobItem> filteredFiles = filterPrevDownloadedFiles(currentFiles);

            for (BlobItem blobItem : filteredFiles) {
                boolean success = downloadBlobItem(blobItem, localDownloadDir);
                if (success) {
                    downloadedFiles.add(blobItem);
                    logger.info("\tAdded: {}", blobItem.getName());
                    String fileName = getFilenameFromBlobName(blobItem.getName());
                    handleDownloadedFile(localDownloadDir, fileName);
                }
            }
        } catch (Throwable e) {
            logger.error("Error in downloadNewFiles: {}", e.getMessage(), e);
        }
        return downloadedFiles;
    }

    private void handleDownloadedFile(String localDirectory, String fileName) {
        logger.info("AzureBlobService handleDownloadedFile...");
        logger.info("\tfile: {}/{}", localDirectory, fileName);
        logger.info("\tThis Method for handling the downloaded file will be implemented by the customer");
    }

    private boolean downloadBlobItem(BlobItem blobItem, String downLoadPath) {
        logger.info("AzureBlobService downloadBlobItem...");
        boolean success = false;
        BlobClient newBlobClient = containerClient.getBlobClient(blobItem.getName());
        try (BlobInputStream blobIS = newBlobClient.openInputStream()) {
            String fileName = getFilenameFromBlobName(blobItem.getName());
            logger.info("fileName: {}", fileName);
            String localFilePath = Paths.get(downLoadPath, fileName).toString();
            // Use buffered stream to avoid OOM for large files
            try (java.io.OutputStream out = Files.newOutputStream(Paths.get(localFilePath))) {
                byte[] buffer = new byte[8192];
                int len;
                while ((len = blobIS.read(buffer)) != -1) {
                    out.write(buffer, 0, len);
                }
            }
            success = true;
        } catch (Exception e) {
            logger.error("Error downloading blob {}: {}", blobItem.getName(), e.getMessage(), e);
        }
        return success;
    }

    private ArrayList<BlobItem> filterPrevDownloadedFiles(ArrayList<BlobItem> currentFiles) {
        // TODO: design a Filter (for now, return all files)
        return currentFiles;
    }

    private String getFilenameFromBlobName(String blobName) {
        return blobName.substring(blobName.lastIndexOf('/') + 1);
    }

    public ArrayList<BlobItem> listAllItems() {
        logger.info("AzureBlobService listAllItems...");
        connect();
        logger.info("erpFilesDir: {}", erpFilesDir);
        ArrayList<BlobItem> returnList = new ArrayList<>();
        try {
            for (BlobItem blobItem : containerClient.listBlobs()) {
                if (blobItem.getName().startsWith(erpFilesDir)
                        && !blobItem.isDeleted()) {
                    returnList.add(blobItem);
                    logger.info("\tAdded: {}, isDeleted: {}", blobItem.getName(), blobItem.isDeleted());
                }
            }
        } catch (Throwable t) {
            logger.error("Issues Listing Blobs: {}", t.getMessage(), t);
        }
        return returnList;
    }
}