package com.itcinfotech.windchill.complaints.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;
import org.springframework.http.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.atomic.AtomicReference;
import java.util.Date;

@Service
public class BearerTokenService implements CommandLineRunner {
    private static final Logger logger = LoggerFactory.getLogger(BearerTokenService.class);

    @Value("${windchill.bearer.token.url}")
    private String tokenUrl;

    @Value("${windchill.client.id}")
    private String clientId;

    @Value("${windchill.client.secret}")
    private String clientSecret;

    @Value("${windchill.grant.type}")
    private String grantType;

    @Value("${windchill.scope}")
    private String scope;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final AtomicReference<String> bearerToken = new AtomicReference<>();
    private long tokenExpiryTime;

    public BearerTokenService(RestTemplate restTemplate, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }

    /**
     * Runs the command line runner. This method is called after the application context
     * has been loaded. It fetches and stores the bearer token.
     *
     * @param args the arguments passed to the application
     * @throws Exception if an exception occurs while fetching the token
     */
    @Override
    public void run(String... args) throws Exception {
        // Fetch the token after the application has started
        fetchAndStoreToken();
    }

    /**
     * Gets the current bearer token. If the token has expired, it is automatically
     * refreshed and the new token is returned.
     *
     * @return the current bearer token
     */
    public String getBearerToken() {
        if (System.currentTimeMillis() >= tokenExpiryTime) {
            fetchAndStoreToken();
        }
        return bearerToken.get();
    }

    /**
     * Fetches the bearer token from the authentication server and stores it for future use.
     * The token is retrieved using client credentials and is set to expire after a period
     * specified by the server. If the token retrieval fails or the response is invalid,
     * a RuntimeException is thrown. After successful retrieval, the token and its expiry
     * time are logged.
     *
     * @throws RuntimeException if the token retrieval fails or the response is invalid
     */
    private void fetchAndStoreToken() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        String body = String.format("client_id=%s&client_secret=%s&grant_type=%s&scope=%s",
                clientId, clientSecret, grantType, scope);

        HttpEntity<String> entity = new HttpEntity<>(body, headers);

        ResponseEntity<String> response = restTemplate.postForEntity(tokenUrl, entity, String.class);
        if (response.getStatusCode() != HttpStatus.OK) {
            throw new RuntimeException("Failed to retrieve token: " + response.getStatusCode());
        }
        try {
            JsonNode responseBody = objectMapper.readTree(response.getBody());
            if (!responseBody.has("access_token") || !responseBody.has("expires_in")) {
                throw new RuntimeException("Invalid token response: " + responseBody.toString());
            }
            bearerToken.set(responseBody.path("access_token").asText());
            int expiresIn = responseBody.path("expires_in").asInt();
            tokenExpiryTime = System.currentTimeMillis() + (expiresIn * 1000);
        } catch (Exception e) {
            logger.error("Error retrieving bearer token: ", e);
            throw new RuntimeException("Error retrieving bearer token: ", e);
        }
        // Log the access token and expiry time
        logger.info("Access Token: {}", bearerToken.get());
        logger.info("Token Expiry Time: {}", new Date(tokenExpiryTime));
    }


}
