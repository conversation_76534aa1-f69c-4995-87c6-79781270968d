package com.itcinfotech.windchill.complaints.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.itcinfotech.windchill.complaints.dto.*;
import com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest;
import com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketResponse;
import com.itcinfotech.windchill.complaints.exception.DuplicateComplaintException;
import com.itcinfotech.windchill.complaints.request.ComplaintRequest;
import com.itcinfotech.windchill.complaints.request.FreshserviceTicketCreateRequest;
import com.itcinfotech.windchill.complaints.request.MozarcComplaintRequest;
import com.itcinfotech.windchill.complaints.request.UploadRequestStage1;
import com.itcinfotech.windchill.complaints.response.*;
import com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketService;
import com.itcinfotech.windchill.complaints.utils.ZipUtil;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.web.multipart.MultipartFile;




import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.util.*;

import static org.springframework.util.FileSystemUtils.deleteRecursively;

@Service
public class ComplaintService {

    @Value("${windchill.host}")
    private String host;

    @Value("${windchill.complaint.create.url}")
    private String apiUrl;



    @Value("${camel.files.integrationRoot.dir}")
    private String integrationRootDir;


    private final CsrfTokenService csrfTokenService;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    private static final Logger logger = LoggerFactory.getLogger(ComplaintService.class);

    @Autowired
    private  UploadService uploadService;

    @Autowired
    private ContainerService containerService;

    @Autowired
    private PartService partService;

    @Autowired
    private ManufacturingService manufacturingService;

    @Autowired
    private PeopleService peopleService;

    @Autowired
    private FreshserviceTicketService freshserviceTicketService;

    @Autowired
    private AuthenticationService authenticationService;

    @Autowired
    public ComplaintService(RestTemplate restTemplate, CsrfTokenService csrfTokenService, ObjectMapper objectMapper, UploadService uploadService) {
        this.restTemplate = restTemplate;
        this.csrfTokenService = csrfTokenService;
        this.objectMapper = objectMapper;
    }

    public ResponseEntity<Object> createComplaintFromJson(MozarcComplaintRequest request, Integer noOfFiles) throws JsonProcessingException {
        String json = objectMapper.writeValueAsString(request);
        String token = csrfTokenService.getCsrfToken();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.set("Authorization", authenticationService.getAuthorizationHeader());
        headers.set("CSRF_NONCE", token);

        HttpEntity<String> entity = new HttpEntity<>(json, headers);

        try {
            ResponseEntity<String> response = restTemplate.exchange(host + apiUrl, HttpMethod.POST, entity, String.class);
            MozarcComplaintResponse complaintResponse = objectMapper.readValue(response.getBody(), MozarcComplaintResponse.class);
            String complaintId = "";
            complaintId = complaintResponse.id();

            if(!complaintId.equals("")){
                UploadRequestStage1 uploadRequest = new UploadRequestStage1(noOfFiles);
                //uploadService.uploadStage1(uploadRequest, token, complaintId);
            }


            return ResponseEntity.ok(complaintResponse);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ComplaintErrorResponse(new ComplaintError("500", e.getMessage())));
        }
    }


    public List<MozarcComplaintRequest> extractDataFromCsv(File csvFile, File processingDir) throws IOException {
        List<MozarcComplaintRequest> complaints = new ArrayList<>();
        List<MozarcComplaintRequest> erroredComplaints = new ArrayList<>(); // New: collect errored complaints

        List<String> attachments = new ArrayList<>();
        List<String> existingComplaints = new ArrayList<>();
        List<String> processingErrors = new ArrayList<>(); // New: collect processing errors
        Boolean isSuccess = true;

        // Validate input parameters
        if (csvFile == null || !csvFile.exists()) {
            throw new IOException("CSV file does not exist: " + (csvFile != null ? csvFile.getAbsolutePath() : "null"));
        }

        if (!csvFile.canRead()) {
            throw new IOException("Cannot read CSV file: " + csvFile.getAbsolutePath());
        }

        logger.info("Starting CSV processing for file: {}", csvFile.getAbsolutePath());

        try (CSVParser parser = CSVParser.parse(new FileReader(csvFile, StandardCharsets.UTF_8), CSVFormat.DEFAULT.withQuote('"'))) {

            List<CSVRecord> records = parser.getRecords();
            logger.info("Found {} records in CSV file", records.size());

            if (records.isEmpty()) {
                logger.warn("CSV file is empty: {}", csvFile.getAbsolutePath());
                throw new IOException("CSV file is empty");
            }

            // Process records with error handling
            int processedCount = 0;
            int errorCount = 0;
            final int MAX_ERRORS = 10; // Stop processing if too many errors

            for (int i = 1; i < records.size(); i++) {
                try {
                    attachments.clear();

                    CSVRecord row = records.get(i);

                    if (row.size() < 38) {
                        logger.warn("Row {} has insufficient columns ({}), skipping", i, row.size());
                        continue;
                    }

                String oDataType = "#" + row.get(0);
                String complaintId = row.get(1);

                if(checkComplaint(complaintId) != null){
                    existingComplaints.add(complaintId + " at row number: " + i);
                    logger.info("Detected existing Complaint with ID: " + complaintId);
                    isSuccess = false;
                    continue;
                }

                HowReported howReported = new HowReported(row.get(2), "");
                String complaintDate = row.get(3);
                EventLocation eventLocation = new EventLocation(row.get(5), "");
                CountryOfOrigin countryOfOrigin = new CountryOfOrigin(row.get(6), "");
                CountryOfEvent countryOfEvent = new CountryOfEvent(row.get(7), "");
                String summary = row.get(8);
                String sourceIntakeSystem = row.get(9);
                String sourceIntakeUserName = row.get(10);
                String containerId = String.valueOf(containerService.getContainerID(row.get(11)));
                String partId = String.valueOf(partService.getPartID(row.get(12)));
                Integer productQuantity = Integer.valueOf(row.get(13));
                UnitOfMeasure uom = new UnitOfMeasure(row.get(14));
                String serialLot = row.get(15);
                // Only call getManufacturingLocationID if there is a value in row.get(17)
                String placeId;
                String manufacturingLocation = row.get(17);
                if (manufacturingLocation != null && !manufacturingLocation.trim().isEmpty()) {
                    placeId = String.valueOf(manufacturingService.getManufacturingLocationID(manufacturingLocation));
                } else {
                    placeId = null; // Set to null when no manufacturing location
                }
                String patientId = String.valueOf(peopleService.getPatientID("Confidential", "Patient"));

                List<PrimaryRelatedProduct> additionalProducts = partService.getAdditionalProducts(
                        row.get(18).split("\\|:\\|")
                );

                String reporterName = row.get(21);
                String reporterId = row.get(22);
                String complaintEmail = row.get(24);
                String complaintPhone = row.get(25);
                String complaintCreated = row.get(26);
                String patientInvolvement = row.get(27);
                String patientImpact = row.get(28);
                String intervention = row.get(29);
                String outcome = row.get(30);

                String oDataContext = "Containers('" + containerId + "')";
                String oDataSubject = "Parts('" + partId + "')";
                // Only create oDataManufacturingLocation if placeId is not null
                String oDataManufacturingLocation = null;
                if(placeId != null) {
                    oDataManufacturingLocation = "Places('" + placeId + "')";
                }
                String oDataPatient = "PeopleOrPlaces('" + patientId + "')";

                PrimaryRelatedPersonOrLocation prp = new PrimaryRelatedPersonOrLocation(
                        "#" + row.get(31), oDataPatient,
                        Integer.valueOf(row.get(32)), new AgeUnits(row.get(33)), row.get(37), false,
                        new Gender(row.get(34)), Integer.valueOf(row.get(35)), new WeightUnits(row.get(36))
                );

                PrimaryRelatedProduct primaryProduct = new PrimaryRelatedProduct(
                        oDataSubject, Boolean.valueOf(row.get(17)), true,
                        productQuantity, serialLot, uom, oDataManufacturingLocation
                );

                List<AdditionalRelatedPersonnelOrLocations> additionalPeople = peopleService.getAdditionalPeople(reporterId);

                for (int j = 38; j < row.size(); j++) {
                    String name = row.get(j).trim();
                    if (!name.isEmpty()) attachments.add(name);
                }

                List<String> attachmentsToProcess = new ArrayList<>(attachments);


                MozarcComplaintRequest request = new MozarcComplaintRequest(
                        oDataType, sourceIntakeSystem, reporterName, complaintId, sourceIntakeUserName,
                        howReported, false, eventLocation, countryOfOrigin, countryOfEvent,
                        complaintEmail, complaintPhone, complaintCreated, patientInvolvement,
                        patientImpact, intervention, outcome, complaintDate, summary,
                        oDataContext, prp, additionalPeople, primaryProduct, additionalProducts, attachmentsToProcess
                );

                complaints.add(request);
                processedCount++;

                } catch (Exception e) {
                    errorCount++;
                    logger.error("Error processing row {}: {}", i, e.getMessage(), e);

                    // Create a partial complaint object for error reporting
                    try {
                        CSVRecord row = records.get(i);
                        String complaintId = row.size() > 1 ? row.get(1) : "Unknown-" + i;
                        String summary = row.size() > 8 ? row.get(8) : "Error processing row data";
                        String reporterName = row.size() > 21 ? row.get(21) : "Unknown Reporter";
                        String sourceIntakeSystem = row.size() > 9 ? row.get(9) : "Unknown System";
                        String sourceIntakeUserName = row.size() > 10 ? row.get(10) : "Unknown User";

                        // Create a minimal complaint object with error information
                        MozarcComplaintRequest errorRequest = createErrorComplaintRequest(
                            complaintId, summary, reporterName, sourceIntakeSystem,
                            sourceIntakeUserName, e.getMessage(), i
                        );

                        erroredComplaints.add(errorRequest);
                        processingErrors.add("Row " + i + " - Complaint ID: " + complaintId + " - Error: " + e.getMessage());

                    } catch (Exception innerE) {
                        // If we can't even create an error complaint, just log it
                        logger.error("Failed to create error complaint for row {}: {}", i, innerE.getMessage());
                        processingErrors.add("Row " + i + " - Critical Error: Unable to process row data - " + e.getMessage());
                    }

                    isSuccess = false;

                    // Stop processing if too many errors
                    if (errorCount >= MAX_ERRORS) {
                        logger.error("Too many errors ({}), stopping CSV processing", errorCount);
                        break;
                    }
                }
            }

            logger.info("CSV processing completed. Processed: {}, Errors: {}", processedCount, errorCount);

        } catch (Exception e) {
            logger.error("Fatal error processing CSV file: {}", e.getMessage(), e);
            isSuccess = false;
            throw new IOException("Failed to process CSV file: " + e.getMessage(), e);
        }

        // Handle different scenarios based on successful vs errored complaints
        if (complaints.isEmpty() && erroredComplaints.isEmpty()) {
            // No complaints processed at all - use existing logic
            File zipFile = getZipFileFromDirectory(integrationRootDir);
            openFreshserviceTicketForComplaints(existingComplaints, zipFile);
            logger.info("No Complaints to create, moving zip file: "  + zipFile.getName() +" to /Error Directory");
            moveZipAndCleanProcessing(integrationRootDir, false);
        } else if (complaints.isEmpty() && !erroredComplaints.isEmpty()) {
            // Only errored complaints - create ticket with error details
            File zipFile = getZipFileFromDirectory(integrationRootDir);
            openFreshserviceTicketForErrors(erroredComplaints, processingErrors, existingComplaints, zipFile);
            logger.info("Only errored complaints found, moving zip file: " + zipFile.getName() + " to /Error Directory");
            moveZipAndCleanProcessing(integrationRootDir, false);
        } else if (!complaints.isEmpty() && !erroredComplaints.isEmpty()) {
            // Mixed success and errors - process successful ones and create ticket for errors
            createComplaintFromCsv(complaints, processingDir, false); // Mark as not fully successful
            File zipFile = getZipFileFromDirectory(integrationRootDir);
            openFreshserviceTicketForErrors(erroredComplaints, processingErrors, existingComplaints, zipFile);
            logger.info("Mixed results: {} successful, {} errored complaints", complaints.size(), erroredComplaints.size());
        } else {
            // Only successful complaints
            createComplaintFromCsv(complaints, processingDir, isSuccess);
        }

        return complaints;
    }

    public ResponseEntity<Object> createComplaintFromCsv(List<MozarcComplaintRequest> requests, File processingDir, boolean isSucess) throws IOException {

        System.out.println("Complaint creation START.  : " + LocalDateTime.now());
        String token = csrfTokenService.getCsrfToken();
        String attachmentsDir = processingDir.getPath() + "\\attachments\\";


        // Iterate over the ComplaintRequest list
        for (MozarcComplaintRequest request : requests) {
            String json = objectMapper.writeValueAsString(request);

            HttpHeaders headers = buildHeaders(token);
            HttpEntity<String> entity = new HttpEntity<>(json, headers);

            try {

                ResponseEntity<String> response = restTemplate.exchange(host + apiUrl, HttpMethod.POST, entity, String.class);
                MozarcComplaintResponse complaintResponse = objectMapper.readValue(response.getBody(), MozarcComplaintResponse.class);
                String complaintId = complaintResponse.id();

                if (!complaintId.isEmpty()) {

                    UploadRequestStage1 uploadRequest = new UploadRequestStage1(request.attachments().size());
                    uploadService.uploadStage1(uploadRequest, token, complaintId, attachmentsDir, request.attachments());
                    System.out.println("Complaint created successfully: " + complaintResponse.number());
                }

            } catch (Exception e) {

                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new ComplaintErrorResponse(new ComplaintError("500", e.getMessage())));
            }


        }

        moveZipAndCleanProcessing(integrationRootDir, isSucess);
        System.out.println("Complaint creation FINISH.  : " + LocalDateTime.now());

        return ResponseEntity.ok("Complaints created successfully");
    }

    private HttpHeaders buildHeaders(String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.set("Authorization", authenticationService.getAuthorizationHeader());
        headers.set("CSRF_NONCE", token);
        return headers;
    }

    public String checkComplaint(String complaintId) throws JsonProcessingException{

        String token = csrfTokenService.getCsrfToken();
        HttpHeaders headers = buildHeaders(token);
        String filter = "?$filter=ComPtcmscloudSourceIntakeRecordID eq '" + complaintId + "'&$select=ID";
        String url = host + apiUrl + "/PTC.CEM.MozarcComplaint" + filter;

        HttpEntity<String> entity = new HttpEntity<>(null, headers);
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

        MozarcComplaintListResponse complaintResponse = objectMapper.readValue(response.getBody(), MozarcComplaintListResponse.class);

        if (complaintResponse != null && complaintResponse.value() != null && !complaintResponse.value().isEmpty()) {
            return complaintResponse.value().get(0).id();
        }

        return null;
    }

    public void cleanAndMoveZip(String pathStr, String targetDir) throws IOException {
        Path rootPath = Paths.get(pathStr + "/Processing");
        Path integrationDir = Paths.get(pathStr);

        if (!Files.exists(rootPath) || !Files.isDirectory(rootPath)) {
            throw new IllegalArgumentException("Invalid Path.: " + pathStr);
        }

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(rootPath)) {
            for (Path entry : stream) {
                if (Files.isDirectory(entry)) {
                    deleteRecursively(entry);
                }
            }
        }


        try (DirectoryStream<Path> stream = Files.newDirectoryStream(rootPath, "*.zip")) {
            for (Path zipFile : stream) {
                Path targetPath = integrationDir.resolveSibling(targetDir);

                if (!Files.exists(targetPath)) {
                    Files.createDirectories(targetPath);
                }

                Path processingPath = targetPath.resolve(zipFile.getFileName());
                Files.move(zipFile, targetPath, StandardCopyOption.REPLACE_EXISTING);
            }
        }
    }

    private void deleteRecursively(Path path) throws IOException {
        if (Files.notExists(path)) return;

        Files.walk(path)
                .sorted(Comparator.reverseOrder())
                .forEach(p -> {
                    try {
                        Files.deleteIfExists(p);
                        System.out.println("Deleted: " + p);
                    } catch (IOException e) {
                        System.err.println("Error deleting: " + p + " - " + e.getMessage());
                    }
                });
    }

    public void moveZipAndCleanProcessing(String integrationRootDir, boolean isSuccess) throws IOException {
        Path processingDir = Paths.get(integrationRootDir, "Processing");
        Path targetDir = Paths.get(integrationRootDir, isSuccess ? "Success" : "Error");

        if (!Files.exists(targetDir)) {
            Files.createDirectories(targetDir);
        }

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(processingDir)) {
            for (Path entry : stream) {
                String fileName = entry.getFileName().toString();

                if (Files.isRegularFile(entry) && fileName.toLowerCase().endsWith(".zip")) {
                    // Move ZIP to Success or Error
                    Path targetPath = targetDir.resolve(entry.getFileName());
                    Files.move(entry, targetPath, StandardCopyOption.REPLACE_EXISTING);
                    System.out.println("Moved ZIP to " + targetPath);
                } else if (Files.isDirectory(entry)) {
                    // Delete directory recursively
                    deleteRecursively(entry);
                    System.out.println("Deleted folder: " + entry);
                }
            }
        }
    }



    // FreshDesk ticket creation from Complaint Errors
    private String buildDescriptionFromComplaintIds(List<String> complaintIds) {
        StringBuilder description = new StringBuilder("The following Complaints already exist on Windchill and could not ne created:<br/>");
        for (String id : complaintIds) {
            description.append("- Complaint ID: ").append(id).append("<br/>");
        }
        description.append("<br/>Please, verify.");
        return description.toString();
    }

    private FreshserviceTicketRequest buildTicketRequest(List<String> complaintIds) {

        Map<String, Object> customFields = new HashMap<>();
        customFields.put("lf_what_solution_is_having_an_issue", 33000031228L);
        return new FreshserviceTicketRequest(
                "Windchill Integration",
                "<EMAIL>",
                "Complaint Creation Error", // Subject
                buildDescriptionFromComplaintIds(complaintIds),
                2,
                1,
                2,
                null,
                "Applications",
                "Troubleshooting",
                "Incident",
                1,
                1,
                customFields
        );
    }

    public MultipartFile convertFileToMultipart(File file) throws IOException {
        return new org.springframework.mock.web.MockMultipartFile(
                file.getName(),
                file.getName(),
                Files.probeContentType(file.toPath()),
                Files.readAllBytes(file.toPath())
        );
    }

    public FreshserviceTicketCreateRequest buildTicketWithAttachment(List<String> complaintIds, File zipFile) throws IOException {
        FreshserviceTicketRequest ticketRequest = buildTicketRequest(complaintIds);
        MultipartFile multipartZip = convertFileToMultipart(zipFile);
        return new FreshserviceTicketCreateRequest(ticketRequest, List.of(multipartZip));
    }

    public void openFreshserviceTicketForComplaints(List<String> complaintIds, File zipFile) {
        try {
            FreshserviceTicketCreateRequest request = buildTicketWithAttachment(complaintIds, zipFile);

            ResponseEntity<Object> response = freshserviceTicketService.createTicketWithAttachments(request);

            if (response.getStatusCode().is2xxSuccessful()) {
                System.out.println("Ticket created.");
            } else {
                System.err.println("Error creating FreshDesk Ticket. Details: " + response.getBody());
            }
        } catch (Exception e) {
            System.err.println("Error creating FreshDeskTicket. " + e.getMessage());
            System.err.println("Error creating FreshDeskTicket. " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Creates a Freshservice ticket for processing errors with detailed complaint information
     */
    public void openFreshserviceTicketForErrors(List<MozarcComplaintRequest> erroredComplaints,
                                               List<String> processingErrors,
                                               List<String> existingComplaints,
                                               File zipFile) {
        try {
            FreshserviceTicketCreateRequest request = buildTicketWithAttachmentForErrors(
                erroredComplaints, processingErrors, existingComplaints, zipFile);

            ResponseEntity<Object> response = freshserviceTicketService.createTicketWithAttachments(request);

            if (response.getStatusCode().is2xxSuccessful()) {
                System.out.println("Error ticket created with detailed complaint information.");
                logger.info("Freshservice ticket created for {} errored complaints and {} existing complaints",
                           erroredComplaints.size(), existingComplaints.size());
            } else {
                System.err.println("Error creating FreshDesk Error Ticket. Details: " + response.getBody());
            }
        } catch (Exception e) {
            System.err.println("Error creating FreshDesk Error Ticket. " + e.getMessage());
            e.printStackTrace();
        }
    }


    public  File getZipFileFromDirectory(String directoryPath) throws IOException {
        Path dir = Paths.get(directoryPath + "/Processing");

        if (!Files.exists(dir) || !Files.isDirectory(dir)) {
            throw new IllegalArgumentException("Path is not a valid directory: " + directoryPath);
        }

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(dir, "*.zip")) {
            for (Path entry : stream) {
                return entry.toFile(); // Returns the first .zip found
            }
        }

        throw new IOException("No ZIP files found in directory: " + directoryPath);
    }

    /**
     * Creates a minimal complaint request object for error reporting
     */
    private MozarcComplaintRequest createErrorComplaintRequest(String complaintId, String summary,
                                                              String reporterName, String sourceIntakeSystem,
                                                              String sourceIntakeUserName, String errorMessage,
                                                              int rowNumber) {
        try {
            // Create minimal objects with error information
            HowReported howReported = new HowReported("Error", "Processing Error");
            EventLocation eventLocation = new EventLocation("Unknown", "Error Location");
            CountryOfOrigin countryOfOrigin = new CountryOfOrigin("Unknown", "Error Country");
            CountryOfEvent countryOfEvent = new CountryOfEvent("Unknown", "Error Country");

            // Enhanced summary with error details
            String errorSummary = String.format("ERROR - Row %d: %s | Original Summary: %s | Error: %s",
                                               rowNumber, complaintId, summary, errorMessage);

            String complaintDate = LocalDateTime.now().toString();
            String oDataContext = "Containers('ERROR')";

            // Create minimal primary product (no manufacturing location for errors)
            PrimaryRelatedProduct primaryProduct = new PrimaryRelatedProduct(
                "Parts('ERROR')", false, false, 0, "ERROR",
                new UnitOfMeasure("ERROR"), null
            );

            // Create minimal person/location
            PrimaryRelatedPersonOrLocation prp = new PrimaryRelatedPersonOrLocation(
                "#ERROR", "PeopleOrPlaces('ERROR')", 0, new AgeUnits("ERROR"),
                "ERROR", false, new Gender("ERROR"), 0, new WeightUnits("ERROR")
            );

            return new MozarcComplaintRequest(
                "#ERROR", sourceIntakeSystem, reporterName, complaintId, sourceIntakeUserName,
                howReported, false, eventLocation, countryOfOrigin, countryOfEvent,
                "<EMAIL>", "000-000-0000", complaintDate, "Error",
                "Error", "Error", "Error", complaintDate, errorSummary,
                oDataContext, prp, new ArrayList<>(), primaryProduct, new ArrayList<>(), new ArrayList<>()
            );
        } catch (Exception e) {
            logger.error("Failed to create error complaint request: {}", e.getMessage());
            // Return a very basic error request
            return createBasicErrorComplaintRequest(complaintId, errorMessage, rowNumber);
        }
    }

    /**
     * Creates a very basic error complaint request when the full creation fails
     */
    private MozarcComplaintRequest createBasicErrorComplaintRequest(String complaintId, String errorMessage, int rowNumber) {
        String errorSummary = String.format("CRITICAL ERROR - Row %d: %s - %s", rowNumber, complaintId, errorMessage);

        return new MozarcComplaintRequest(
            "#ERROR", "ERROR_SYSTEM", "ERROR_REPORTER", complaintId, "ERROR_USER",
            new HowReported("Error", ""), false, new EventLocation("Error", ""),
            new CountryOfOrigin("Error", ""), new CountryOfEvent("Error", ""),
            "<EMAIL>", "000-000-0000", LocalDateTime.now().toString(),
            "Error", "Error", "Error", "Error", LocalDateTime.now().toString(),
            errorSummary, "Containers('ERROR')",
            new PrimaryRelatedPersonOrLocation("#ERROR", "PeopleOrPlaces('ERROR')", 0,
                new AgeUnits("ERROR"), "ERROR", false, new Gender("ERROR"), 0, new WeightUnits("ERROR")),
            new ArrayList<>(),
            new PrimaryRelatedProduct("Parts('ERROR')", false, false, 0, "ERROR",
                new UnitOfMeasure("ERROR"), null),
            new ArrayList<>(), new ArrayList<>()
        );
    }

    /**
     * Builds a detailed description for error tickets including complaint information
     */
    private String buildDetailedErrorDescription(List<MozarcComplaintRequest> erroredComplaints,
                                                List<String> processingErrors,
                                                List<String> existingComplaints) {
        StringBuilder description = new StringBuilder();

        description.append("<h3>Complaint Processing Errors Report</h3>");
        description.append("<p>The following issues were encountered during complaint processing:</p>");

        // Existing complaints section
        if (!existingComplaints.isEmpty()) {
            description.append("<h4>Existing Complaints (").append(existingComplaints.size()).append(")</h4>");
            description.append("<p>The following complaints already exist in Windchill:</p><ul>");
            for (String existing : existingComplaints) {
                description.append("<li>").append(existing).append("</li>");
            }
            description.append("</ul>");
        }

        // Processing errors section
        if (!processingErrors.isEmpty()) {
            description.append("<h4>Processing Errors (").append(processingErrors.size()).append(")</h4>");
            description.append("<p>The following errors occurred during processing:</p><ul>");
            for (String error : processingErrors) {
                description.append("<li>").append(error).append("</li>");
            }
            description.append("</ul>");
        }

        // Detailed complaint information
        if (!erroredComplaints.isEmpty()) {
            description.append("<h4>Errored Complaint Details (").append(erroredComplaints.size()).append(")</h4>");
            description.append("<table border='1' style='border-collapse: collapse; width: 100%;'>");
            description.append("<tr><th>Complaint ID</th><th>Reporter</th><th>System</th><th>Summary</th></tr>");

            for (MozarcComplaintRequest complaint : erroredComplaints) {
                description.append("<tr>");
                description.append("<td>").append(complaint.complaintId()).append("</td>");
                description.append("<td>").append(complaint.reporterName()).append("</td>");
                description.append("<td>").append(complaint.sourceIntakeSystem()).append("</td>");
                description.append("<td>").append(complaint.summary()).append("</td>");
                description.append("</tr>");
            }
            description.append("</table>");
        }

        description.append("<br/><p><strong>Action Required:</strong> Please review the attached ZIP file and resolve the issues listed above.</p>");

        return description.toString();
    }

    /**
     * Builds a Freshservice ticket request for errors with detailed complaint information
     */
    private FreshserviceTicketRequest buildErrorTicketRequest(List<MozarcComplaintRequest> erroredComplaints,
                                                             List<String> processingErrors,
                                                             List<String> existingComplaints) {
        Map<String, Object> customFields = new HashMap<>();
        customFields.put("lf_what_solution_is_having_an_issue", 33000031228L);

        String subject = String.format("Complaint Processing Errors - %d Errored, %d Existing",
                                      erroredComplaints.size(), existingComplaints.size());

        return new FreshserviceTicketRequest(
            "Windchill Integration",
            "<EMAIL>",
            subject,
            buildDetailedErrorDescription(erroredComplaints, processingErrors, existingComplaints),
            2, // Open status
            1, // High priority
            2, // Email source
            null,
            "Applications",
            "Troubleshooting",
            "Incident",
            1, // High impact
            1, // High urgency
            customFields
        );
    }

    /**
     * Builds a Freshservice ticket with attachment for errors
     */
    public FreshserviceTicketCreateRequest buildTicketWithAttachmentForErrors(List<MozarcComplaintRequest> erroredComplaints,
                                                                             List<String> processingErrors,
                                                                             List<String> existingComplaints,
                                                                             File zipFile) throws IOException {
        FreshserviceTicketRequest ticketRequest = buildErrorTicketRequest(erroredComplaints, processingErrors, existingComplaints);
        MultipartFile multipartZip = convertFileToMultipart(zipFile);
        return new FreshserviceTicketCreateRequest(ticketRequest, List.of(multipartZip));
    }

}