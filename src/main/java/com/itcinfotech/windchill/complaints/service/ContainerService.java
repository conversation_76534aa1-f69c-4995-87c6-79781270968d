package com.itcinfotech.windchill.complaints.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.itcinfotech.windchill.complaints.dto.*;
import com.itcinfotech.windchill.complaints.request.ComplaintRequest;
import com.itcinfotech.windchill.complaints.request.UploadRequestStage1;
import com.itcinfotech.windchill.complaints.response.ComplaintError;
import com.itcinfotech.windchill.complaints.response.ComplaintErrorResponse;
import com.itcinfotech.windchill.complaints.response.ComplaintResponse;
import com.itcinfotech.windchill.complaints.response.ContainerResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;

@Service
public class ContainerService {

    @Value("${windchill.host}")
    private String host;

    @Value("${windchill.container.url}")
    private String apiUrl;

    private final CsrfTokenService csrfTokenService;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final AuthenticationService authenticationService;

    @Autowired
    public ContainerService(RestTemplate restTemplate, CsrfTokenService csrfTokenService, ObjectMapper objectMapper, AuthenticationService authenticationService) {
        this.restTemplate = restTemplate;
        this.csrfTokenService = csrfTokenService;
        this.objectMapper = objectMapper;
        this.authenticationService = authenticationService;
    }

    public String getContainerID(String filter) throws JsonProcessingException {
        String token = csrfTokenService.getCsrfToken();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.set("Authorization", authenticationService.getAuthorizationHeader());
        headers.set("CSRF_NONCE", token);

        String url = host + apiUrl;
        String fields = "ID,Name";
        if (filter != null && !filter.isEmpty()) {
            url += "?$filter=Name eq '" + filter + "'&$select=" + fields;
        }

        HttpEntity<String> entity = new HttpEntity<>(null, headers);

        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

        ContainerResponse containerResponse = objectMapper.readValue(response.getBody(), ContainerResponse.class);

        if (containerResponse.value() != null && !containerResponse.value().isEmpty()) {
            return containerResponse.value().get(0).id();
        }

        throw new RuntimeException("Container not found with name = " + filter);
    }



}
