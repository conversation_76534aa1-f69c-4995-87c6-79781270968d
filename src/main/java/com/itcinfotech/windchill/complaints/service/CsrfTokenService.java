package com.itcinfotech.windchill.complaints.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;

@Service
public class CsrfTokenService {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Value("${windchill.host}")
    private String host;

    @Value("${windchill.token.url}")
    private String baseUrl;

    private final AuthenticationService authenticationService;

    public CsrfTokenService(RestTemplate restTemplate, ObjectMapper objectMapper, AuthenticationService authenticationService) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
        this.authenticationService = authenticationService;
    }

    public String getCsrfToken() {

        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", authenticationService.getAuthorizationHeader());
        HttpEntity<String> entity = new HttpEntity<>(headers);

        try {

            ResponseEntity<String> response = restTemplate.exchange(host+ baseUrl, HttpMethod.GET, entity, String.class);
            JsonNode responseBody = objectMapper.readTree(response.getBody());
            String nonceValue = responseBody.path("NonceValue").asText();
            return nonceValue;
        } catch (Exception e) {
            throw new RuntimeException("Error getting CSRF token: ", e);
        }
    }
}
