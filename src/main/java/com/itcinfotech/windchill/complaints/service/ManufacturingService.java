package com.itcinfotech.windchill.complaints.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.itcinfotech.windchill.complaints.response.ManufacturingResponse;
import com.itcinfotech.windchill.complaints.response.PartResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.Collections;

@Service
public class ManufacturingService {

    @Value("${windchill.host}")
    private String host;

    @Value("${windchill.manufacturing.url}")
    private String apiUrl;

    private final CsrfTokenService csrfTokenService;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final AuthenticationService authenticationService;

    @Autowired
    public ManufacturingService(RestTemplate restTemplate, CsrfTokenService csrfTokenService, ObjectMapper objectMapper, AuthenticationService authenticationService) {
        this.restTemplate = restTemplate;
        this.csrfTokenService = csrfTokenService;
        this.objectMapper = objectMapper;
        this.authenticationService = authenticationService;
    }

    public String getManufacturingLocationID(String filter) throws JsonProcessingException {
        String token = csrfTokenService.getCsrfToken();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.set("Authorization", authenticationService.getAuthorizationHeader());
        headers.set("CSRF_NONCE", token);

        String url = host + apiUrl;
        String fields = "ID,Name";

        if (filter != null && !filter.isEmpty()) {
            url += "?$filter=Name eq '" + filter + "'&$select=" + fields;
        }

        HttpEntity<String> entity = new HttpEntity<>(null, headers);

        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

        ManufacturingResponse manufacturingResponse = objectMapper.readValue(response.getBody(), ManufacturingResponse.class);

        if (manufacturingResponse.value() != null && !manufacturingResponse.value().isEmpty()) {
            return manufacturingResponse.value().get(0).placeId();
        }

        throw new RuntimeException("Manufacturing Location not found with Name = " + filter);

    }





}
