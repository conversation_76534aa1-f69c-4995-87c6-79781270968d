package com.itcinfotech.windchill.complaints.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.itcinfotech.windchill.complaints.dto.PrimaryRelatedProduct;
import com.itcinfotech.windchill.complaints.dto.UnitOfMeasure;
import com.itcinfotech.windchill.complaints.response.ContainerResponse;
import com.itcinfotech.windchill.complaints.response.PartResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;

@Service
public class PartService {

    @Value("${windchill.host}")
    private String host;

    @Value("${windchill.part.url}")
    private String apiUrl;

    private final CsrfTokenService csrfTokenService;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final AuthenticationService authenticationService;

    @Autowired
    private ManufacturingService manufacturingService;

    @Autowired
    public PartService(RestTemplate restTemplate, CsrfTokenService csrfTokenService, ObjectMapper objectMapper, AuthenticationService authenticationService) {
        this.restTemplate = restTemplate;
        this.csrfTokenService = csrfTokenService;
        this.objectMapper = objectMapper;
        this.authenticationService = authenticationService;
    }

    public List<PrimaryRelatedProduct> getAdditionalProducts (String[] additionalProducts) throws JsonProcessingException {

        List<PrimaryRelatedProduct> additionalProductsList = new ArrayList<>();
        for(int i=0; i< additionalProducts.length;i++){
            String[] product = additionalProducts[i].split(";");
            additionalProductsList.add
                    (new PrimaryRelatedProduct(
                            "Parts('" + getPartID(product[0]) + "')",
                    Boolean.valueOf(product[12]),
                    true,
                    Integer.valueOf(product[3]),
                    product[9],
                    new UnitOfMeasure(product[6]),
                    "Places('" + manufacturingService.getManufacturingLocationID(product[15]) + "')"
                    )
            );
        }
        return additionalProductsList;
    }

    public String getPartID(String filter) throws JsonProcessingException {
        String token = csrfTokenService.getCsrfToken();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.set("Authorization", authenticationService.getAuthorizationHeader());
        headers.set("CSRF_NONCE", token);

        String url = host + apiUrl;
        String fields = "ID,Number";
        if (filter != null && !filter.isEmpty()) {
            url += "?$filter=startswith(Name,'" + filter + "')&$select=" + fields;
        }

        HttpEntity<String> entity = new HttpEntity<>(null, headers);

        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

        PartResponse partResponse = objectMapper.readValue(response.getBody(), PartResponse.class);

        if (partResponse.value() != null && !partResponse.value().isEmpty()) {
            return partResponse.value().get(0).partId();
        }

        throw new RuntimeException("PartService ERROR: Part not found with NAME = " + filter);

    }





}
