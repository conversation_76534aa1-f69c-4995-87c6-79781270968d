package com.itcinfotech.windchill.complaints.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.itcinfotech.windchill.complaints.dto.AdditionalRelatedPersonnelOrLocations;
import com.itcinfotech.windchill.complaints.response.PeopleResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;

@Service
public class PeopleService {

    @Value("${windchill.host}")
    private String host;

    @Value("${windchill.person.url}")
    private String apiUrl;

    private final CsrfTokenService csrfTokenService;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final AuthenticationService authenticationService;

    @Autowired
    public PeopleService(RestTemplate restTemplate, CsrfTokenService csrfTokenService, ObjectMapper objectMapper, AuthenticationService authenticationService) {
        this.restTemplate = restTemplate;
        this.csrfTokenService = csrfTokenService;
        this.objectMapper = objectMapper;
        this.authenticationService = authenticationService;
    }

    public String getPatientID(String firstName, String lastName) throws JsonProcessingException {
        String token = csrfTokenService.getCsrfToken();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.set("Authorization", authenticationService.getAuthorizationHeader());
        headers.set("CSRF_NONCE", token);

        String url = host + apiUrl;
        String fields = "ID,Name,ComPtcmscloudUniquenessEmailAddress";

        if (firstName != null && !firstName.isEmpty() && lastName != null && !lastName.isEmpty()) {
            url += "?$filter=FirstName eq '" + firstName + "' and LastName eq '" + lastName + "'";
        }

        if (lastName == null || lastName.isEmpty()) {
            url += "?$filter=ComPtcmscloudUniquenessEmailAddress eq '" + firstName + "'";
        }
        url += "&$select=" + fields;

        HttpEntity<String> entity = new HttpEntity<>(null, headers);

        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

        PeopleResponse peopleResponse = objectMapper.readValue(response.getBody(), PeopleResponse.class);

        if (peopleResponse.value() != null && !peopleResponse.value().isEmpty()) {
            return peopleResponse.value().get(0).id();
        }

        throw new RuntimeException("People Service ERROR: People not found with FIRSTNAME or EMAIL = " + firstName);
    }


    public List<AdditionalRelatedPersonnelOrLocations> getAdditionalPeople (String name) throws JsonProcessingException {

        List<AdditionalRelatedPersonnelOrLocations> additionalPeopleList = new ArrayList<>();
        AdditionalRelatedPersonnelOrLocations additionalPerson = new AdditionalRelatedPersonnelOrLocations(
                "#PTC.QMS.InitialReporterContact",
                "PeopleOrPlaces('" + getPatientID(name, null) + "')"
        );
        additionalPeopleList.add(additionalPerson);

        return additionalPeopleList;
    }



}
