package com.itcinfotech.windchill.complaints.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.itcinfotech.windchill.complaints.dto.ContentInfoStage2;
import com.itcinfotech.windchill.complaints.dto.ContentInfoStage3;
import com.itcinfotech.windchill.complaints.dto.ContentValue;
import com.itcinfotech.windchill.complaints.request.ComplaintRequest;
import com.itcinfotech.windchill.complaints.request.UploadRequestStage1;
import com.itcinfotech.windchill.complaints.request.UploadRequestStage3;
import com.itcinfotech.windchill.complaints.response.*;
import com.itcinfotech.windchill.complaints.utils.CustomMultipartFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;





@Service
public class UploadService {

    @Value("${windchill.host}")
    private String host;

    @Value("${windchill.upload.stage1.url}")
    private String stage1Url;

    @Value("${windchill.upload.stage3.url}")
    private String stage3Url;



    private String stage1Action = "/PTC.CEM.UploadStage1Action";
    private String stage3Action = "/PTC.CEM.UploadStage3Action";

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final AuthenticationService authenticationService;
    private String mimeType;

    @Autowired
    public UploadService(RestTemplate restTemplate, ObjectMapper objectMapper, AuthenticationService authenticationService) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
        this.authenticationService = authenticationService;
    }

    public ResponseEntity<Object> uploadStage1(UploadRequestStage1 request, String token, String complaintId, String attachmentsDir, List<String> attachments) throws JsonProcessingException {

        String json = objectMapper.writeValueAsString(request);
        String oid = "('" + complaintId + "')";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.set("Authorization", authenticationService.getAuthorizationHeader());
        headers.set("CSRF_NONCE", token);

        HttpEntity<String> entity = new HttpEntity<>(json, headers);

        try {
            ResponseEntity<String> response = restTemplate.exchange(host + stage1Url + oid + stage1Action, HttpMethod.POST, entity, String.class);
            UploadResponseStage1 responseStage1 = objectMapper.readValue(response.getBody(), UploadResponseStage1.class);
            //System.out.println("responseStage1 = " + responseStage1);
            //System.out.println("\r\n\r\n\r\n");

            // Preparing the resquest for Stage 2
            //List<File> listFiles = createFilesList();
            List<File> listFiles = getFilesList(attachmentsDir,attachments);
            ContentValue contentValue = responseStage1.value().get(0);
            String masterURL = contentValue.masterUrl();
            String replicaURL = contentValue.replicaUrl();
            List<Integer> fileNames = contentValue.fileNames();

            //Collections.sort(fileNames);

            String cacheDescriptorArray = fileNames.stream()
                    .map(fileName -> String.join(":", Collections.nCopies(3, String.valueOf(fileName))))
                    .collect(Collectors.joining(";"));

            uploadStage2(replicaURL, masterURL, listFiles, cacheDescriptorArray, token, complaintId, attachmentsDir);


            return ResponseEntity.ok(responseStage1);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ComplaintErrorResponse(new ComplaintError("500", e.getMessage())));
        }
    }



    public ResponseEntity<Object> uploadStage2(String replicaURL, String masterURL, List<File> files, String cacheDescriptorArray, String token, String complaintId, String attachmentsDir) throws Exception {

        //System.out.println("--------Stage 2 : " + cacheDescriptorArray);

        String boundary = "----boundary";

        StringBuilder requestBody = new StringBuilder();

        List<String> fileNames = new ArrayList<>();
        List<Map<String, String>> fileDetails = new ArrayList<>();

        // Create the connection
        HttpURLConnection conn = (HttpURLConnection) new URL(replicaURL).openConnection();
        conn.setDoOutput(true);
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);
        conn.setRequestProperty("CSRF_NONCE", token);
        conn.setRequestProperty("Authorization", authenticationService.getAuthorizationHeader());
        conn.setConnectTimeout(0);
        conn.setReadTimeout(0);

        // Open the OutputStream
        try (OutputStream out = conn.getOutputStream()) {
            // Write headers for Master URL and CacheDescriptor_array using StringBuilder
            requestBody.append("--").append(boundary).append("\r\n");
            requestBody.append("Content-Disposition: form-data; name=\"Master_URL\"\r\n");
            requestBody.append("Content-Type: text/plain; charset=UTF-8\r\n");
            requestBody.append("\r\n");
            requestBody.append(masterURL).append("\r\n");
            out.write(requestBody.toString().getBytes());
            requestBody.setLength(0);  // Reset StringBuilder

            requestBody.append("--").append(boundary).append("\r\n");
            requestBody.append("Content-Disposition: form-data; name=\"CacheDescriptor_array\"\r\n");
            requestBody.append("Content-Type: text/plain; charset=UTF-8\r\n");
            requestBody.append("\r\n");
            requestBody.append(cacheDescriptorArray).append("\r\n");
            out.write(requestBody.toString().getBytes());
            requestBody.setLength(0);  // Reset StringBuilder

            // Call getFiles to write the files using OutputStream
            String[] cacheDescriptorValues = cacheDescriptorArray.split(";");

            getFiles(files, out, boundary, cacheDescriptorValues, fileNames,fileDetails);  // Using OutputStream here

            // End boundary
            out.write(("--" + boundary + "--\r\n").getBytes());
        }

        // Get the response from the server
        StringBuilder response = getStringBuilder(conn);
        conn.disconnect();

        // Preparing for Stage 3
        String uploadStage3Url = host + stage3Url;
        String stage2Response = response.toString();
        //System.out.println(" stage2Response : " + stage2Response);
        //System.out.println("\r\n\r\n\r\n");

        uploadStage3(uploadStage3Url, files, fileNames, token, stage2Response, complaintId, fileDetails);

        return null;
    }


    public ResponseEntity<UploadResponseStage3> uploadStage3(String uploadStage3Url, List<File> files, List<String> fileNames, String token, String stage2Response, String complaintId, List<Map<String, String>> fileDetails) throws IOException {

        String oid = "('" + complaintId + "')";
        String url = uploadStage3Url  + oid + stage3Action;

        // Parse the response from Stage2 to get contentInfos
        ObjectMapper objectMapper = new ObjectMapper();
        UploadResponseStage2 stage2ResponseObj = objectMapper.readValue(stage2Response, UploadResponseStage2.class);

        List<ContentInfoStage2> contentInfos = stage2ResponseObj.contentInfos();
        contentInfos.sort(Comparator.comparingInt(ContentInfoStage2::streamId));

        List<ContentInfoStage3> contentInfoStage3List = new ArrayList<>();

        for (ContentInfoStage2 contentInfoStage2 : contentInfos) {
            // Agora a lista está ordenada, então você pode usar streamId, fileSize e encodedInfo diretamente
            Integer streamId = contentInfoStage2.streamId();
            Integer fileSize = contentInfoStage2.fileSize();
            String encodedInfo = contentInfoStage2.encodedInfo();

            String fileName = fileDetails.stream()
                    .filter(detail -> detail.get("streamId").equals(streamId.toString()))  // Filtra pelo streamId
                    .map(detail -> detail.get("fileName"))  // Mapeia para o fileName
                    .findFirst()
                    .orElse("defaultFileName.txt");

            String mimeType = getMimeType(fileName);
            Boolean primaryContent = false;  // Você pode definir isso como desejar

            // Adiciona o conteúdo para Stage3
            contentInfoStage3List.add(new ContentInfoStage3(
                    streamId, fileSize, encodedInfo, fileName, mimeType, primaryContent
            ));
        }



        // Create the request body for Stage3
        //contentInfo.sort(Comparator.comparingInt(ContentInfoStage3::streamId));
        UploadRequestStage3 uploadRequestStage3 = new UploadRequestStage3(contentInfoStage3List);

        String jsonRequestBody = objectMapper.writeValueAsString(uploadRequestStage3);

        //System.out.println("REQUEST Stage3 : " + jsonRequestBody.toString());
        //System.out.println("\r\n\r\n\r\n");

        HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
        conn.setDoOutput(true);
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", "application/json");
        conn.setRequestProperty("Accept", "application/json");
        conn.setRequestProperty("CSRF_NONCE", token);
        conn.setRequestProperty("Authorization", authenticationService.getAuthorizationHeader());
        conn.setConnectTimeout(0);
        conn.setReadTimeout(0);

        try (OutputStream out = conn.getOutputStream()) {
            byte[] input = jsonRequestBody.getBytes("UTF-8");
            out.write(input, 0, input.length);
        }

        // Read response
        StringBuilder response = new StringBuilder();
        try (BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
            String line;
            while ((line = in.readLine()) != null) {
                response.append(line);
            }
        } catch (IOException e) {
            // Handle error response
            try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(conn.getErrorStream()))) {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    response.append(line);
                }
            }
        }

        //System.out.println(" RESPONSE Stage3 : " + response.toString());
        //System.out.println("\r\n\r\n\r\n");
        conn.disconnect();


        return null;
    }






    private static StringBuilder getStringBuilder(HttpURLConnection conn) throws IOException {
        StringBuilder response = new StringBuilder();
        try (BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
            String line;
            while ((line = in.readLine()) != null) {
                response.append(line);
            }
        } catch (IOException e) {

            try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(conn.getErrorStream()))) {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    response.append(line);
                }
            }
        }
        return response;
    }




    private void getFiles(List<File> files, OutputStream out, String boundary, String[] cacheDescriptorValues, List<String> fileNames, List<Map<String, String>> fileDetails) throws IOException {


        for (int i = 0; i < files.size(); i++) {
            String fieldName = cacheDescriptorValues[i].split(":")[0];

            long fileSize = files.get(i).length();

            // Write headers for the file part using OutputStream
            String header = "--" + boundary + "\r\n" +
                    "Content-Disposition: form-data; name=\"" + fieldName
                    + "\"; filename=\"" + files.get(i).getName() + " "
                    + "\"; filesize=\"" + fileSize + "\"\r\n" +
                    "Content-Type: " + getMimeType(files.get(i).getName()) + "\r\n" +
                    "\r\n";
            out.write(header.getBytes());

            // Read and write file content in binary
            byte[] fileBytes = Files.readAllBytes(files.get(i).toPath());
            out.write(fileBytes);

            // End of file part
            out.write("\r\n".getBytes());

            // Add the file name to the list for use in Stage 3
//            fileNames.add(files.get(i).getName());

            Map<String, String> fileDetail = new HashMap<>();
            fileDetail.put("streamId", fieldName);  // streamId
            fileDetail.put("fileName", files.get(i).getName());
            fileDetails.add(fileDetail);

            //System.out.println("File: " + files.get(i).getName() + " , Size: " + fileSize + " , StreamID: " + fieldName);

        }
    }


    private MultipartFile createMultipartFile(File file) throws IOException {
        // Read the file as byte[]
        Path path = Paths.get(file.getAbsolutePath());
        String mimeType = Files.probeContentType(path);
        byte[] fileContent = Files.readAllBytes(path);

        // Create the MultipartFile manually
        return new CustomMultipartFile(file.getName(), mimeType != null ? mimeType : "application/octet-stream", fileContent);
    }



    private String getBoundary() {
        return "--boundary";
    }

    private String getFirstBoundary() {
        return "----boundary";
    }

    private void writeFormField(DataOutputStream outputStream, String name, String value) throws IOException {
        if(name.equals("Master_URL")){
            outputStream.writeBytes(getFirstBoundary() + "\r\n");
        } else{
            outputStream.writeBytes(getBoundary() + "\r\n");
        }
        outputStream.writeBytes("Content-Disposition: form-data; name=\"" + name + "\"\r\n");
        outputStream.writeBytes("\r\n");
        outputStream.writeBytes(value + "\r\n");
        //outputStream.writeBytes("\r\n");
        //System.out.println("Writing form field: " + name + " = " + value);


    }

    private void writeFileField2(DataOutputStream outputStream, String fieldName, File file) throws IOException {
        // Escreve a boundary
        outputStream.writeBytes(getBoundary() + "\r\n");
        //System.out.println("Writing file: " + file.getName() + " with size: " + file.length());

        long contentLength = file.length();

        // Write the header part for the file
        outputStream.writeBytes("Content-Disposition: form-data; name=\"" + fieldName + "\"; filename=\"" + file.getName() + "\"; filesize=\"" + contentLength + "\"\r\n");
//        outputStream.writeBytes("Content-Type: " + getMimeType(file.getName()) + "\r\n");
        outputStream.writeBytes("Content-Type: mime/type" + "\r\n");
//        outputStream.writeBytes("Content-Length: " + String.valueOf(contentLength) + "\r\n");

        // Mandatory blank line after header
        outputStream.writeBytes("\r\n");

        // Now, insert the binary content of the file
        try (FileInputStream fileInputStream = new FileInputStream(file)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }

        // End line for the file field
        outputStream.writeBytes("\r\n");
    }

    private void writeFileField(DataOutputStream outputStream, String fieldName, File file) throws IOException {
        // Write the boundary
        outputStream.writeBytes(getBoundary() + "\r\n");
        //System.out.println("Writing file: " + file.getName() + " with size: " + file.length());

        long contentLength = file.length();

        // Write the header part for the file
        outputStream.writeBytes("Content-Disposition: form-data; name=\"" + fieldName + "\"; filename=\"" + file.getName() + "\"; filesize=\"" + contentLength + "\"\r\n");
//        outputStream.writeBytes("Content-Type: " + getMimeType(file.getName()) + "\r\n");
        outputStream.writeBytes("Content-Type: mime/type" + "\r\n");
//        outputStream.writeBytes("Content-Length: " + String.valueOf(contentLength) + "\r\n");

        // Mandatory blank line after header
        outputStream.writeBytes("\r\n");

        // Now, insert the binary content of the file
        try (FileInputStream fileInputStream = new FileInputStream(file)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }

        // End line for the file field
        outputStream.writeBytes("\r\n");
    }

    private String getMimeType(String fileName) {
        try {
            Path path = Paths.get(fileName);
            String mimeType = Files.probeContentType(path);
            return mimeType != null ? mimeType : "application/octet-stream";
        } catch (IOException e) {
            return "application/octet-stream";
        }
    }


    private List<File> createFilesList() {
        // Logic to create the file list here.

        String filePath1 = "C:/temp/files/Document1-TXT.txt";
        String filePath2 = "C:/temp/files/Document2-PDF.pdf";

        File file1 = new File(filePath1);
        File file2 = new File(filePath2);

        return List.of(file1, file2);
    }

    private List<File> getFilesList(String rootDir, List<String> fileList) {
        List<File> files = new ArrayList<>();

        for (String fileName : fileList) {
            File file = new File(rootDir, fileName);
            if (file.exists()) {
                files.add(file);
            } else {
                System.out.println("File not found: " + file.getAbsolutePath());
            }
        }

        return files;
    }

}
