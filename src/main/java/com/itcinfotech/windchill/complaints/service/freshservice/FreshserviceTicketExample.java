package com.itcinfotech.windchill.complaints.service.freshservice;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest;
import com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketResponse;
import com.itcinfotech.windchill.complaints.request.FreshserviceTicketCreateRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Example class demonstrating how to use the FreshserviceTicketService
 * This is for demonstration purposes only and should be adapted to your specific use case
 */
@Component
public class FreshserviceTicketExample {

    private static final Logger logger = LoggerFactory.getLogger(FreshserviceTicketExample.class);
    
    private final FreshserviceTicketService freshserviceTicketService;
    private final RestTemplate restTemplate;
    
    @Autowired
    public FreshserviceTicketExample(FreshserviceTicketService freshserviceTicketService, RestTemplate restTemplate) {
        this.freshserviceTicketService = freshserviceTicketService;
        this.restTemplate = restTemplate;
    }
    
    /**
     * Example method to create a ticket in Freshservice
     * 
     * @return The created ticket response
     */
    public FreshserviceTicketResponse createExampleTicket() {
        try {
            // Create a direct JSON string with required fields including custom fields
            String ticketJson = "{\n" +
                "  \"name\": \"Test User\",\n" +
                "  \"email\": \"<EMAIL>\",\n" +
                "  \"subject\": \"Test Ticket Request\",\n" +
                "  \"description\": \"This is a test ticket created for testing purposes.\",\n" +
                "  \"status\": 2,\n" +
                "  \"priority\": 1,\n" +
                "  \"source\": 2,\n" +
                "  \"category\": \"Applications\",\n" +
                "  \"sub_category\": \"Troubleshooting\",\n" +
                "  \"type\": \"Incident\",\n" +
                "  \"impact\": 1,\n" +
                "  \"urgency\": 1,\n" +
                "  \"custom_fields\": {\n" +
                "    \"lf_what_solution_is_having_an_issue\": 33000031228\n" +
                "  }\n" +
                "}";
            
            // Make a direct REST call to create the ticket
            HttpHeaders headers = new HttpHeaders();
            String auth = freshserviceTicketService.getApiKey() + ":X";
            byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes());
            String authHeader = "Basic " + new String(encodedAuth);
            headers.set("Authorization", authHeader);
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<String> entity = new HttpEntity<>(ticketJson, headers);
            
            ResponseEntity<String> response = restTemplate.exchange(
                freshserviceTicketService.getApiUrl() + "/tickets",
                HttpMethod.POST,
                entity,
                String.class
            );
            
            // Check if the ticket was created successfully
            if (response.getStatusCode().is2xxSuccessful()) {
                // Parse the response
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode rootNode = objectMapper.readTree(response.getBody());
                JsonNode ticketNode = rootNode.get("ticket");
                
                if (ticketNode != null && ticketNode.has("id")) {
                    Long ticketId = ticketNode.get("id").asLong();
                    
                    // Create a minimal ticket response object
                    FreshserviceTicketResponse ticketResponse = new FreshserviceTicketResponse();
                    ticketResponse.setId(ticketId);
                    ticketResponse.setSubject("Test Ticket Request");
                    
                    logger.info("Successfully created ticket with ID: {}", ticketId);
                    return ticketResponse;
                } else {
                    logger.error("Failed to parse ticket response: {}", response.getBody());
                    return null;
                }
            } else {
                logger.error("Failed to create ticket: {}", response.getBody());
                return null;
            }
        } catch (Exception e) {
            logger.error("Failed to create ticket: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Example method to create a ticket with attachments in Freshservice
     * 
     * @param attachments List of file attachments
     * @return The created ticket response
     */
    public FreshserviceTicketResponse createExampleTicketWithAttachments(List<MultipartFile> attachments) {
        // Create a ticket request with sample data
        FreshserviceTicketRequest ticketRequest = createSampleTicketRequest();
        
        // Create a request with ticket data and attachments
        FreshserviceTicketCreateRequest request = new FreshserviceTicketCreateRequest(ticketRequest, attachments);
        
        try {
            // Call the service to create the ticket with attachments
            ResponseEntity<Object> response = freshserviceTicketService.createTicketWithAttachments(request);
            
            // Check if the ticket was created successfully
            if (response.getStatusCode().is2xxSuccessful()) {
                FreshserviceTicketResponse ticketResponse = (FreshserviceTicketResponse) response.getBody();
                
                logger.info("Successfully created ticket with ID: {} and attachments", 
                    ticketResponse.getId());
                return ticketResponse;
            } else {
                logger.error("Failed to create ticket with attachments: {}", response.getBody());
                return null;
            }
        } catch (Exception e) {
            logger.error("Failed to create ticket with attachments: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Example method to create a ticket with attachments in a single API call
     * 
     * @param attachments List of file attachments
     * @return The created ticket response
     */
    public FreshserviceTicketResponse createExampleTicketWithAttachmentsInSingleCall(List<MultipartFile> attachments) {
        // Create a ticket request with sample data
        FreshserviceTicketRequest ticketRequest = createSampleTicketRequest();
        
        // Create a request with ticket data and attachments
        FreshserviceTicketCreateRequest request = new FreshserviceTicketCreateRequest(ticketRequest, attachments);
        
        try {
            // Call the service to create the ticket with attachments in a single call
            ResponseEntity<Object> response = freshserviceTicketService.createTicketWithAttachmentsInSingleCall(request);
            
            // Check if the ticket was created successfully
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() instanceof FreshserviceTicketResponse) {
                FreshserviceTicketResponse ticketResponse = (FreshserviceTicketResponse) response.getBody();
                
                logger.info("Successfully created ticket with ID: {} and attachments in a single call", 
                    ticketResponse.getId());
                return ticketResponse;
            } else {
                logger.error("Failed to create ticket with attachments in a single call: {}", response.getBody());
                return null;
            }
        } catch (Exception e) {
            logger.error("Failed to create ticket with attachments in a single call: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Creates a sample ticket request with predefined values
     * 
     * @return A sample ticket request
     */
    public FreshserviceTicketRequest createSampleTicketRequest() {
        // Create custom fields map with the required field
        Map<String, Object> customFields = new HashMap<>();
        customFields.put("lf_what_solution_is_having_an_issue", 33000031228L);
        
        return new FreshserviceTicketRequest(
            "Test User",
            "<EMAIL>",
            "Test Ticket Request",
            "This is a test ticket created for testing purposes.",
            2, // Open status
            1, // High priority
            2, // Email source
            null,
            "Applications",
            "Troubleshooting",
            "Incident",
            1,
            1,
            customFields
            );
    }
}
