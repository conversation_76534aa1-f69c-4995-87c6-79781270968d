package com.itcinfotech.windchill.complaints.service.freshservice;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.itcinfotech.windchill.complaints.dto.freshservice.*;
import com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest;
import com.itcinfotech.windchill.complaints.request.FreshserviceTicketCreateRequest;
import com.itcinfotech.windchill.complaints.response.ComplaintError;
import com.itcinfotech.windchill.complaints.response.ComplaintErrorResponse;
import com.itcinfotech.windchill.complaints.utils.FreshserviceFileUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;

@Service
public class FreshserviceTicketService {

    private static final Logger logger = LoggerFactory.getLogger(FreshserviceTicketService.class);

    @Value("${freshservice.api.url}")
    private String apiUrl;

    @Value("${freshservice.api.key}")
    private String apiKey;

    @Value("${freshservice.attachments.max.size}")
    private long maxAttachmentSize;

    @Value("${freshservice.attachments.allowed.extensions}")
    private String allowedExtensions;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final Path tempAttachmentDirectory;

    @Autowired
    public FreshserviceTicketService(RestTemplate restTemplate, ObjectMapper objectMapper, Path tempAttachmentDirectory) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
        this.tempAttachmentDirectory = tempAttachmentDirectory;
    }

    /**
     * Creates a ticket in Freshservice
     * 
     * @param request The ticket creation request containing ticket details
     * @return ResponseEntity with the created ticket or error response
     */
    public ResponseEntity<Object> createTicket(FreshserviceTicketRequest request) {
        try {

            String json = objectMapper.writeValueAsString(request);
            // Create a minimal JSON object with only the required fields
            // This avoids any issues with Jackson serialization including unwanted fields
//            String json = "{\n" +
//                "  \"name\": \"" + request.name() + "\",\n" +
//                "  \"email\": \"" + request.email() + "\",\n" +
//                "  \"subject\": \"" + request.subject() + "\",\n" +
//                "  \"description\": \"" + request.description() + "\",\n" +
//                "  \"status\": " + request.status() + ",\n" +
//                "  \"priority\": " + request.priority() + ",\n" +
//                "  \"source\": " + request.source() + ",\n" +
//                "  \"category\": \"" + request.category() + "\",\n" +
//                "  \"sub_category\": \"" + request.subCategory() + "\",\n" +
//                "  \"type\": \"" + request.type() + "\",\n" +
//                "  \"impact\": " + request.impact() + ",\n" +
//                "  \"urgency\": " + request.urgency() + "\n" +
//                "}";
            
            logger.info("Creating Freshservice ticket with request: {}", json);
            
            HttpHeaders headers = createHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<>(json, headers);
            
            try {
                ResponseEntity<String> response = restTemplate.exchange(
                    apiUrl + "/tickets", 
                    HttpMethod.POST, 
                    entity, 
                    String.class
                );
                
                logger.info("Received response from Freshservice API: {}", response.getBody());
                
                if (response.getStatusCode().is2xxSuccessful()) {
                    try {
                        FreshserviceResponse<FreshserviceTicketResponse> freshserviceResponse = 
                            objectMapper.readValue(response.getBody(), 
                                objectMapper.getTypeFactory().constructParametricType(
                                    FreshserviceResponse.class, FreshserviceTicketResponse.class));
                        
                        if (freshserviceResponse != null && freshserviceResponse.getTicket() != null) {
                            logger.info("Successfully created ticket with ID: {}", 
                                freshserviceResponse.getTicket().getId());
                            return ResponseEntity.ok(freshserviceResponse.getTicket());
                        } else {
                            logger.error("Failed to parse ticket response: {}", response.getBody());
                            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body(new ComplaintErrorResponse(new ComplaintError("500", 
                                    "Failed to parse ticket response")));
                        }
                    } catch (Exception e) {
                        logger.error("Error parsing ticket response: {}", e.getMessage(), e);
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                            .body(new ComplaintErrorResponse(new ComplaintError("500", 
                                "Error parsing ticket response: " + e.getMessage())));
                    }
                } else {
                    logger.error("Failed to create ticket: {}", response.getBody());
                    return ResponseEntity.status(response.getStatusCode())
                        .body(new ComplaintErrorResponse(new ComplaintError(
                            String.valueOf(response.getStatusCodeValue()), 
                            "Failed to create ticket: " + response.getBody())));
                }
            } catch (RestClientException e) {
                logger.error("Error connecting to Freshservice API: {}", e.getMessage(), e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ComplaintErrorResponse(new ComplaintError("500", 
                        "Error connecting to Freshservice API: " + e.getMessage())));
            }
        } catch (Exception e) {
            logger.error("Error creating Freshservice ticket", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ComplaintErrorResponse(new ComplaintError("500", e.getMessage())));
        }
    }
    
    /**
     * Creates a ticket in Freshservice with attachments
     * 
     * @param request The ticket creation request containing ticket details and attachments
     * @return ResponseEntity with the created ticket or error response
     */
    public ResponseEntity<Object> createTicketWithAttachments(FreshserviceTicketCreateRequest request) {
        try {
            // First create the ticket
            ResponseEntity<Object> ticketResponse = createTicket(request.getTicketRequest());
            
            if (ticketResponse.getStatusCode() != HttpStatus.OK) {
                return ticketResponse;
            }
            
            // Extract ticket ID from response
            FreshserviceTicketResponse freshserviceResponse = (FreshserviceTicketResponse) ticketResponse.getBody();
            Long ticketId = freshserviceResponse.getId();
            
            // If there are attachments, add them to the ticket
            if (request.getAttachments() != null && !request.getAttachments().isEmpty()) {
                return addAttachmentsToTicket(ticketId, request.getAttachments());
            }
            
            return ticketResponse;
        } catch (Exception e) {
            logger.error("Error creating Freshservice ticket with attachments", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ComplaintErrorResponse(new ComplaintError("500", e.getMessage())));
        }
    }
    
    /**
     * Adds attachments to an existing Freshservice ticket
     * 
     * @param ticketId The ID of the ticket to add attachments to
     * @param attachments List of attachments to add
     * @return ResponseEntity with the attachment response or error response
     */
    public ResponseEntity<Object> addAttachmentsToTicket(Long ticketId, List<MultipartFile> attachments) {
        List<File> tempFiles = null;
        try {
            if (attachments == null || attachments.isEmpty()) {
                logger.warn("No attachments provided for ticket ID: {}", ticketId);
                return ResponseEntity.ok().body("No attachments to add");
            }
            
            logger.info("Processing {} attachments for ticket ID: {}", attachments.size(), ticketId);
            
            // Log attachment details for debugging
            for (MultipartFile file : attachments) {
                logger.debug("Attachment: name={}, size={}, contentType={}", 
                    file.getOriginalFilename(), file.getSize(), file.getContentType());
            }
            
            // Validate attachments
            validateAttachments(attachments);
            
            // Convert MultipartFile objects to File objects
            tempFiles = FreshserviceFileUtils.convertMultipartFilesToFiles(
                attachments, 
                tempAttachmentDirectory.toString()
            );
            
            logger.info("Created {} temporary files for attachments", tempFiles.size());
            
            // Use RestTemplate to upload attachments
            ResponseEntity<Object> response = uploadAttachmentsWithRestTemplate(ticketId, tempFiles);
            
            // Log the response for debugging
            if (response.getStatusCode().is2xxSuccessful()) {
                logger.info("Successfully added attachments to ticket ID: {}", ticketId);
            } else {
                logger.error("Failed to add attachments to ticket ID: {}: {}", 
                    ticketId, response.getBody());
            }
            
            return response;
        } catch (IllegalArgumentException e) {
            logger.error("Validation error for attachments: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(new ComplaintErrorResponse(new ComplaintError("400", e.getMessage())));
        } catch (Exception e) {
            logger.error("Error adding attachments to Freshservice ticket", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ComplaintErrorResponse(new ComplaintError("500", e.getMessage())));
        } finally {
            // Clean up temporary files
            if (tempFiles != null) {
                FreshserviceFileUtils.cleanupTempFiles(tempFiles);
                logger.debug("Cleaned up {} temporary files", tempFiles.size());
            }
        }
    }
    
    /**
     * Uploads attachments to Freshservice using RestTemplate and HttpEntity
     * 
     * @param ticketId The ID of the ticket to add attachments to
     * @param tempFiles List of temporary files to upload
     * @return ResponseEntity with the attachment response or error response
     */
    private ResponseEntity<Object> uploadAttachmentsWithRestTemplate(Long ticketId, List<File> tempFiles) {
        try {
            // Create MultiValueMap for the multipart request
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            
            // Add body parameter (required for the notes endpoint)
            body.add("body", "Attachment added to ticket");
            
            // Add each file as a form field
            for (File file : tempFiles) {
                try {
                    // Read file content
                    byte[] fileContent = Files.readAllBytes(file.toPath());
                    ByteArrayResource resource = new ByteArrayResource(fileContent) {
                        @Override
                        public String getFilename() {
                            return file.getName();
                        }
                    };
                    
                    // Add file to the request with the correct parameter name for Freshdesk API
                    body.add("attachments[]", resource);
                } catch (IOException e) {
                    logger.error("Error reading file content: {}", file.getName(), e);
                    throw new RuntimeException("Error reading file content: " + e.getMessage(), e);
                }
            }
            
            // Create headers with authentication
            HttpHeaders headers = createHeaders();
            // Set content type to multipart/form-data
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            
            // Create HttpEntity with the body and headers
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
            
            // Build the URL for the notes endpoint
            String url = apiUrl + "/tickets/" + ticketId + "/notes";
            
            logger.info("Uploading attachments to Freshservice ticket with ID: {}", ticketId);
            
            // Make the API call using RestTemplate
            ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String.class
            );
            
            // Log the response
            logger.info("Freshservice API response status: {}", response.getStatusCode());
            logger.debug("Freshservice API response body: {}", response.getBody());
            
            try {
                if (response.getBody() == null || response.getBody().isEmpty()) {
                    // Handle empty response
                    Map<String, Object> result = new HashMap<>();
                    result.put("status", "success");
                    result.put("message", "Attachments added successfully, but response was empty");
                    return ResponseEntity.ok(result);
                }
                
                // Parse the response - handle the conversation structure
                JsonNode rootNode = objectMapper.readTree(response.getBody());
                JsonNode conversationNode = rootNode.get("conversation");
                
                if (conversationNode != null && conversationNode.has("attachments")) {
                    // Extract attachments from the conversation node
                    JsonNode attachmentsNode = conversationNode.get("attachments");
                    
                    if (attachmentsNode.isArray()) {
                        List<FreshserviceAttachmentResponse> attachments = new ArrayList<>();
                        
                        for (JsonNode attachmentNode : attachmentsNode) {
                            FreshserviceAttachmentResponse attachment = objectMapper.treeToValue(
                                attachmentNode, FreshserviceAttachmentResponse.class);
                            attachments.add(attachment);
                        }
                        
                        FreshserviceAttachmentResponseWrapper wrapper = new FreshserviceAttachmentResponseWrapper(attachments);
                        logger.info("Successfully added {} attachments to Freshservice ticket with ID: {}", 
                            attachments.size(), ticketId);
                        
                        return ResponseEntity.ok(wrapper);
                    }
                }
                
                // If we couldn't parse the attachments from the conversation, return a generic success response
                Map<String, Object> result = new HashMap<>();
                result.put("status", "success");
                result.put("message", "Attachments added successfully, but could not extract attachment details");
                result.put("rawResponse", response.getBody());
                
                return ResponseEntity.ok(result);
            } catch (Exception e) {
                logger.error("Error parsing attachment response: {}", e.getMessage(), e);
                
                // Create a structured response even when parsing fails
                Map<String, Object> result = new HashMap<>();
                result.put("status", "success");
                result.put("message", "Attachments added successfully, but could not parse response");
                result.put("rawResponse", response.getBody());
                result.put("error", e.getMessage());
                
                return ResponseEntity.ok(result);
            }
        } catch (Exception e) {
            logger.error("Error uploading attachments: {}", e.getMessage(), e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", "error");
            errorResponse.put("message", "Error uploading attachments: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * Creates a ticket in Freshservice with attachments in a single API call
     * 
     * @param request The ticket creation request containing ticket details and attachments
     * @return ResponseEntity with the created ticket or error response
     */
    public ResponseEntity<Object> createTicketWithAttachmentsInSingleCall(FreshserviceTicketCreateRequest request) {
        List<File> tempFiles = null;
        try {
            // Sanitize the request to remove problematic fields
            if (request.getTicketRequest().customFields() != null) {
                sanitizeCustomFields(request.getTicketRequest().customFields());
            }
            
            // Validate attachments if present
            if (request.getAttachments() != null && !request.getAttachments().isEmpty()) {
                validateAttachments(request.getAttachments());
                
                // Convert MultipartFile objects to File objects
                tempFiles = FreshserviceFileUtils.convertMultipartFilesToFiles(
                    request.getAttachments(), 
                    tempAttachmentDirectory.toString()
                );
            }
            
            // Create MultiValueMap for the multipart request
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            
            // Add ticket fields from the request
            FreshserviceTicketRequest ticketRequest = request.getTicketRequest();
            body.add("email", ticketRequest.email());
            body.add("subject", ticketRequest.subject());
            body.add("description", ticketRequest.description());
            body.add("status", String.valueOf(ticketRequest.status()));
            body.add("priority", String.valueOf(ticketRequest.priority()));
            body.add("source", String.valueOf(ticketRequest.source()));
            
            if (ticketRequest.category() != null) {
                body.add("category", ticketRequest.category());
            }
            
            if (ticketRequest.subCategory() != null) {
                body.add("sub_category", ticketRequest.subCategory());
            }
            
            if (ticketRequest.type() != null) {
                body.add("type", ticketRequest.type());
            }
            
            if (ticketRequest.impact() != null) {
                body.add("impact", String.valueOf(ticketRequest.impact()));
            }
            
            if (ticketRequest.urgency() != null) {
                body.add("urgency", String.valueOf(ticketRequest.urgency()));
            }
            
            // Add attachments if present
            if (tempFiles != null && !tempFiles.isEmpty()) {
                for (File file : tempFiles) {
                    try {
                        // Read file content
                        byte[] fileContent = Files.readAllBytes(file.toPath());
                        ByteArrayResource resource = new ByteArrayResource(fileContent) {
                            @Override
                            public String getFilename() {
                                return file.getName();
                            }
                        };
                        
                        // Add file to the request with the correct parameter name for Freshdesk API
                        body.add("attachments[]", resource);
                    } catch (IOException e) {
                        logger.error("Error reading file content: {}", file.getName(), e);
                        throw new RuntimeException("Error reading file content: " + e.getMessage(), e);
                    }
                }
            }
            
            // Create headers with authentication
            HttpHeaders headers = createHeaders();
            // Set content type to multipart/form-data
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            
            // Create HttpEntity with the body and headers
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
            
            // Build the URL for the tickets endpoint
            String url = apiUrl + "/tickets";
            
            logger.info("Creating Freshservice ticket with attachments in a single API call");
            
            // Make the API call using RestTemplate
            ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String.class
            );
            
            logger.info("Received response from Freshservice API: {}", response.getBody());
            
            if (response.getStatusCode().is2xxSuccessful()) {
                try {
                    FreshserviceResponse<FreshserviceTicketResponse> freshserviceResponse = 
                        objectMapper.readValue(response.getBody(), 
                            objectMapper.getTypeFactory().constructParametricType(
                                FreshserviceResponse.class, FreshserviceTicketResponse.class));
                    
                    if (freshserviceResponse != null && freshserviceResponse.getTicket() != null) {
                        logger.info("Successfully created ticket with ID: {} and attachments in a single call", 
                            freshserviceResponse.getTicket().getId());
                        return ResponseEntity.ok(freshserviceResponse.getTicket());
                    } else {
                        logger.error("Failed to parse ticket response: {}", response.getBody());
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                            .body(new ComplaintErrorResponse(new ComplaintError("500", 
                                "Failed to parse ticket response")));
                    }
                } catch (Exception e) {
                    logger.error("Error parsing ticket response: {}", e.getMessage(), e);
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new ComplaintErrorResponse(new ComplaintError("500", 
                            "Error parsing ticket response: " + e.getMessage())));
                }
            } else {
                logger.error("Failed to create ticket with attachments: {}", response.getBody());
                return ResponseEntity.status(response.getStatusCode())
                    .body(new ComplaintErrorResponse(new ComplaintError(
                        String.valueOf(response.getStatusCodeValue()), 
                        "Failed to create ticket with attachments: " + response.getBody())));
            }
        } catch (Exception e) {
            logger.error("Error creating Freshservice ticket with attachments", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ComplaintErrorResponse(new ComplaintError("500", e.getMessage())));
        } finally {
            // Clean up temporary files
            if (tempFiles != null) {
                FreshserviceFileUtils.cleanupTempFiles(tempFiles);
            }
        }
    }
    
    /**
     * Retrieves a ticket from Freshdesk by its ID
     * 
     * @param ticketId The ID of the ticket to retrieve
     * @return ResponseEntity with the ticket details or error response
     */
    public ResponseEntity<Object> getTicketById(Long ticketId) {
        try {
            logger.info("Retrieving Freshdesk ticket with ID: {}", ticketId);
            
            // Create the URL for the GET request
            String url = apiUrl + "/tickets/" + ticketId;
            logger.info("GET request URL: {}", url);
            
            // Set up headers with authentication
            HttpHeaders headers = createHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            
            // Create the HTTP entity with headers
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            // Make the GET request to Freshdesk API
            ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                entity,
                String.class
            );
            
            logger.info("Freshdesk API response status: {}", response.getStatusCode());
            
            // Parse the response
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                try {
                    // Log the raw response for debugging
                    logger.info("Raw response body: {}", response.getBody());
                    
                    // Check if the response is wrapped in a 'ticket' field
                    String responseBody = response.getBody();
                    if (responseBody.contains("\"ticket\":")) {
                        // Extract the ticket object from the wrapper
                        ObjectMapper mapper = new ObjectMapper();
                        JsonNode rootNode = mapper.readTree(responseBody);
                        JsonNode ticketNode = rootNode.get("ticket");
                        if (ticketNode != null) {
                            responseBody = ticketNode.toString();
                            logger.info("Extracted ticket node from response: {}", responseBody);
                        }
                    }
                    
                    // Parse the response body to get the ticket details
                    FreshserviceTicketResponse ticketResponse = objectMapper.readValue(
                        responseBody, 
                        FreshserviceTicketResponse.class
                    );
                    
                    if (ticketResponse != null && ticketResponse.getId() != null) {
                        logger.info("Successfully retrieved ticket with ID: {}", ticketId);
                        return ResponseEntity.ok(ticketResponse);
                    } else {
                        logger.warn("Retrieved empty or null ticket response for ID: {}", ticketId);
                        // Return the raw response for debugging purposes
                        return ResponseEntity.ok()
                            .body("Raw API response: " + response.getBody());
                    }
                } catch (Exception e) {
                    logger.error("Error parsing Freshdesk ticket response: {}", e.getMessage(), e);
                    // Return the raw response for debugging purposes
                    return ResponseEntity.ok()
                        .body("Error parsing response. Raw API response: " + response.getBody());
                }
            } else {
                logger.error("Failed to retrieve ticket. Status: {}, Response: {}", 
                    response.getStatusCode(), response.getBody());
                return ResponseEntity.status(response.getStatusCode())
                    .body(new ComplaintErrorResponse(new ComplaintError(
                        String.valueOf(response.getStatusCodeValue()), 
                        "Failed to retrieve ticket: " + response.getBody())));
            }
        } catch (Exception e) {
            logger.error("Error retrieving Freshdesk ticket: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ComplaintErrorResponse(new ComplaintError("500", 
                    "Error retrieving Freshdesk ticket: " + e.getMessage())));
        }
    }
    
    /**
     * Retrieves attachments for a ticket from Freshservice
     * 
     * @param ticketId The ID of the ticket to retrieve attachments for
     * @return ResponseEntity with the attachments or error response
     */
    @Operation(
        summary = "Get attachments for a ticket", 
        description = "Retrieve attachments for a ticket from Freshservice",
        responses = {
            @ApiResponse(responseCode = "200", description = "Attachments retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Bad request"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
        }
    )
    public ResponseEntity<Object> getTicketAttachments(@Parameter(description = "The ID of the ticket to retrieve attachments for") Long ticketId) {
        try {
            logger.info("Retrieving attachments for Freshservice ticket with ID: {}", ticketId);
            
            // Create the URL for the GET request to retrieve ticket conversations
            String url = apiUrl + "/tickets/" + ticketId + "/conversations";
            logger.info("GET request URL for conversations: {}", url);
            
            // Set up headers with authentication
            HttpHeaders headers = createHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            
            // Create the HTTP entity with headers
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            // Make the GET request to Freshservice API
            ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                entity,
                String.class
            );
            
            logger.info("Freshservice API response status for conversations: {}", response.getStatusCode());
            
            // Parse the response to extract attachments
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                try {
                    // Log the raw response for debugging
                    logger.debug("Raw conversations response: {}", response.getBody());
                    
                    // Parse the response to get the conversations
                    JsonNode rootNode = objectMapper.readTree(response.getBody());
                    JsonNode conversationsNode = rootNode.get("conversations");
                    
                    if (conversationsNode != null && conversationsNode.isArray() && conversationsNode.size() > 0) {
                        // Create a list to hold all attachments
                        List<FreshserviceAttachmentResponse> allAttachments = new ArrayList<>();
                        
                        // Iterate through conversations to find attachments
                        for (JsonNode conversation : conversationsNode) {
                            JsonNode attachmentsNode = conversation.get("attachments");
                            if (attachmentsNode != null && attachmentsNode.isArray() && !attachmentsNode.isEmpty()) {
                                // Convert attachments from this conversation to FreshserviceAttachmentResponse objects
                                for (JsonNode attachment : attachmentsNode) {
                                    FreshserviceAttachmentResponse attachmentResponse = objectMapper.treeToValue(
                                        attachment, FreshserviceAttachmentResponse.class);
                                    allAttachments.add(attachmentResponse);
                                }
                            }
                        }
                        
                        if (!allAttachments.isEmpty()) {
                            // Create a wrapper with all attachments
                            FreshserviceAttachmentResponseWrapper wrapper = new FreshserviceAttachmentResponseWrapper(allAttachments);
                            
                            logger.info("Retrieved {} attachments for ticket ID: {}", allAttachments.size(), ticketId);
                            return ResponseEntity.ok(wrapper);
                        } else {
                            logger.info("No attachments found in conversations for ticket ID: {}", ticketId);
                            return ResponseEntity.ok(new FreshserviceAttachmentResponseWrapper(Collections.emptyList()));
                        }
                    } else {
                        logger.info("No conversations found for ticket ID: {}", ticketId);
                        return ResponseEntity.ok(new FreshserviceAttachmentResponseWrapper(Collections.emptyList()));
                    }
                } catch (Exception e) {
                    logger.error("Error parsing Freshservice conversations response: {}", e.getMessage(), e);
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new ComplaintErrorResponse(new ComplaintError("500", 
                            "Error parsing Freshservice conversations response: " + e.getMessage())));
                }
            } else {
                logger.error("Failed to retrieve conversations. Status: {}, Response: {}", 
                    response.getStatusCode(), response.getBody());
                return ResponseEntity.status(response.getStatusCode())
                    .body(new ComplaintErrorResponse(new ComplaintError(
                        String.valueOf(response.getStatusCodeValue()), 
                        "Failed to retrieve conversations: " + response.getBody())));
            }
        } catch (Exception e) {
            logger.error("Error retrieving Freshservice ticket attachments: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ComplaintErrorResponse(new ComplaintError("500", 
                    "Error retrieving Freshservice ticket attachments: " + e.getMessage())));
        }
    }
    
    /**
     * Downloads an attachment from Freshservice by its ID
     * 
     * @param attachmentId The ID of the attachment to download
     * @return ResponseEntity with the attachment content or error response
     */
    @Operation(
        summary = "Download an attachment", 
        description = "Download an attachment from Freshservice by its ID",
        responses = {
            @ApiResponse(responseCode = "200", description = "Attachment downloaded successfully"),
            @ApiResponse(responseCode = "400", description = "Bad request"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
        }
    )
    public ResponseEntity<Object> downloadAttachment(@Parameter(description = "The ID of the attachment to download") Long attachmentId) {
        try {
            logger.info("Downloading attachment with ID: {}", attachmentId);
            
            // Create the URL for the GET request
            String url = apiUrl + "/attachments/" + attachmentId;
            logger.info("GET request URL for attachment: {}", url);
            
            // Set up headers with authentication
            HttpHeaders headers = createHeaders();
            
            // Create the HTTP entity with headers
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            // Make the GET request to Freshservice API
            ResponseEntity<byte[]> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                entity,
                byte[].class
            );
            
            logger.info("Freshservice API response status for attachment download: {}", response.getStatusCode());
            
            // Check if the download was successful
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                // Get content type from response headers
                HttpHeaders responseHeaders = new HttpHeaders();
                String contentType = response.getHeaders().getContentType() != null 
                    ? response.getHeaders().getContentType().toString() 
                    : MediaType.APPLICATION_OCTET_STREAM_VALUE;
                responseHeaders.setContentType(MediaType.parseMediaType(contentType));
                
                // Get filename from Content-Disposition header if available
                String filename = "attachment_" + attachmentId;
                if (response.getHeaders().getContentDisposition() != null) {
                    String contentDisposition = response.getHeaders().getContentDisposition().toString();
                    if (contentDisposition.contains("filename=")) {
                        filename = contentDisposition.substring(
                            contentDisposition.indexOf("filename=") + 9).replace("\"", "");
                    }
                }

                // Set Content-Disposition header
                responseHeaders.setContentDisposition(
                    ContentDisposition.builder("attachment")
                        .filename(filename)
                        .build());
                
                // Set Content-Length header
                responseHeaders.setContentLength(response.getBody().length);
                
                logger.info("Successfully downloaded attachment with ID: {}, size: {} bytes", 
                    attachmentId, response.getBody().length);
                
                return new ResponseEntity<>(response.getBody(), responseHeaders, HttpStatus.OK);
            } else {
                logger.error("Failed to download attachment. Status: {}", response.getStatusCode());
                return ResponseEntity.status(response.getStatusCode())
                    .body(new ComplaintErrorResponse(new ComplaintError(
                        String.valueOf(response.getStatusCodeValue()), 
                        "Failed to download attachment")));
            }
        } catch (Exception e) {
            logger.error("Error downloading attachment: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ComplaintErrorResponse(new ComplaintError("500", 
                    "Error downloading attachment: " + e.getMessage())));
        }
    }
    
    /**
     * Get the Freshservice API URL
     * 
     * @return The API URL
     */
    public String getApiUrl() {
        return apiUrl;
    }

    /**
     * Get the Freshservice API key
     * 
     * @return The API key
     */
    public String getApiKey() {
        return apiKey;
    }
    
    /**
     * Gets conversations for a ticket from Freshservice
     * 
     * @param ticketId The ID of the ticket to retrieve conversations for
     * @return ResponseEntity with the conversations or error response
     */
    @Operation(
        summary = "Get conversations for a ticket", 
        description = "Retrieve conversations for a ticket from Freshservice",
        responses = {
            @ApiResponse(responseCode = "200", description = "Conversations retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Bad request"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
        }
    )
    public ResponseEntity<String> getTicketConversations(@Parameter(description = "The ID of the ticket to retrieve conversations for") Long ticketId) {
        try {
            logger.info("Retrieving conversations for Freshservice ticket with ID: {}", ticketId);
            
            // Create the URL for the GET request to retrieve ticket conversations
            String url = apiUrl + "/tickets/" + ticketId + "/conversations";
            logger.info("GET request URL for conversations: {}", url);
            
            // Set up headers with authentication
            HttpHeaders headers = createHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            
            // Create the HTTP entity with headers
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            // Make the GET request to Freshservice API
            ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                entity,
                String.class
            );
            
            logger.info("Freshservice API response status for conversations: {}", response.getStatusCode());
            
            // Return the raw response
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                logger.info("Successfully retrieved conversations for ticket ID: {}", ticketId);
                // Log the raw response for debugging
                logger.debug("Raw conversations response: {}", response.getBody());
                return ResponseEntity.ok(response.getBody());
            } else {
                logger.error("Failed to retrieve conversations. Status: {}, Response: {}", 
                    response.getStatusCode(), response.getBody());
                return ResponseEntity.status(response.getStatusCode()).body(response.getBody());
            }
        } catch (Exception e) {
            logger.error("Error getting ticket conversations: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error getting ticket conversations: " + e.getMessage());
        }
    }
    
    /**
     * Validates the attachments against size and extension constraints
     * 
     * @param attachments List of attachments to validate
     * @throws IllegalArgumentException if any attachment is invalid
     */
    private void validateAttachments(List<MultipartFile> attachments) {
        if (attachments == null || attachments.isEmpty()) {
            throw new IllegalArgumentException("No attachments provided");
        }
        
        for (MultipartFile file : attachments) {
            // Check file size
            if (file.getSize() > maxAttachmentSize) {
                throw new IllegalArgumentException(
                    "File '" + file.getOriginalFilename() + "' exceeds maximum size of " + 
                    (maxAttachmentSize / (1024 * 1024)) + " MB"
                );
            }
            
            // Check file extension
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.isEmpty()) {
                throw new IllegalArgumentException("File name cannot be empty");
            }
            
            if (!originalFilename.contains(".")) {
                throw new IllegalArgumentException(
                    "File '" + originalFilename + "' has no extension. Allowed extensions: " + 
                    allowedExtensions
                );
            }
            
            // Use the FreshserviceFileUtils.isExtensionAllowed method for consistent validation
            if (!FreshserviceFileUtils.isExtensionAllowed(originalFilename, allowedExtensions)) {
                String extension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();
                throw new IllegalArgumentException(
                    "File extension '" + extension + "' is not allowed. Allowed extensions: " + 
                    allowedExtensions
                );
            }
        }
        
        logger.info("All attachments validated successfully");
    }
    
    /**
     * Creates HTTP headers with authentication for Freshservice API
     * 
     * @return HttpHeaders with authentication
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        String auth = apiKey + ":X";
        byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes());
        String authHeader = "Basic " + new String(encodedAuth);
        headers.set("Authorization", authHeader);
        return headers;
    }
    
    /**
     * Wrapper method to create a ticket in Freshservice
     * Simplified interface for other services to create tickets
     * 
     * @param ticketRequest The ticket request object
     * @return The created ticket response or null if creation failed
     */
    public FreshserviceTicketResponse createTicketSimple(FreshserviceTicketRequest ticketRequest) {
        try {
            logger.info("Creating ticket in Freshservice (simple wrapper)");
            
            ResponseEntity<Object> response = createTicket(ticketRequest);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() instanceof FreshserviceTicketResponse) {
                FreshserviceTicketResponse ticketResponse = (FreshserviceTicketResponse) response.getBody();
                logger.info("Successfully created ticket with ID: {}", ticketResponse.getId());
                return ticketResponse;
            } else {
                logger.error("Failed to create ticket. Response status: {}", response.getStatusCode());
                return null;
            }
        } catch (Exception e) {
            logger.error("Error creating ticket: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Wrapper method to add attachments to an existing ticket in Freshservice
     * Simplified interface for other services to add attachments
     * 
     * @param ticketId The ID of the ticket to add attachments to
     * @param attachments List of file attachments
     * @return True if attachments were added successfully, false otherwise
     */
    public boolean addAttachmentsToTicketSimple(Long ticketId, List<MultipartFile> attachments) {
        try {
            logger.info("Adding {} attachments to ticket ID: {} (simple wrapper)", attachments.size(), ticketId);
            
            ResponseEntity<Object> response = addAttachmentsToTicket(ticketId, attachments);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                logger.info("Successfully added attachments to ticket ID: {}", ticketId);
                return true;
            } else {
                logger.error("Failed to add attachments to ticket. Response status: {}", response.getStatusCode());
                return false;
            }
        } catch (Exception e) {
            logger.error("Error adding attachments to ticket: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Wrapper method to create a ticket with attachments using a two-step process
     * 1. Create the ticket first
     * 2. Then add attachments to the created ticket
     * Simplified interface for other services to create tickets with attachments
     * 
     * @param ticketRequest The ticket request object
     * @param attachments List of file attachments
     * @return The created ticket response or null if creation failed
     */
    public FreshserviceTicketResponse createTicketWithAttachmentsSimple(
            FreshserviceTicketRequest ticketRequest, 
            List<MultipartFile> attachments) {
        try {
            // Step 1: Create the ticket
            FreshserviceTicketResponse ticketResponse = createTicketSimple(ticketRequest);
            
            if (ticketResponse == null || ticketResponse.getId() == null) {
                logger.error("Failed to create ticket");
                return null;
            }
            
            // Step 2: Add attachments to the created ticket
            if (attachments != null && !attachments.isEmpty()) {
                boolean attachmentsAdded = addAttachmentsToTicketSimple(ticketResponse.getId(), attachments);
                
                if (!attachmentsAdded) {
                    logger.warn("Created ticket with ID: {} but failed to add attachments", ticketResponse.getId());
                } else {
                    logger.info("Successfully created ticket with ID: {} and added {} attachments", 
                               ticketResponse.getId(), attachments.size());
                }
            }
            
            return ticketResponse;
        } catch (Exception e) {
            logger.error("Error creating ticket with attachments: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Retrieves multiple tickets from Freshservice by their IDs
     * 
     * @param ticketIds List of ticket IDs to retrieve
     * @return ResponseEntity with the list of tickets or error response
     */
    @Operation(
        summary = "Get multiple tickets by IDs", 
        description = "Retrieve multiple tickets from Freshservice by their IDs",
        responses = {
            @ApiResponse(responseCode = "200", description = "Tickets retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Bad request"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
        }
    )
    public ResponseEntity<Object> getTicketsByIds(@Parameter(description = "List of ticket IDs to retrieve") List<Long> ticketIds) {
        if (ticketIds == null || ticketIds.isEmpty()) {
            logger.warn("No ticket IDs provided");
            return ResponseEntity.badRequest().body(new ComplaintErrorResponse(
                new ComplaintError("400", "No ticket IDs provided")));
        }
        
        try {
            logger.info("Retrieving {} tickets from Freshservice", ticketIds.size());
            
            List<FreshserviceTicketResponse> tickets = new ArrayList<>();
            List<Long> failedTicketIds = new ArrayList<>();
            
            // Retrieve each ticket individually
            for (Long ticketId : ticketIds) {
                ResponseEntity<Object> response = getTicketById(ticketId);
                
                if (response.getStatusCode().is2xxSuccessful() && response.getBody() instanceof FreshserviceTicketResponse) {
                    tickets.add((FreshserviceTicketResponse) response.getBody());
                } else {
                    logger.warn("Failed to retrieve ticket with ID: {}", ticketId);
                    failedTicketIds.add(ticketId);
                }
            }
            
            // Log results
            logger.info("Successfully retrieved {} tickets, failed to retrieve {} tickets", 
                tickets.size(), failedTicketIds.size());
            
            // Create response with retrieved tickets and any failed IDs
            Map<String, Object> result = new HashMap<>();
            result.put("tickets", tickets);
            
            if (!failedTicketIds.isEmpty()) {
                result.put("failedTicketIds", failedTicketIds);
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("Error retrieving Freshservice tickets: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ComplaintErrorResponse(new ComplaintError("500", 
                    "Error retrieving Freshservice tickets: " + e.getMessage())));
        }
    }


    /**
     * Sanitizes custom fields by removing known problematic fields
     * Call this method before sending the request to the API
     */
    public void sanitizeCustomFields(Map customFields) {
        if (customFields != null) {
            // Remove known problematic fields
            customFields.remove("msf_closing_code");
            customFields.remove("lf_what_solution_is_having_an_issue");

            // If custom fields map is empty after removing problematic fields, set it to null
            if (customFields.isEmpty()) {
                customFields = null;
            }
        }
    }



}
