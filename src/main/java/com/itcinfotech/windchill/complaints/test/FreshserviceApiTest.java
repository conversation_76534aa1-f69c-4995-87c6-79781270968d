package com.itcinfotech.windchill.complaints.test;

import com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketResponse;
import com.itcinfotech.windchill.complaints.service.freshservice.FreshserviceTicketExample;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.File;
import java.io.FileInputStream;
import java.nio.file.Files;

/**
 * Test class for Freshservice API
 * This class is no longer configured to run automatically at startup
 * To use it, inject it into another component or controller
 */
// @Configuration - removed to prevent automatic execution at startup
public class FreshserviceApiTest {
    
    private static final Logger logger = LoggerFactory.getLogger(FreshserviceApiTest.class);
    
    @Autowired
    private FreshserviceTicketExample freshserviceTicketExample;
    
    // @Bean - removed to prevent automatic execution at startup
    public CommandLineRunner testFreshserviceApi() {
        return args -> {
            logger.info("Starting Freshservice API test...");
            
            // Test creating a ticket without attachment first
            try {
                logger.info("Creating ticket without attachment...");
                
                // Create a ticket using the example service
                FreshserviceTicketResponse response = freshserviceTicketExample.createExampleTicket();
                
                if (response != null) {
                    logger.info("Successfully created ticket with ID: {}", response.getId());
                    logger.info("Ticket details: {}", response);
                    
                    // Now let's create a test zip file for attachment testing
                    createTestZipFile();
                    
                    logger.info("Test files created. To test with attachments, use the following endpoint:");
                    logger.info("POST http://localhost:8080/api/v1/freshservice/test/create-ticket-with-attachments-single-call");
                    logger.info("with a multipart form containing an 'attachments' field with the test_attachment.zip file");
                } else {
                    logger.error("Failed to create ticket");
                }
            } catch (Exception e) {
                logger.error("Error in Freshservice API test: {}", e.getMessage(), e);
            }
        };
    }
    
    /**
     * Creates a test zip file if it doesn't exist
     */
    private void createTestZipFile() throws Exception {
        // Create test_files directory if it doesn't exist
        File testFilesDir = new File("test_files");
        if (!testFilesDir.exists()) {
            testFilesDir.mkdir();
        }
        
        // Create test.txt file if it doesn't exist
        File testTxtFile = new File("test_files/test.txt");
        if (!testTxtFile.exists()) {
            Files.write(testTxtFile.toPath(), "This is a test file for Freshservice attachment upload".getBytes());
        }
        
        // Create test_attachment.zip file if it doesn't exist
        File testZipFile = new File("test_files/test_attachment.zip");
        if (!testZipFile.exists()) {
            // Use Java's built-in ZIP functionality to create a zip file
            java.util.zip.ZipOutputStream zipOut = new java.util.zip.ZipOutputStream(Files.newOutputStream(testZipFile.toPath()));
            FileInputStream fis = new FileInputStream(testTxtFile);
            java.util.zip.ZipEntry zipEntry = new java.util.zip.ZipEntry(testTxtFile.getName());
            zipOut.putNextEntry(zipEntry);
            
            byte[] bytes = new byte[1024];
            int length;
            while ((length = fis.read(bytes)) >= 0) {
                zipOut.write(bytes, 0, length);
            }
            
            zipOut.closeEntry();
            fis.close();
            zipOut.close();
            
            logger.info("Created test zip file at: {}", testZipFile.getAbsolutePath());
        } else {
            logger.info("Test zip file already exists at: {}", testZipFile.getAbsolutePath());
        }
    }
}
