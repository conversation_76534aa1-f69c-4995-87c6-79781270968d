package com.itcinfotech.windchill.complaints.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Utility class for handling file operations related to Freshservice attachments
 */
public class FreshserviceFileUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(FreshserviceFileUtils.class);
    
    /**
     * Converts MultipartFile objects to temporary File objects
     * 
     * @param multipartFiles List of MultipartFile objects
     * @param tempDir Directory to store temporary files
     * @return List of File objects
     * @throws IOException If there's an error creating temporary files
     */
    public static List<File> convertMultipartFilesToFiles(List<MultipartFile> multipartFiles, String tempDir) throws IOException {
        List<File> files = new ArrayList<>();
        
        // Create temp directory if it doesn't exist
        Path tempDirPath = Paths.get(tempDir);
        if (!Files.exists(tempDirPath)) {
            Files.createDirectories(tempDirPath);
        }
        
        for (MultipartFile multipartFile : multipartFiles) {
            String originalFilename = multipartFile.getOriginalFilename();
            if (originalFilename == null) {
                originalFilename = UUID.randomUUID().toString();
            }
            
            // Create a temporary file with a unique name to avoid conflicts
            File file = new File(tempDir + File.separator + UUID.randomUUID() + "_" + originalFilename);
            multipartFile.transferTo(file);
            files.add(file);
            
            // Register the file for deletion when the JVM exits
            file.deleteOnExit();
        }
        
        return files;
    }
    
    /**
     * Gets the MIME type of a file
     * 
     * @param file The file to get the MIME type for
     * @return The MIME type of the file
     */
    public static String getMimeType(File file) {
        try {
            Path path = file.toPath();
            String mimeType = Files.probeContentType(path);
            return mimeType != null ? mimeType : "application/octet-stream";
        } catch (IOException e) {
            logger.warn("Could not determine MIME type for file: {}", file.getName(), e);
            return "application/octet-stream";
        }
    }
    
    /**
     * Validates if a file extension is allowed
     * 
     * @param filename The filename to check
     * @param allowedExtensions Comma-separated list of allowed extensions
     * @return true if the extension is allowed, false otherwise
     */
    public static boolean isExtensionAllowed(String filename, String allowedExtensions) {
        if (filename == null || filename.isEmpty() || !filename.contains(".")) {
            return false;
        }
        
        // Get the extension with the dot
        String extension = filename.substring(filename.lastIndexOf(".")).toLowerCase();
        String[] extensions = allowedExtensions.split(",");
        
        for (String ext : extensions) {
            ext = ext.trim();
            if (extension.equals(ext)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Cleans up temporary files
     * 
     * @param files List of files to delete
     */
    public static void cleanupTempFiles(List<File> files) {
        if (files == null) {
            return;
        }
        
        for (File file : files) {
            if (file.exists()) {
                boolean deleted = file.delete();
                if (!deleted) {
                    logger.warn("Failed to delete temporary file: {}", file.getAbsolutePath());
                }
            }
        }
    }
}
