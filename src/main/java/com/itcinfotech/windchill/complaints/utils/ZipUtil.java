package com.itcinfotech.windchill.complaints.utils;

import com.itcinfotech.windchill.complaints.service.ComplaintService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

@Component
public class ZipUtil {


    @Autowired
    ComplaintService complaintService;


    public void unzip(String zipFilePath, String destDirectory) throws IOException {
        File zipFile = new File(zipFilePath);

        // Validate input parameters
        if (!zipFile.exists()) {
            throw new IOException("ZIP file does not exist: " + zipFilePath);
        }

        if (!zipFile.canRead()) {
            throw new IOException("Cannot read ZIP file: " + zipFilePath);
        }

        String zipFileName = zipFile.getName().replace(".zip", "");
        File destDir = new File(destDirectory + File.separator + zipFileName);

        // Create destination directory
        if (!destDir.exists() && !destDir.mkdirs()) {
            throw new IOException("Failed to create destination directory: " + destDir.getAbsolutePath());
        }

        // Extract ZIP file with proper error handling
        try (ZipInputStream zipIn = new ZipInputStream(new FileInputStream(zipFilePath))) {
            ZipEntry entry;
            int extractedFiles = 0;
            final int MAX_FILES = 1000; // Prevent zip bomb attacks

            while ((entry = zipIn.getNextEntry()) != null) {
                // Prevent zip bomb attacks
                if (++extractedFiles > MAX_FILES) {
                    throw new IOException("ZIP file contains too many entries (max: " + MAX_FILES + ")");
                }

                // Validate entry name to prevent directory traversal attacks
                String entryName = entry.getName();
                if (entryName.contains("..") || entryName.startsWith("/")) {
                    throw new IOException("Invalid entry name in ZIP: " + entryName);
                }

                File file = new File(destDir, entryName);

                // Ensure the file is within the destination directory
                if (!file.getCanonicalPath().startsWith(destDir.getCanonicalPath())) {
                    throw new IOException("Entry is outside of the target directory: " + entryName);
                }

                if (entry.isDirectory()) {
                    if (!file.exists() && !file.mkdirs()) {
                        throw new IOException("Failed to create directory: " + file.getAbsolutePath());
                    }
                } else {
                    // Create parent directories if they don't exist
                    File parentDir = file.getParentFile();
                    if (parentDir != null && !parentDir.exists() && !parentDir.mkdirs()) {
                        throw new IOException("Failed to create parent directory: " + parentDir.getAbsolutePath());
                    }

                    // Extract file with size limit to prevent zip bomb
                    try (BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(file))) {
                        byte[] bytesIn = new byte[4096];
                        int read;
                        long totalSize = 0;
                        final long MAX_SIZE = 100 * 1024 * 1024; // 100MB limit per file

                        while ((read = zipIn.read(bytesIn)) != -1) {
                            totalSize += read;
                            if (totalSize > MAX_SIZE) {
                                throw new IOException("File too large in ZIP: " + entryName + " (max: " + MAX_SIZE + " bytes)");
                            }
                            bos.write(bytesIn, 0, read);
                        }
                    }
                }
                zipIn.closeEntry();
            }
        } catch (IOException e) {
            // Clean up partially extracted files on error
            try {
                deleteDirectoryRecursively(destDir);
            } catch (Exception cleanupException) {
                // Log cleanup error but don't mask the original error
                System.err.println("Failed to cleanup after extraction error: " + cleanupException.getMessage());
            }
            throw e;
        }

        // Process the extracted CSV file
        File csvFile = new File(destDir, "data.csv");
        if (!csvFile.exists()) {
            throw new IOException("Required data.csv file not found in ZIP: " + zipFilePath);
        }

        try {
            complaintService.extractDataFromCsv(csvFile, destDir);
        } catch (Exception e) {
            throw new IOException("Error processing CSV file: " + e.getMessage(), e);
        }
    }

    /**
     * Recursively delete a directory and all its contents
     */
    private void deleteDirectoryRecursively(File directory) throws IOException {
        if (directory == null || !directory.exists()) {
            return;
        }

        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteDirectoryRecursively(file);
                }
            }
        }

        if (!directory.delete()) {
            throw new IOException("Failed to delete: " + directory.getAbsolutePath());
        }
    }



}
