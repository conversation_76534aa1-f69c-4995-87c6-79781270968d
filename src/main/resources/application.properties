spring.application.name=windchill.complaints
spring.main.banner-mode=console

#Configurations for fetching the bearer token
windchill.bearer.token.url=https://mozarc-int-pingfed-runtime.cloud.thingworx.com/as/token.oauth2
windchill.client.id=DxP_Mozarc_Microservices
windchill.client.secret=172763e904d5f5f0844ff2a91fb11b48894356df6a8d25d34644936c2c80fd2d
windchill.grant.type=client_credentials
windchill.scope=WINDCHILL

#Authentication toggle: basic or bearer
windchill.auth.type=bearer

#Credentials for basic auth
windchill.basic.auth.user=wcadmin
windchill.basic.auth.password=wcadmin

camel.files.root=C:/temp
camel.files.integrationRoot.dir = c:/temp/IntegrationRoot

#Configurations for Azure Blob Storage
azure.localDownloadDir=/path/to/download
azure.endpoint=https://ptccustomermozaue1.blob.core.windows.net/moza01-ue1wmi-esi?sv=2023-08-03&si=moza01-ue1wmi-esi-RITM0255203&sr=c&sig=b%2F37kEnyqLjp2MjTjLPF%2FCe1m0yZOLWTkCHfiD7Jx0g%3D
azure.erpFilesDir=/path/to/erp/files
azure.container=your-container-name

#windchill.host=https://PP-2501231811W4.portal.ptc.io:8443/
windchill.host=https://mozarc-int.ptcmscloud.com/
windchill.token.url=Windchill/oauth/servlet/odata/PTC/GetCSRFToken()
windchill.complaint.create.url=Windchill/oauth/servlet/odata/v7/CEM/CustomerExperiences
windchill.container.url = Windchill/oauth/servlet/odata/v7/DataAdmin/Containers
windchill.part.url = Windchill/oauth/servlet/odata/v7/ProdMgmt/Parts
windchill.manufacturing.url = Windchill/oauth/servlet/odata/v7/QMS/PeopleOrPlaces
windchill.person.url = Windchill/oauth/servlet/odata/v7/QMS/People
windchill.upload.stage1.url=Windchill/oauth/servlet/odata/v7/CEM/CustomerExperiences
windchill.upload.stage3.url=Windchill/oauth/servlet/odata/v7/CEM/CustomerExperiences

#Freshservice API Configuration to create ticket when complaint creation errors out,
# upload the zip archive as well to the ticket.
#freshservice.api.url=https://paaapu.freshdesk.com/api/v2
#freshservice.api.key=********************
freshservice.api.url=https://mozarcmedicalservicedesk-november-test.freshservice.com/api/v2
freshservice.api.key=f9fMMB0qZuiLwj7IaEoT
freshservice.attachments.max.size=15728640
freshservice.attachments.allowed.extensions=.jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.txt,.zip

logging.level.org.springframework.web.reactive.function.client.ExchangeFunctions=DEBUG

#Enable HTTP compression to reduce the REST API payload size
server.compression.enabled=true
server.compression.mime-types=application/json,application/xml,text/html,text/plain,text/csv
server.compression.min-response-size=1024