<!DOCTYPE html>
<html>
<head>
    <title>Freshservice Ticket with Attachments</title>
    <script>
        function createTicket() {
            const ticketData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                subject: document.getElementById('subject').value,
                description: document.getElementById('description').value,
                status: 2,
                priority: 1,
                source: 2
            };
            
            const formData = new FormData();
            
            // Add ticket data as JSON string in a part named "ticket"
            formData.append('ticket', new Blob([JSON.stringify(ticketData)], {
                type: 'application/json'
            }));
            
            // Add attachments
            const fileInput = document.getElementById('attachments');
            for (let i = 0; i < fileInput.files.length; i++) {
                formData.append('attachments', fileInput.files[i]);
            }
            
            // Send the request
            fetch('/api/v1/freshservice/tickets/with-attachments', {
                method: 'POST',
                body: formData
                // No Content-Type header - browser will set it with boundary
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('result').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                document.getElementById('result').textContent = 'Error: ' + error;
            });
        }
        
        function addAttachments() {
            const ticketId = document.getElementById('ticketId').value;
            const formData = new FormData();
            
            // Add attachments
            const fileInput = document.getElementById('ticketAttachments');
            for (let i = 0; i < fileInput.files.length; i++) {
                formData.append('attachments', fileInput.files[i]);
            }
            
            // Send the request
            fetch(`/api/v1/freshservice/tickets/${ticketId}/attachments`, {
                method: 'POST',
                body: formData
                // No Content-Type header - browser will set it with boundary
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('attachResult').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                document.getElementById('attachResult').textContent = 'Error: ' + error;
            });
        }
    </script>
</head>
<body>
    <h1>Create Freshservice Ticket with Attachments</h1>
    <div>
        <label for="name">Name:</label>
        <input type="text" id="name" value="Test User"><br>
        
        <label for="email">Email:</label>
        <input type="email" id="email" value="<EMAIL>"><br>
        
        <label for="subject">Subject:</label>
        <input type="text" id="subject" value="Test Ticket"><br>
        
        <label for="description">Description:</label>
        <textarea id="description">This is a test ticket with attachments.</textarea><br>
        
        <label for="attachments">Attachments:</label>
        <input type="file" id="attachments" multiple><br>
        
        <button onclick="createTicket()">Create Ticket</button>
    </div>
    
    <h3>Result:</h3>
    <pre id="result"></pre>
    
    <h1>Add Attachments to Existing Ticket</h1>
    <div>
        <label for="ticketId">Ticket ID:</label>
        <input type="number" id="ticketId"><br>
        
        <label for="ticketAttachments">Attachments:</label>
        <input type="file" id="ticketAttachments" multiple><br>
        
        <button onclick="addAttachments()">Add Attachments</button>
    </div>
    
    <h3>Result:</h3>
    <pre id="attachResult"></pre>
</body>
</html>