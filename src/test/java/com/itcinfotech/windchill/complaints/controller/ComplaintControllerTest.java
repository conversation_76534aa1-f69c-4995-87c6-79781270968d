package com.itcinfotech.windchill.complaints.controller;

import com.itcinfotech.windchill.complaints.service.ComplaintService;
import com.itcinfotech.windchill.complaints.service.CsrfTokenService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComplaintControllerTest {

    @Mock
    private ComplaintService complaintService;

    @Mock
    private CsrfTokenService csrfTokenService;

    private ComplaintController complaintController;

    @BeforeEach
    void setUp() {
        complaintController = new ComplaintController(complaintService, csrfTokenService);
    }

    @Test
    void testGetCsrfTokenSuccess() {
        // Given
        String expectedToken = "test-csrf-token-12345";
        when(csrfTokenService.getCsrfToken()).thenReturn(expectedToken);

        // When
        ResponseEntity<Object> response = complaintController.getCsrfToken();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        
        ComplaintController.CsrfTokenResponse tokenResponse = 
            (ComplaintController.CsrfTokenResponse) response.getBody();
        
        assertEquals(expectedToken, tokenResponse.getCsrfToken());
        assertTrue(tokenResponse.getTimestamp() > 0);
        assertTrue(tokenResponse.getTimestamp() <= System.currentTimeMillis());
    }

    @Test
    void testGetCsrfTokenError() {
        // Given
        String errorMessage = "Connection failed to Windchill server";
        when(csrfTokenService.getCsrfToken()).thenThrow(new RuntimeException(errorMessage));

        // When
        ResponseEntity<Object> response = complaintController.getCsrfToken();

        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        
        // Verify error response structure
        assertTrue(response.getBody().toString().contains("Error retrieving CSRF token"));
        assertTrue(response.getBody().toString().contains(errorMessage));
    }

    @Test
    void testCsrfTokenResponseStructure() {
        // Given
        String testToken = "test-token";
        long beforeTimestamp = System.currentTimeMillis();

        // When
        ComplaintController.CsrfTokenResponse response = 
            new ComplaintController.CsrfTokenResponse(testToken);
        
        long afterTimestamp = System.currentTimeMillis();

        // Then
        assertEquals(testToken, response.getCsrfToken());
        assertTrue(response.getTimestamp() >= beforeTimestamp);
        assertTrue(response.getTimestamp() <= afterTimestamp);
    }
}
