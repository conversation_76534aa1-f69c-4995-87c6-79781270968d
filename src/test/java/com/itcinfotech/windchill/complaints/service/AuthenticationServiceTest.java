package com.itcinfotech.windchill.complaints.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AuthenticationServiceTest {

    @Mock
    private BearerTokenService bearerTokenService;

    private AuthenticationService authenticationService;

    @BeforeEach
    void setUp() {
        authenticationService = new AuthenticationService(bearerTokenService);
    }

    @Test
    void testBasicAuthenticationDefault() {
        // Given
        ReflectionTestUtils.setField(authenticationService, "authType", "basic");
        ReflectionTestUtils.setField(authenticationService, "basicAuthUser", "testuser");
        ReflectionTestUtils.setField(authenticationService, "basicAuthPassword", "testpass");

        // When
        String authHeader = authenticationService.getAuthorizationHeader();

        // Then
        assertTrue(authHeader.startsWith("Basic "));
        assertTrue(authenticationService.isBasicAuth());
        assertFalse(authenticationService.isBearerTokenAuth());
        assertEquals("basic", authenticationService.getAuthType());
    }

    @Test
    void testBearerTokenAuthentication() {
        // Given
        ReflectionTestUtils.setField(authenticationService, "authType", "bearer");
        when(bearerTokenService.getBearerToken()).thenReturn("test-bearer-token");

        // When
        String authHeader = authenticationService.getAuthorizationHeader();

        // Then
        assertEquals("Bearer test-bearer-token", authHeader);
        assertFalse(authenticationService.isBasicAuth());
        assertTrue(authenticationService.isBearerTokenAuth());
        assertEquals("bearer", authenticationService.getAuthType());
    }

    @Test
    void testNullAuthTypeDefaultsToBasic() {
        // Given
        ReflectionTestUtils.setField(authenticationService, "authType", null);
        ReflectionTestUtils.setField(authenticationService, "basicAuthUser", "testuser");
        ReflectionTestUtils.setField(authenticationService, "basicAuthPassword", "testpass");

        // When & Then
        assertTrue(authenticationService.isBasicAuth());
        assertFalse(authenticationService.isBearerTokenAuth());
        assertEquals("basic", authenticationService.getAuthType());
    }

    @Test
    void testEmptyAuthTypeDefaultsToBasic() {
        // Given
        ReflectionTestUtils.setField(authenticationService, "authType", "");
        ReflectionTestUtils.setField(authenticationService, "basicAuthUser", "testuser");
        ReflectionTestUtils.setField(authenticationService, "basicAuthPassword", "testpass");

        // When & Then
        assertTrue(authenticationService.isBasicAuth());
        assertFalse(authenticationService.isBearerTokenAuth());
        assertEquals("basic", authenticationService.getAuthType());
    }

    @Test
    void testCaseInsensitiveAuthType() {
        // Given - Test uppercase
        ReflectionTestUtils.setField(authenticationService, "authType", "BEARER");
        when(bearerTokenService.getBearerToken()).thenReturn("test-token");

        // When & Then
        assertTrue(authenticationService.isBearerTokenAuth());
        assertFalse(authenticationService.isBasicAuth());

        // Given - Test mixed case
        ReflectionTestUtils.setField(authenticationService, "authType", "Basic");

        // When & Then
        assertTrue(authenticationService.isBasicAuth());
        assertFalse(authenticationService.isBearerTokenAuth());
    }
}
