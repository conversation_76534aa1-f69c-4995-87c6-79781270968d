package com.itcinfotech.windchill.complaints.service.freshservice;

import com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketRequest;
import com.itcinfotech.windchill.complaints.dto.freshservice.FreshserviceTicketResponse;
import com.itcinfotech.windchill.complaints.request.FreshserviceTicketCreateRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest
public class FreshserviceTicketServiceTest {

    @Autowired
    private FreshserviceTicketService freshserviceTicketService;

    @Autowired
    private FreshserviceTicketExample freshserviceTicketExample;
    
    @Value("${freshservice.api.url}")
    private String apiUrl;
    
    @BeforeEach
    public void setup() {
        System.out.println("Using API URL: " + apiUrl);
    }

    @Test
    public void testCreateTicketWithAttachmentsInSingleCall() throws IOException {
        // Create a test attachment with an extension that matches the format in the allowed list
        MockMultipartFile testFile = new MockMultipartFile(
            "attachment",
            "test-attachment.jpg",
            "image/jpeg",
            "This is a test attachment file.".getBytes(StandardCharsets.UTF_8)
        );

        List<MultipartFile> attachments = new ArrayList<>();
        attachments.add(testFile);

        // Create a ticket request with unique identifiers
        String timestamp = String.valueOf(System.currentTimeMillis());

        // Create custom fields map
        Map<String, Object> customFields = new HashMap<>();
        customFields.put("lf_what_solution_is_having_an_issue", 33000031228L);

        // Create a new ticket request with updated subject and description
        // Since FreshserviceTicketRequest is a record (immutable), we need to create a new instance
        FreshserviceTicketRequest ticketRequest = new FreshserviceTicketRequest(
            "Test User",
            "<EMAIL>",
            "Test Ticket with Attachment - " + timestamp,
            "This is a test ticket created from automated test at " + timestamp,
            2, // Open status
            1, // High priority
            2, // Email source
            null, // department_id
            "Applications",
            "Troubleshooting",
            "Incident",
            1, // impact
            1, // urgency
            customFields
        );
        
        System.out.println("Test file name: " + testFile.getOriginalFilename());
        System.out.println("Test file content type: " + testFile.getContentType());
        System.out.println("Test file size: " + testFile.getSize());
        
        // Create a request with ticket data and attachments
        FreshserviceTicketCreateRequest request = new FreshserviceTicketCreateRequest(ticketRequest, attachments);
        
        System.out.println("Expected API endpoint URL: " + apiUrl + "/tickets");
        
        try {
            // Call the service to create the ticket with attachments in a single call
            ResponseEntity<Object> response = freshserviceTicketService.createTicketWithAttachmentsInSingleCall(request);
            
            // Verify the response
            assertNotNull(response);
            System.out.println("Response status: " + response.getStatusCode());
            System.out.println("Response body: " + response.getBody());
            
            assertTrue(response.getStatusCode().is2xxSuccessful(), 
                    "Expected successful status code but got: " + response.getStatusCode());
            
            if (response.getBody() instanceof FreshserviceTicketResponse) {
                FreshserviceTicketResponse ticketResponse = (FreshserviceTicketResponse) response.getBody();
                assertNotNull(ticketResponse.getId(), "Ticket ID should not be null");
                System.out.println("Successfully created ticket with ID: " + ticketResponse.getId());
                System.out.println("Ticket subject: " + ticketResponse.getSubject());
            } else {
                System.out.println("Response body is not a FreshserviceTicketResponse: " + 
                        (response.getBody() != null ? response.getBody().getClass().getName() : "null"));
                throw new AssertionError("Response body is not a FreshserviceTicketResponse");
            }
        } catch (Exception e) {
            System.err.println("Exception during test: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
}
