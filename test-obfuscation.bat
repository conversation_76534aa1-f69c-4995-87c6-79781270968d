@echo off
REM Test script to verify yGuard obfuscation
REM This script compares original and obfuscated JARs

echo ========================================
echo yGuard Obfuscation Test Script
echo ========================================
echo.

REM Check if JARs exist
if not exist "target\windchill.complaints-0.0.1-SNAPSHOT.jar" (
    echo ERROR: Original JAR not found
    echo Please run 'build-obfuscated.bat' first
    pause
    exit /b 1
)

if not exist "target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar" (
    echo ERROR: Obfuscated JAR not found
    echo Please run 'build-obfuscated.bat' first
    pause
    exit /b 1
)

echo Step 1: Comparing JAR contents...
echo.

echo Original JAR classes:
echo ----------------------------------------
jar -tf target\windchill.complaints-0.0.1-SNAPSHOT.jar | findstr "com/itcinfotech" | findstr "\.class" | head -20
echo.

echo Obfuscated JAR classes:
echo ----------------------------------------
jar -tf target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar | findstr "com/itcinfotech" | findstr "\.class" | head -20
echo.

echo Step 2: Checking file sizes...
echo ----------------------------------------
for %%I in ("target\windchill.complaints-0.0.1-SNAPSHOT.jar") do echo Original JAR size:    %%~zI bytes
for %%I in ("target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar") do echo Obfuscated JAR size:  %%~zI bytes
echo.

echo Step 3: Testing obfuscated JAR startup...
echo ----------------------------------------
echo Starting obfuscated application (will stop after 10 seconds)...
timeout /t 2 /nobreak >nul

REM Start the application in background and kill it after 10 seconds
start /b java -jar target\windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar --server.port=8081 >nul 2>&1
timeout /t 10 /nobreak >nul

REM Kill Java processes (be careful - this kills ALL Java processes)
echo Stopping test application...
taskkill /f /im java.exe >nul 2>&1

echo.
echo Step 4: Checking obfuscation log...
echo ----------------------------------------
if exist "target\yguard-log.xml" (
    echo yGuard log file found - checking for obfuscated classes...
    findstr /i "class.*->" target\yguard-log.xml | head -10
    echo.
    echo Full log available at: target\yguard-log.xml
) else (
    echo WARNING: yGuard log file not found
)

echo.
echo ========================================
echo Test Summary
echo ========================================
echo ✅ JAR comparison completed
echo ✅ Size comparison completed
echo ✅ Startup test completed
echo ✅ Log analysis completed
echo.
echo Manual verification steps:
echo 1. Compare class names in both JARs above
echo 2. Check that obfuscated classes have shorter/scrambled names
echo 3. Verify the application started successfully
echo 4. Review the yGuard log for detailed obfuscation mapping
echo.

pause
