<?xml version="1.0" encoding="UTF-8"?>
<!-- yGuard Configuration for Spring Boot Windchill Complaints Microservice -->
<!-- This configuration preserves Spring Boot functionality while obfuscating business logic -->

<project name="yguard-obfuscation" default="obfuscate" basedir=".">
    
    <property name="input.jar" value="target/windchill.complaints-0.0.1-SNAPSHOT.jar"/>
    <property name="output.jar" value="target/windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar"/>
    <property name="log.file" value="target/yguard-obfuscation.xml"/>
    
    <target name="obfuscate">
        <taskdef name="yguard" classname="com.yworks.yguard.YGuardTask"/>
        
        <yguard>
            <inoutpair in="${input.jar}" out="${output.jar}"/>
            
            <!-- Rename configuration with comprehensive Spring Boot support -->
            <rename logfile="${log.file}" 
                    replaceClassNameStrings="true" 
                    conserveManifest="true"
                    mainclass="com.itcinfotech.windchill.complaints.WindchillComplaintsApplication">
                
                <!-- ========== KEEP RULES - Classes that should NOT be obfuscated ========== -->
                
                <!-- Keep main application class -->
                <keep>
                    <class classes="public" methods="public" fields="public">
                        <patternset>
                            <include name="com.itcinfotech.windchill.complaints.WindchillComplaintsApplication"/>
                        </patternset>
                    </class>
                </keep>
                
                <!-- Keep all Spring Boot configuration classes -->
                <keep>
                    <class classes="public" methods="public" fields="public">
                        <patternset>
                            <include name="com.itcinfotech.windchill.complaints.config.**"/>
                        </patternset>
                    </class>
                </keep>
                
                <!-- Keep REST Controllers and their public methods -->
                <keep>
                    <class classes="public" methods="public" fields="public">
                        <patternset>
                            <include name="com.itcinfotech.windchill.complaints.controller.**"/>
                        </patternset>
                    </class>
                </keep>
                
                <!-- Keep Service interfaces and public methods -->
                <keep>
                    <class classes="public" methods="public" fields="public">
                        <patternset>
                            <include name="com.itcinfotech.windchill.complaints.service.**"/>
                        </patternset>
                    </class>
                </keep>
                
                <!-- Keep DTOs, Request/Response classes for JSON serialization -->
                <keep>
                    <class classes="public" methods="public" fields="public">
                        <patternset>
                            <include name="com.itcinfotech.windchill.complaints.dto.**"/>
                            <include name="com.itcinfotech.windchill.complaints.request.**"/>
                            <include name="com.itcinfotech.windchill.complaints.response.**"/>
                        </patternset>
                    </class>
                </keep>
                
                <!-- Keep Exception classes -->
                <keep>
                    <class classes="public" methods="public" fields="public">
                        <patternset>
                            <include name="com.itcinfotech.windchill.complaints.exception.**"/>
                        </patternset>
                    </class>
                </keep>
                
                <!-- Keep Camel Route classes -->
                <keep>
                    <class classes="public" methods="public" fields="public">
                        <patternset>
                            <include name="com.itcinfotech.windchill.complaints.camel.**"/>
                        </patternset>
                    </class>
                </keep>
                
                <!-- Keep classes with Spring annotations -->
                <keep>
                    <class classes="public" methods="public" fields="public">
                        <patternset>
                            <include name="**"/>
                        </patternset>
                        <attribute name="org.springframework.stereotype.Component"/>
                        <attribute name="org.springframework.stereotype.Service"/>
                        <attribute name="org.springframework.stereotype.Repository"/>
                        <attribute name="org.springframework.web.bind.annotation.RestController"/>
                        <attribute name="org.springframework.context.annotation.Configuration"/>
                        <attribute name="org.springframework.boot.autoconfigure.SpringBootApplication"/>
                        <attribute name="org.springframework.context.annotation.Bean"/>
                    </class>
                </keep>
                
                <!-- Keep classes with Lombok annotations -->
                <keep>
                    <class classes="public" methods="public" fields="public">
                        <patternset>
                            <include name="**"/>
                        </patternset>
                        <attribute name="lombok.Data"/>
                        <attribute name="lombok.Getter"/>
                        <attribute name="lombok.Setter"/>
                        <attribute name="lombok.Builder"/>
                        <attribute name="lombok.AllArgsConstructor"/>
                        <attribute name="lombok.NoArgsConstructor"/>
                        <attribute name="lombok.RequiredArgsConstructor"/>
                    </class>
                </keep>
                
                <!-- Keep classes with Jackson annotations -->
                <keep>
                    <class classes="public" methods="public" fields="public">
                        <patternset>
                            <include name="**"/>
                        </patternset>
                        <attribute name="com.fasterxml.jackson.annotation.JsonProperty"/>
                        <attribute name="com.fasterxml.jackson.annotation.JsonIgnore"/>
                        <attribute name="com.fasterxml.jackson.annotation.JsonFormat"/>
                    </class>
                </keep>
                
                <!-- Keep classes with Swagger/OpenAPI annotations -->
                <keep>
                    <class classes="public" methods="public" fields="public">
                        <patternset>
                            <include name="**"/>
                        </patternset>
                        <attribute name="io.swagger.v3.oas.annotations.Operation"/>
                        <attribute name="io.swagger.v3.oas.annotations.responses.ApiResponse"/>
                        <attribute name="io.swagger.v3.oas.annotations.responses.ApiResponses"/>
                    </class>
                </keep>
                
                <!-- Keep serialization methods -->
                <keep>
                    <method class="**" name="writeObject"/>
                    <method class="**" name="readObject"/>
                    <method class="**" name="writeReplace"/>
                    <method class="**" name="readResolve"/>
                </keep>
                
                <!-- Keep enum methods -->
                <keep>
                    <method class="**" name="values"/>
                    <method class="**" name="valueOf"/>
                </keep>
                
                <!-- ========== ADJUST RULES - Selective obfuscation ========== -->
                
                <!-- Obfuscate private methods and fields in utility classes -->
                <adjust replaceContent="true">
                    <class classes="private,protected" methods="private,protected" fields="private,protected">
                        <patternset>
                            <include name="com.itcinfotech.windchill.complaints.utils.**"/>
                        </patternset>
                    </class>
                </adjust>
                
                <!-- Obfuscate private implementation details in service classes -->
                <adjust replaceContent="true">
                    <class classes="private" methods="private" fields="private">
                        <patternset>
                            <include name="com.itcinfotech.windchill.complaints.service.impl.**"/>
                        </patternset>
                    </class>
                </adjust>
                
            </rename>
        </yguard>
        
        <echo message=""/>
        <echo message="=== yGuard Obfuscation Completed Successfully! ==="/>
        <echo message="Original JAR: ${input.jar}"/>
        <echo message="Obfuscated JAR: ${output.jar}"/>
        <echo message="Log file: ${log.file}"/>
        <echo message=""/>
    </target>
    
</project>
