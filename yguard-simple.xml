<?xml version="1.0" encoding="UTF-8"?>
<!-- Simple yGuard configuration for testing -->
<project name="yguard-simple" default="obfuscate" basedir=".">
    
    <property name="yguard.jar" value="${user.home}/.m2/repository/com/yworks/yguard/4.1.1/yguard-4.1.1.jar"/>
    <property name="input.jar" value="target/windchill.complaints-0.0.1-SNAPSHOT.jar"/>
    <property name="output.jar" value="target/windchill.complaints-0.0.1-SNAPSHOT-obfuscated.jar"/>
    <property name="log.file" value="target/yguard-simple.xml"/>
    
    <target name="check-files">
        <available file="${input.jar}" property="input.jar.exists"/>
        <fail unless="input.jar.exists" message="Input JAR not found: ${input.jar}"/>
        
        <available file="${yguard.jar}" property="yguard.jar.exists"/>
        <fail unless="yguard.jar.exists" message="yGuard JAR not found: ${yguard.jar}"/>
    </target>
    
    <target name="obfuscate" depends="check-files">
        <taskdef name="yguard" classname="com.yworks.yguard.YGuardTask" classpath="${yguard.jar}"/>
        
        <echo message="========================================"/>
        <echo message="Starting yGuard obfuscation"/>
        <echo message="========================================"/>
        <echo message="Input JAR: ${input.jar}"/>
        <echo message="Output JAR: ${output.jar}"/>
        <echo message="yGuard JAR: ${yguard.jar}"/>
        <echo message=""/>
        
        <yguard>
            <inoutpair in="${input.jar}" out="${output.jar}"/>
            
            <rename logfile="${log.file}"
                    replaceClassNameStrings="true"
                    conserveManifest="true">

                <!-- Single keep element with multiple class elements -->
                <keep>
                    <!-- Keep Spring Boot launcher classes -->
                    <class classes="public" methods="public" fields="public">
                        <patternset>
                            <include name="org.springframework.boot.loader.**"/>
                        </patternset>
                    </class>

                    <!-- Keep Spring framework classes -->
                    <class classes="public" methods="public" fields="public">
                        <patternset>
                            <include name="org.springframework.**"/>
                        </patternset>
                    </class>

                    <!-- Keep main application class -->
                    <class classes="public" methods="public" fields="public">
                        <patternset>
                            <include name="com.itcinfotech.windchill.complaints.WindchillComplaintsApplication"/>
                        </patternset>
                    </class>

                    <!-- Keep all public classes and methods (minimal obfuscation) -->
                    <class classes="public" methods="public" fields="public">
                        <patternset>
                            <include name="com.itcinfotech.windchill.complaints.**"/>
                        </patternset>
                    </class>


                </keep>

            </rename>
        </yguard>
        
        <echo message=""/>
        <echo message="========================================"/>
        <echo message="yGuard obfuscation completed!"/>
        <echo message="========================================"/>
        <echo message="Original JAR: ${input.jar}"/>
        <echo message="Obfuscated JAR: ${output.jar}"/>
        <echo message="Log file: ${log.file}"/>
        
        <!-- Show file sizes -->
        <length file="${input.jar}" property="input.size"/>
        <length file="${output.jar}" property="output.size"/>
        <echo message="Original size: ${input.size} bytes"/>
        <echo message="Obfuscated size: ${output.size} bytes"/>
    </target>
    
</project>
